@echo off
echo 启动WaferAligner - 生产环境模式
echo ================================

REM 清除开发模式环境变量（如果存在）
set WAFER_ALIGNER_DEV_MODE=

REM 显示当前环境配置
echo 环境配置:
echo - 开发模式: 关闭
echo - 硬件连接: 启用
echo - 需要硬件: 汇川PLC + 串口设备
echo.

REM 检查硬件连接提醒
echo 请确认以下硬件已连接:
echo [1] 汇川PLC设备已上电 (IP: ************, ************)
echo [2] 串口设备已连接 (默认: COM1, 115200波特率)
echo [3] 网络连接正常
echo.

REM 询问是否继续
set /p continue="确认硬件已连接，继续启动? (Y/N): "
if /i "%continue%" neq "Y" (
    echo 启动已取消
    pause
    exit /b
)

REM 启动程序
echo.
echo 正在启动程序...
start "" "WaferAligner.exe"

REM 保持窗口打开，显示环境信息
echo.
echo 程序已启动，当前为生产模式
echo 如需切换到开发模式，请使用"开发环境启动.bat"
echo.
pause
