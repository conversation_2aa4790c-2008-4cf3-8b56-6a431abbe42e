using System;
using System.Threading.Tasks;

namespace WaferAligner.Services
{
    /// <summary>
    /// 气缸控制服务接口
    /// 负责所有气缸相关操作的统一管理
    /// </summary>
    public interface ICylinderService
    {
        /// <summary>
        /// 控制气缸状态
        /// </summary>
        /// <param name="cylinderType">气缸类型（TOPWAFER/TRAYWAFERINNER/TRAYWAFERUTER/TRAY/CHUCKLOCK/HORIZONTALADJUST）</param>
        /// <param name="targetState">目标状态(0-关闭,1-打开)</param>
        /// <returns>操作是否成功</returns>
        Task<bool> ControlCylinderAsync(string cylinderType, int targetState);
        
        /// <summary>
        /// 获取气缸当前状态
        /// </summary>
        /// <param name="cylinderType">气缸类型（TOPWAFER/TRAYWAFERINNER/TRAYWAFERUTER/TRAY/CHUCKLOCK/HORIZONTALADJUST）</param>
        /// <returns>气缸状态(0-关闭,1-打开)</returns>
        int GetCylinderState(string cylinderType);
        
        /// <summary>
        /// 等待气缸达到指定状态
        /// </summary>
        /// <param name="cylinderType">气缸类型（TOPWAFER/TRAYWAFERINNER/TRAYWAFERUTER/TRAY/CHUCKLOCK/HORIZONTALADJUST）</param>
        /// <param name="expectedState">期望状态(0-关闭,1-打开)</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>是否在超时前达到期望状态</returns>
        Task<bool> WaitForCylinderStateAsync(string cylinderType, int expectedState, TimeSpan timeout);
        
        /// <summary>
        /// 检查所有气缸是否就绪
        /// </summary>
        /// <returns>所有气缸是否就绪</returns>
        Task<bool> CheckAllCylindersReadyAsync();
        
        /// <summary>
        /// 获取气缸名称列表
        /// </summary>
        /// <returns>所有支持的气缸名称</returns>
        string[] GetSupportedCylinders();
    }
} 