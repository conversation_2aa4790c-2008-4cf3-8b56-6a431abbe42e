﻿namespace WaferAligner
{
    partial class AxisControl
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // 在释放前先清理资源
                try
                {
                    // 避免重复清理
                    if (!_isCleaningUp)
                    {
                        //_loggingService?.LogDebug($"AxisControl {_id} disposing, calling CleanUp", WaferAligner.EventIds.EventIds.Resource_Released);
                        CleanUp();
                    }
                }
                catch (Exception)
                {
                    // 忽略所有异常，确保Dispose过程继续
                    //_loggingService?.LogError(ex, $"Dispose error: {ex.Message}", WaferAligner.EventIds.EventIds.Resource_Released);
                }
                
                try
                {
                    if (components != null)
                    {
                        components.Dispose();
                    }
                }
                catch (Exception)
                {
                    // 忽略组件释放过程中的异常
                }
            }
            
            try
            {
                base.Dispose(disposing);
            }
            catch (Exception)
            {
                // 忽略基类Dispose过程中的异常
            }
        }

        #region 组件设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            tableLayoutPanel4 = new TableLayoutPanel();
            panel10 = new Panel();
            TxtTargetPos = new Sunny.UI.UITextBox();
            panel36 = new Panel();
            BtnZero = new Button();
            panel41 = new Panel();
            LalZCurenttPos = new Label();
            panel43 = new Panel();
            LaltZTargetPos = new Label();
            panel44 = new Panel();
            LalZTargetVel = new Label();
            panel45 = new Panel();
            TxtTargetVel = new Sunny.UI.UITextBox();
            panel46 = new Panel();
            BtnZPos = new Button();
            panel47 = new Panel();
            BtnZPause = new Button();
            panel48 = new Panel();
            panel49 = new Panel();
            LalZPosComplete = new Label();
            panel50 = new Panel();
            LED_PosComplete = new MyLED();
            panel51 = new Panel();
            LalZJogVel = new Label();
            panel52 = new Panel();
            BtnZFront = new Button();
            panel53 = new Panel();
            BtnZBack = new Button();
            panel54 = new Panel();
            LalZPosLimit = new Label();
            panel55 = new Panel();
            TxtPosLimit = new Sunny.UI.UITextBox();
            panel56 = new Panel();
            TxtNegLimit = new Sunny.UI.UITextBox();
            panel57 = new Panel();
            LalZNegLimit = new Label();
            panel58 = new Panel();
            lalZTextName = new Label();
            button2 = new Button();
            button1 = new Button();
            panel59 = new Panel();
            lalZZero = new Label();
            panel60 = new Panel();
            LED_Zero = new MyLED();
            panel61 = new Panel();
            TxtCurentPos = new Sunny.UI.UITextBox();
            panel62 = new Panel();
            LalCurentPosUnit = new Label();
            panel63 = new Panel();
            LalTargetPosUnit = new Label();
            panel42 = new Panel();
            LalZCurenttVel = new Label();
            panel64 = new Panel();
            TxtCurentVel = new Sunny.UI.UITextBox();
            panel65 = new Panel();
            LalCurentVelUnit = new Label();
            panel66 = new Panel();
            LalTargetVelUnit = new Label();
            panel67 = new Panel();
            panel68 = new Panel();
            TxtJogVel = new Sunny.UI.UITextBox();
            panel69 = new Panel();
            LalJogVelUnit = new Label();
            panel70 = new Panel();
            LalPosLimitUnit = new Label();
            panel71 = new Panel();
            LalNegLimitUnit = new Label();
            panel72 = new Panel();
            panel9 = new Panel();
            LED_Alarm = new MyLED();
            LalZAlarm = new Label();
            LED_Enable = new MyLED();
            LalZEnable = new Label();
            LED_Ready = new MyLED();
            LalZReady = new Label();
            tableLayoutPanel4.SuspendLayout();
            panel10.SuspendLayout();
            panel36.SuspendLayout();
            panel41.SuspendLayout();
            panel43.SuspendLayout();
            panel44.SuspendLayout();
            panel45.SuspendLayout();
            panel46.SuspendLayout();
            panel47.SuspendLayout();
            panel49.SuspendLayout();
            panel50.SuspendLayout();
            panel51.SuspendLayout();
            panel52.SuspendLayout();
            panel53.SuspendLayout();
            panel54.SuspendLayout();
            panel55.SuspendLayout();
            panel56.SuspendLayout();
            panel57.SuspendLayout();
            panel58.SuspendLayout();
            panel59.SuspendLayout();
            panel60.SuspendLayout();
            panel61.SuspendLayout();
            panel62.SuspendLayout();
            panel63.SuspendLayout();
            panel42.SuspendLayout();
            panel64.SuspendLayout();
            panel65.SuspendLayout();
            panel66.SuspendLayout();
            panel68.SuspendLayout();
            panel69.SuspendLayout();
            panel70.SuspendLayout();
            panel71.SuspendLayout();
            panel9.SuspendLayout();
            SuspendLayout();
            // 
            // tableLayoutPanel4
            // 
            tableLayoutPanel4.ColumnCount = 6;
            tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20.2593117F));
            tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20.2593212F));
            tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 9.481361F));
            tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20.2593212F));
            tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20.2593212F));
            tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 9.481361F));
            tableLayoutPanel4.Controls.Add(panel10, 1, 3);
            tableLayoutPanel4.Controls.Add(panel36, 3, 1);
            tableLayoutPanel4.Controls.Add(panel41, 0, 2);
            tableLayoutPanel4.Controls.Add(panel43, 0, 3);
            tableLayoutPanel4.Controls.Add(panel44, 3, 3);
            tableLayoutPanel4.Controls.Add(panel45, 4, 3);
            tableLayoutPanel4.Controls.Add(panel46, 0, 4);
            tableLayoutPanel4.Controls.Add(panel47, 1, 4);
            tableLayoutPanel4.Controls.Add(panel48, 2, 4);
            tableLayoutPanel4.Controls.Add(panel49, 3, 4);
            tableLayoutPanel4.Controls.Add(panel50, 4, 4);
            tableLayoutPanel4.Controls.Add(panel51, 0, 5);
            tableLayoutPanel4.Controls.Add(panel52, 3, 5);
            tableLayoutPanel4.Controls.Add(panel53, 4, 5);
            tableLayoutPanel4.Controls.Add(panel54, 0, 6);
            tableLayoutPanel4.Controls.Add(panel55, 1, 6);
            tableLayoutPanel4.Controls.Add(panel56, 4, 6);
            tableLayoutPanel4.Controls.Add(panel57, 3, 6);
            tableLayoutPanel4.Controls.Add(panel58, 0, 0);
            tableLayoutPanel4.Controls.Add(panel59, 4, 1);
            tableLayoutPanel4.Controls.Add(panel60, 5, 1);
            tableLayoutPanel4.Controls.Add(panel61, 1, 2);
            tableLayoutPanel4.Controls.Add(panel62, 2, 2);
            tableLayoutPanel4.Controls.Add(panel63, 2, 3);
            tableLayoutPanel4.Controls.Add(panel42, 3, 2);
            tableLayoutPanel4.Controls.Add(panel64, 4, 2);
            tableLayoutPanel4.Controls.Add(panel65, 5, 2);
            tableLayoutPanel4.Controls.Add(panel66, 5, 3);
            tableLayoutPanel4.Controls.Add(panel67, 5, 4);
            tableLayoutPanel4.Controls.Add(panel68, 1, 5);
            tableLayoutPanel4.Controls.Add(panel69, 2, 5);
            tableLayoutPanel4.Controls.Add(panel70, 2, 6);
            tableLayoutPanel4.Controls.Add(panel71, 5, 6);
            tableLayoutPanel4.Controls.Add(panel72, 5, 5);
            tableLayoutPanel4.Controls.Add(panel9, 0, 1);
            tableLayoutPanel4.Location = new Point(3, 3);
            tableLayoutPanel4.Name = "tableLayoutPanel4";
            tableLayoutPanel4.RowCount = 7;
            tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Percent, 14.3550119F));
            tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Percent, 14.0353346F));
            tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Percent, 14.3242178F));
            tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Percent, 14.3213587F));
            tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Percent, 14.3213587F));
            tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Percent, 14.3213587F));
            tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Percent, 14.3213558F));
            tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            tableLayoutPanel4.Size = new Size(708, 565);
            tableLayoutPanel4.TabIndex = 62;
            // 
            // panel10
            // 
            panel10.Controls.Add(TxtTargetPos);
            panel10.Dock = DockStyle.Fill;
            panel10.Location = new Point(146, 243);
            panel10.Name = "panel10";
            panel10.Size = new Size(137, 74);
            panel10.TabIndex = 15;
            // 
            // TxtTargetPos
            // 
            TxtTargetPos.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtTargetPos.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetPos.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetPos.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtTargetPos.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetPos.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetPos.ButtonSymbolOffset = new Point(0, 0);
            TxtTargetPos.DecimalPlaces = 3;
            TxtTargetPos.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtTargetPos.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTargetPos.Location = new Point(21, 24);
            TxtTargetPos.Margin = new Padding(4, 5, 4, 5);
            TxtTargetPos.MinimumSize = new Size(1, 16);
            TxtTargetPos.Name = "TxtTargetPos";
            TxtTargetPos.Padding = new Padding(5);
            TxtTargetPos.RectColor = Color.FromArgb(140, 140, 140);
            TxtTargetPos.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtTargetPos.ShowText = false;
            TxtTargetPos.Size = new Size(95, 26);
            TxtTargetPos.Style = Sunny.UI.UIStyle.Gray;
            TxtTargetPos.TabIndex = 2;
            TxtTargetPos.Text = "0.000";
            TxtTargetPos.TextAlignment = ContentAlignment.MiddleLeft;
            TxtTargetPos.Type = Sunny.UI.UITextBox.UIEditType.Double;
            TxtTargetPos.Watermark = "";
            TxtTargetPos.KeyUp += TxtTargetPos_KeyUp;
            // 
            // panel36
            // 
            panel36.Controls.Add(BtnZero);
            panel36.Dock = DockStyle.Fill;
            panel36.Location = new Point(356, 84);
            panel36.Name = "panel36";
            panel36.Size = new Size(137, 73);
            panel36.TabIndex = 11;
            // 
            // BtnZero
            // 
            BtnZero.BackColor = Color.FromArgb(224, 224, 224);
            BtnZero.Enabled = false;
            BtnZero.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            BtnZero.Location = new Point(3, 17);
            BtnZero.Name = "BtnZero";
            BtnZero.Size = new Size(131, 39);
            BtnZero.TabIndex = 44;
            BtnZero.Text = "归零";
            BtnZero.UseVisualStyleBackColor = true;
            BtnZero.Click += BtnZZero_Click;
            // 
            // panel41
            // 
            panel41.Controls.Add(LalZCurenttPos);
            panel41.Dock = DockStyle.Fill;
            panel41.Location = new Point(3, 163);
            panel41.Name = "panel41";
            panel41.Size = new Size(137, 74);
            panel41.TabIndex = 34;
            // 
            // LalZCurenttPos
            // 
            LalZCurenttPos.AutoSize = true;
            LalZCurenttPos.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalZCurenttPos.Location = new Point(29, 29);
            LalZCurenttPos.Name = "LalZCurenttPos";
            LalZCurenttPos.Size = new Size(79, 16);
            LalZCurenttPos.TabIndex = 0;
            LalZCurenttPos.Text = "当前位置:";
            // 
            // panel43
            // 
            panel43.Controls.Add(LaltZTargetPos);
            panel43.Dock = DockStyle.Fill;
            panel43.Location = new Point(3, 243);
            panel43.Name = "panel43";
            panel43.Size = new Size(137, 74);
            panel43.TabIndex = 35;
            // 
            // LaltZTargetPos
            // 
            LaltZTargetPos.AutoSize = true;
            LaltZTargetPos.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LaltZTargetPos.Location = new Point(29, 29);
            LaltZTargetPos.Name = "LaltZTargetPos";
            LaltZTargetPos.Size = new Size(79, 16);
            LaltZTargetPos.TabIndex = 47;
            LaltZTargetPos.Text = "目标位置:";
            // 
            // panel44
            // 
            panel44.Controls.Add(LalZTargetVel);
            panel44.Dock = DockStyle.Fill;
            panel44.Location = new Point(356, 243);
            panel44.Name = "panel44";
            panel44.Size = new Size(137, 74);
            panel44.TabIndex = 16;
            // 
            // LalZTargetVel
            // 
            LalZTargetVel.AutoSize = true;
            LalZTargetVel.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalZTargetVel.Location = new Point(29, 29);
            LalZTargetVel.Name = "LalZTargetVel";
            LalZTargetVel.Size = new Size(79, 16);
            LalZTargetVel.TabIndex = 2;
            LalZTargetVel.Text = "目标速度:";
            // 
            // panel45
            // 
            panel45.Controls.Add(TxtTargetVel);
            panel45.Dock = DockStyle.Fill;
            panel45.Location = new Point(499, 243);
            panel45.Name = "panel45";
            panel45.Size = new Size(137, 74);
            panel45.TabIndex = 17;
            // 
            // TxtTargetVel
            // 
            TxtTargetVel.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtTargetVel.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetVel.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetVel.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtTargetVel.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetVel.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetVel.ButtonSymbolOffset = new Point(0, 0);
            TxtTargetVel.DecimalPlaces = 3;
            TxtTargetVel.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtTargetVel.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTargetVel.Location = new Point(21, 24);
            TxtTargetVel.Margin = new Padding(4, 5, 4, 5);
            TxtTargetVel.MinimumSize = new Size(1, 16);
            TxtTargetVel.Name = "TxtTargetVel";
            TxtTargetVel.Padding = new Padding(5);
            TxtTargetVel.RectColor = Color.FromArgb(140, 140, 140);
            TxtTargetVel.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtTargetVel.ShowText = false;
            TxtTargetVel.Size = new Size(95, 26);
            TxtTargetVel.Style = Sunny.UI.UIStyle.Gray;
            TxtTargetVel.TabIndex = 2;
            TxtTargetVel.Text = "0.000";
            TxtTargetVel.TextAlignment = ContentAlignment.MiddleLeft;
            TxtTargetVel.Type = Sunny.UI.UITextBox.UIEditType.Double;
            TxtTargetVel.Watermark = "";
            TxtTargetVel.KeyUp += TxtVel_KeyUp;
            // 
            // panel46
            // 
            panel46.Controls.Add(BtnZPos);
            panel46.Dock = DockStyle.Fill;
            panel46.Location = new Point(3, 323);
            panel46.Name = "panel46";
            panel46.Size = new Size(137, 74);
            panel46.TabIndex = 18;
            // 
            // BtnZPos
            // 
            BtnZPos.BackColor = Color.FromArgb(224, 224, 224);
            BtnZPos.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            BtnZPos.Location = new Point(3, 18);
            BtnZPos.Name = "BtnZPos";
            BtnZPos.Size = new Size(131, 39);
            BtnZPos.TabIndex = 31;
            BtnZPos.Text = "定位";
            BtnZPos.UseVisualStyleBackColor = true;
            BtnZPos.Click += BtnZPos_Click;
            // 
            // panel47
            // 
            panel47.Controls.Add(BtnZPause);
            panel47.Dock = DockStyle.Fill;
            panel47.Location = new Point(146, 323);
            panel47.Name = "panel47";
            panel47.Size = new Size(137, 74);
            panel47.TabIndex = 24;
            // 
            // BtnZPause
            // 
            BtnZPause.BackColor = Color.FromArgb(224, 224, 224);
            BtnZPause.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            BtnZPause.Location = new Point(3, 18);
            BtnZPause.Name = "BtnZPause";
            BtnZPause.Size = new Size(131, 39);
            BtnZPause.TabIndex = 32;
            BtnZPause.Text = "停止";
            BtnZPause.UseVisualStyleBackColor = true;
            BtnZPause.Click += BtnZPause_Click;
            // 
            // panel48
            // 
            panel48.Dock = DockStyle.Fill;
            panel48.Location = new Point(289, 323);
            panel48.Name = "panel48";
            panel48.Size = new Size(61, 74);
            panel48.TabIndex = 19;
            // 
            // panel49
            // 
            panel49.Controls.Add(LalZPosComplete);
            panel49.Dock = DockStyle.Fill;
            panel49.Location = new Point(356, 323);
            panel49.Name = "panel49";
            panel49.Size = new Size(137, 74);
            panel49.TabIndex = 20;
            // 
            // LalZPosComplete
            // 
            LalZPosComplete.AutoSize = true;
            LalZPosComplete.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalZPosComplete.Location = new Point(33, 29);
            LalZPosComplete.Name = "LalZPosComplete";
            LalZPosComplete.Size = new Size(71, 16);
            LalZPosComplete.TabIndex = 48;
            LalZPosComplete.Text = "定位完成\r\n";
            // 
            // panel50
            // 
            panel50.Controls.Add(LED_PosComplete);
            panel50.Dock = DockStyle.Fill;
            panel50.Location = new Point(499, 323);
            panel50.Name = "panel50";
            panel50.Size = new Size(137, 74);
            panel50.TabIndex = 36;
            // 
            // LED_PosComplete
            // 
            LED_PosComplete.BorderColor = Color.White;
            LED_PosComplete.BorderWidth = 2;
            LED_PosComplete.CenterColor = Color.White;
            LED_PosComplete.FlashInterval = 500;
            LED_PosComplete.GapWidth = 1;
            LED_PosComplete.IsBorder = false;
            LED_PosComplete.IsFlash = false;
            LED_PosComplete.IsHighLight = true;
            LED_PosComplete.LampColor = new Color[]
    {
    Color.Green,
    Color.Yellow
    };
            LED_PosComplete.LEDColor = Color.Green;
            LED_PosComplete.LedFalseColor = Color.Gray;
            LED_PosComplete.LedStatus = false;
            LED_PosComplete.LedTrueColor = Color.Green;
            LED_PosComplete.Location = new Point(57, 26);
            LED_PosComplete.Name = "LED_PosComplete";
            LED_PosComplete.Size = new Size(22, 23);
            LED_PosComplete.TabIndex = 56;
            // 
            // panel51
            // 
            panel51.Controls.Add(LalZJogVel);
            panel51.Dock = DockStyle.Fill;
            panel51.Location = new Point(3, 403);
            panel51.Name = "panel51";
            panel51.Size = new Size(137, 74);
            panel51.TabIndex = 21;
            // 
            // LalZJogVel
            // 
            LalZJogVel.AutoSize = true;
            LalZJogVel.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalZJogVel.Location = new Point(33, 29);
            LalZJogVel.Name = "LalZJogVel";
            LalZJogVel.Size = new Size(71, 16);
            LalZJogVel.TabIndex = 1;
            LalZJogVel.Text = "Jog速度:";
            // 
            // panel52
            // 
            panel52.Controls.Add(BtnZFront);
            panel52.Dock = DockStyle.Fill;
            panel52.Location = new Point(356, 403);
            panel52.Name = "panel52";
            panel52.Size = new Size(137, 74);
            panel52.TabIndex = 22;
            // 
            // BtnZFront
            // 
            BtnZFront.BackColor = Color.FromArgb(224, 224, 224);
            BtnZFront.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            BtnZFront.Location = new Point(3, 18);
            BtnZFront.Name = "BtnZFront";
            BtnZFront.Size = new Size(131, 39);
            BtnZFront.TabIndex = 29;
            BtnZFront.Text = "正转";
            BtnZFront.UseVisualStyleBackColor = true;
            BtnZFront.MouseDown += BtnZFront_MouseDown;
            BtnZFront.MouseUp += BtnZFront_MouseUp;
            // 
            // panel53
            // 
            panel53.Controls.Add(BtnZBack);
            panel53.Dock = DockStyle.Fill;
            panel53.Location = new Point(499, 403);
            panel53.Name = "panel53";
            panel53.Size = new Size(137, 74);
            panel53.TabIndex = 23;
            // 
            // BtnZBack
            // 
            BtnZBack.BackColor = Color.FromArgb(224, 224, 224);
            BtnZBack.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            BtnZBack.Location = new Point(3, 18);
            BtnZBack.Name = "BtnZBack";
            BtnZBack.Size = new Size(131, 39);
            BtnZBack.TabIndex = 30;
            BtnZBack.Text = "反转";
            BtnZBack.UseVisualStyleBackColor = true;
            BtnZBack.MouseDown += BtnZBack_MouseDown;
            BtnZBack.MouseUp += BtnZBack_MouseUp;
            // 
            // panel54
            // 
            panel54.Controls.Add(LalZPosLimit);
            panel54.Dock = DockStyle.Fill;
            panel54.Location = new Point(3, 483);
            panel54.Name = "panel54";
            panel54.Size = new Size(137, 79);
            panel54.TabIndex = 27;
            // 
            // LalZPosLimit
            // 
            LalZPosLimit.AutoSize = true;
            LalZPosLimit.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalZPosLimit.Location = new Point(37, 31);
            LalZPosLimit.Name = "LalZPosLimit";
            LalZPosLimit.Size = new Size(63, 16);
            LalZPosLimit.TabIndex = 1;
            LalZPosLimit.Text = "正极限:";
            // 
            // panel55
            // 
            panel55.Controls.Add(TxtPosLimit);
            panel55.Dock = DockStyle.Fill;
            panel55.Location = new Point(146, 483);
            panel55.Name = "panel55";
            panel55.Size = new Size(137, 79);
            panel55.TabIndex = 28;
            // 
            // TxtPosLimit
            // 
            TxtPosLimit.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtPosLimit.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtPosLimit.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtPosLimit.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtPosLimit.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtPosLimit.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtPosLimit.ButtonSymbolOffset = new Point(0, 0);
            TxtPosLimit.DecimalPlaces = 3;
            TxtPosLimit.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtPosLimit.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtPosLimit.Location = new Point(21, 26);
            TxtPosLimit.Margin = new Padding(4, 5, 4, 5);
            TxtPosLimit.MinimumSize = new Size(1, 16);
            TxtPosLimit.Name = "TxtPosLimit";
            TxtPosLimit.Padding = new Padding(5);
            TxtPosLimit.RectColor = Color.FromArgb(140, 140, 140);
            TxtPosLimit.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtPosLimit.ShowText = false;
            TxtPosLimit.Size = new Size(95, 26);
            TxtPosLimit.Style = Sunny.UI.UIStyle.Gray;
            TxtPosLimit.TabIndex = 0;
            TxtPosLimit.Text = "0.000";
            TxtPosLimit.TextAlignment = ContentAlignment.MiddleLeft;
            TxtPosLimit.Type = Sunny.UI.UITextBox.UIEditType.Double;
            TxtPosLimit.Watermark = "";
            TxtPosLimit.KeyUp += TxtLimit_KeyUp;
            // 
            // panel56
            // 
            panel56.Controls.Add(TxtNegLimit);
            panel56.Dock = DockStyle.Fill;
            panel56.Location = new Point(499, 483);
            panel56.Name = "panel56";
            panel56.Size = new Size(137, 79);
            panel56.TabIndex = 30;
            // 
            // TxtNegLimit
            // 
            TxtNegLimit.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtNegLimit.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtNegLimit.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtNegLimit.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtNegLimit.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtNegLimit.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtNegLimit.ButtonSymbolOffset = new Point(0, 0);
            TxtNegLimit.DecimalPlaces = 3;
            TxtNegLimit.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtNegLimit.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtNegLimit.Location = new Point(21, 26);
            TxtNegLimit.Margin = new Padding(4, 5, 4, 5);
            TxtNegLimit.MinimumSize = new Size(1, 16);
            TxtNegLimit.Name = "TxtNegLimit";
            TxtNegLimit.Padding = new Padding(5);
            TxtNegLimit.RectColor = Color.FromArgb(140, 140, 140);
            TxtNegLimit.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtNegLimit.ShowText = false;
            TxtNegLimit.Size = new Size(95, 26);
            TxtNegLimit.Style = Sunny.UI.UIStyle.Gray;
            TxtNegLimit.TabIndex = 0;
            TxtNegLimit.Text = "0.000";
            TxtNegLimit.TextAlignment = ContentAlignment.MiddleLeft;
            TxtNegLimit.Type = Sunny.UI.UITextBox.UIEditType.Double;
            TxtNegLimit.Watermark = "";
            TxtNegLimit.KeyUp += TxtLimit_KeyUp;
            // 
            // panel57
            // 
            panel57.Controls.Add(LalZNegLimit);
            panel57.Dock = DockStyle.Fill;
            panel57.Location = new Point(356, 483);
            panel57.Name = "panel57";
            panel57.Size = new Size(137, 79);
            panel57.TabIndex = 29;
            // 
            // LalZNegLimit
            // 
            LalZNegLimit.AutoSize = true;
            LalZNegLimit.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalZNegLimit.Location = new Point(37, 31);
            LalZNegLimit.Name = "LalZNegLimit";
            LalZNegLimit.Size = new Size(63, 16);
            LalZNegLimit.TabIndex = 2;
            LalZNegLimit.Text = "负极限:";
            // 
            // panel58
            // 
            tableLayoutPanel4.SetColumnSpan(panel58, 6);
            panel58.Controls.Add(lalZTextName);
            panel58.Controls.Add(button2);
            panel58.Controls.Add(button1);
            panel58.Dock = DockStyle.Fill;
            panel58.Location = new Point(3, 3);
            panel58.Name = "panel58";
            panel58.Size = new Size(702, 75);
            panel58.TabIndex = 37;
            // 
            // lalZTextName
            // 
            lalZTextName.AutoSize = true;
            lalZTextName.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            lalZTextName.Location = new Point(39, 24);
            lalZTextName.Name = "lalZTextName";
            lalZTextName.Size = new Size(119, 16);
            lalZTextName.TabIndex = 5;
            lalZTextName.Text = "XX（步进）参数";
            // 
            // button2
            // 
            button2.BackColor = Color.Silver;
            button2.Location = new Point(4, 32);
            button2.Name = "button2";
            button2.Size = new Size(30, 1);
            button2.TabIndex = 4;
            button2.Text = "button2";
            button2.UseVisualStyleBackColor = false;
            // 
            // button1
            // 
            button1.BackColor = Color.Silver;
            button1.Location = new Point(139, 32);
            button1.Name = "button1";
            button1.Size = new Size(550, 1);
            button1.TabIndex = 3;
            button1.Text = "button1";
            button1.UseVisualStyleBackColor = false;
            // 
            // panel59
            // 
            panel59.Controls.Add(lalZZero);
            panel59.Dock = DockStyle.Fill;
            panel59.Location = new Point(499, 84);
            panel59.Name = "panel59";
            panel59.Size = new Size(137, 73);
            panel59.TabIndex = 38;
            // 
            // lalZZero
            // 
            lalZZero.AutoSize = true;
            lalZZero.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            lalZZero.Location = new Point(49, 28);
            lalZZero.Name = "lalZZero";
            lalZZero.Size = new Size(39, 16);
            lalZZero.TabIndex = 50;
            lalZZero.Text = "原点";
            // 
            // panel60
            // 
            panel60.Controls.Add(LED_Zero);
            panel60.Dock = DockStyle.Fill;
            panel60.Location = new Point(642, 84);
            panel60.Name = "panel60";
            panel60.Size = new Size(63, 73);
            panel60.TabIndex = 39;
            // 
            // LED_Zero
            // 
            LED_Zero.BorderColor = Color.White;
            LED_Zero.BorderWidth = 2;
            LED_Zero.CenterColor = Color.White;
            LED_Zero.FlashInterval = 500;
            LED_Zero.GapWidth = 1;
            LED_Zero.IsBorder = false;
            LED_Zero.IsFlash = false;
            LED_Zero.IsHighLight = true;
            LED_Zero.LampColor = new Color[]
    {
    Color.Green,
    Color.Yellow
    };
            LED_Zero.LEDColor = Color.Green;
            LED_Zero.LedFalseColor = Color.Gray;
            LED_Zero.LedStatus = false;
            LED_Zero.LedTrueColor = Color.Green;
            LED_Zero.Location = new Point(20, 25);
            LED_Zero.Name = "LED_Zero";
            LED_Zero.Size = new Size(22, 23);
            LED_Zero.TabIndex = 56;
            // 
            // panel61
            // 
            panel61.Controls.Add(TxtCurentPos);
            panel61.Dock = DockStyle.Fill;
            panel61.Location = new Point(146, 163);
            panel61.Name = "panel61";
            panel61.Size = new Size(137, 74);
            panel61.TabIndex = 40;
            // 
            // TxtCurentPos
            // 
            TxtCurentPos.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtCurentPos.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtCurentPos.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtCurentPos.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtCurentPos.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtCurentPos.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtCurentPos.ButtonSymbolOffset = new Point(0, 0);
            TxtCurentPos.DecimalPlaces = 3;
            TxtCurentPos.Enabled = false;
            TxtCurentPos.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtCurentPos.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCurentPos.Location = new Point(21, 24);
            TxtCurentPos.Margin = new Padding(4, 5, 4, 5);
            TxtCurentPos.MinimumSize = new Size(1, 16);
            TxtCurentPos.Name = "TxtCurentPos";
            TxtCurentPos.Padding = new Padding(5);
            TxtCurentPos.RectColor = Color.FromArgb(140, 140, 140);
            TxtCurentPos.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtCurentPos.ShowText = false;
            TxtCurentPos.Size = new Size(95, 26);
            TxtCurentPos.Style = Sunny.UI.UIStyle.Gray;
            TxtCurentPos.TabIndex = 3;
            TxtCurentPos.Text = "0.000";
            TxtCurentPos.TextAlignment = ContentAlignment.MiddleLeft;
            TxtCurentPos.Type = Sunny.UI.UITextBox.UIEditType.Double;
            TxtCurentPos.Watermark = "";
            // 
            // panel62
            // 
            panel62.Controls.Add(LalCurentPosUnit);
            panel62.Dock = DockStyle.Fill;
            panel62.Location = new Point(289, 163);
            panel62.Name = "panel62";
            panel62.Size = new Size(61, 74);
            panel62.TabIndex = 41;
            // 
            // LalCurentPosUnit
            // 
            LalCurentPosUnit.AutoSize = true;
            LalCurentPosUnit.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalCurentPosUnit.Location = new Point(19, 29);
            LalCurentPosUnit.Name = "LalCurentPosUnit";
            LalCurentPosUnit.Size = new Size(23, 16);
            LalCurentPosUnit.TabIndex = 8;
            LalCurentPosUnit.Text = "mm";
            // 
            // panel63
            // 
            panel63.Controls.Add(LalTargetPosUnit);
            panel63.Dock = DockStyle.Fill;
            panel63.Location = new Point(289, 243);
            panel63.Name = "panel63";
            panel63.Size = new Size(61, 74);
            panel63.TabIndex = 42;
            // 
            // LalTargetPosUnit
            // 
            LalTargetPosUnit.AutoSize = true;
            LalTargetPosUnit.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalTargetPosUnit.Location = new Point(19, 29);
            LalTargetPosUnit.Name = "LalTargetPosUnit";
            LalTargetPosUnit.Size = new Size(23, 16);
            LalTargetPosUnit.TabIndex = 6;
            LalTargetPosUnit.Text = "mm";
            // 
            // panel42
            // 
            panel42.Controls.Add(LalZCurenttVel);
            panel42.Dock = DockStyle.Fill;
            panel42.Location = new Point(356, 163);
            panel42.Name = "panel42";
            panel42.Size = new Size(137, 74);
            panel42.TabIndex = 43;
            // 
            // LalZCurenttVel
            // 
            LalZCurenttVel.AutoSize = true;
            LalZCurenttVel.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalZCurenttVel.Location = new Point(29, 29);
            LalZCurenttVel.Name = "LalZCurenttVel";
            LalZCurenttVel.Size = new Size(79, 16);
            LalZCurenttVel.TabIndex = 2;
            LalZCurenttVel.Text = "当前速度:";
            // 
            // panel64
            // 
            panel64.Controls.Add(TxtCurentVel);
            panel64.Dock = DockStyle.Fill;
            panel64.Location = new Point(499, 163);
            panel64.Name = "panel64";
            panel64.Size = new Size(137, 74);
            panel64.TabIndex = 44;
            // 
            // TxtCurentVel
            // 
            TxtCurentVel.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtCurentVel.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtCurentVel.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtCurentVel.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtCurentVel.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtCurentVel.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtCurentVel.ButtonSymbolOffset = new Point(0, 0);
            TxtCurentVel.DecimalPlaces = 3;
            TxtCurentVel.Enabled = false;
            TxtCurentVel.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtCurentVel.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCurentVel.Location = new Point(21, 24);
            TxtCurentVel.Margin = new Padding(4, 5, 4, 5);
            TxtCurentVel.MinimumSize = new Size(1, 16);
            TxtCurentVel.Name = "TxtCurentVel";
            TxtCurentVel.Padding = new Padding(5);
            TxtCurentVel.RectColor = Color.FromArgb(140, 140, 140);
            TxtCurentVel.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtCurentVel.ShowText = false;
            TxtCurentVel.Size = new Size(95, 26);
            TxtCurentVel.Style = Sunny.UI.UIStyle.Gray;
            TxtCurentVel.TabIndex = 4;
            TxtCurentVel.Text = "0.000";
            TxtCurentVel.TextAlignment = ContentAlignment.MiddleLeft;
            TxtCurentVel.Type = Sunny.UI.UITextBox.UIEditType.Double;
            TxtCurentVel.Watermark = "";
            // 
            // panel65
            // 
            panel65.Controls.Add(LalCurentVelUnit);
            panel65.Dock = DockStyle.Fill;
            panel65.Location = new Point(642, 163);
            panel65.Name = "panel65";
            panel65.Size = new Size(63, 74);
            panel65.TabIndex = 45;
            // 
            // LalCurentVelUnit
            // 
            LalCurentVelUnit.AutoSize = true;
            LalCurentVelUnit.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalCurentVelUnit.Location = new Point(12, 29);
            LalCurentVelUnit.Name = "LalCurentVelUnit";
            LalCurentVelUnit.Size = new Size(39, 16);
            LalCurentVelUnit.TabIndex = 36;
            LalCurentVelUnit.Text = "mm/s";
            // 
            // panel66
            // 
            panel66.Controls.Add(LalTargetVelUnit);
            panel66.Dock = DockStyle.Fill;
            panel66.Location = new Point(642, 243);
            panel66.Name = "panel66";
            panel66.Size = new Size(63, 74);
            panel66.TabIndex = 46;
            // 
            // LalTargetVelUnit
            // 
            LalTargetVelUnit.AutoSize = true;
            LalTargetVelUnit.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalTargetVelUnit.Location = new Point(12, 29);
            LalTargetVelUnit.Name = "LalTargetVelUnit";
            LalTargetVelUnit.Size = new Size(39, 16);
            LalTargetVelUnit.TabIndex = 3;
            LalTargetVelUnit.Text = "mm/s";
            // 
            // panel67
            // 
            panel67.Dock = DockStyle.Fill;
            panel67.Location = new Point(642, 323);
            panel67.Name = "panel67";
            panel67.Size = new Size(63, 74);
            panel67.TabIndex = 47;
            // 
            // panel68
            // 
            panel68.Controls.Add(TxtJogVel);
            panel68.Dock = DockStyle.Fill;
            panel68.Location = new Point(146, 403);
            panel68.Name = "panel68";
            panel68.Size = new Size(137, 74);
            panel68.TabIndex = 48;
            // 
            // TxtJogVel
            // 
            TxtJogVel.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtJogVel.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtJogVel.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtJogVel.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtJogVel.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtJogVel.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtJogVel.ButtonSymbolOffset = new Point(0, 0);
            TxtJogVel.DecimalPlaces = 3;
            TxtJogVel.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtJogVel.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtJogVel.Location = new Point(21, 24);
            TxtJogVel.Margin = new Padding(4, 5, 4, 5);
            TxtJogVel.MinimumSize = new Size(1, 16);
            TxtJogVel.Name = "TxtJogVel";
            TxtJogVel.Padding = new Padding(5);
            TxtJogVel.RectColor = Color.FromArgb(140, 140, 140);
            TxtJogVel.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtJogVel.ShowText = false;
            TxtJogVel.Size = new Size(95, 26);
            TxtJogVel.Style = Sunny.UI.UIStyle.Gray;
            TxtJogVel.TabIndex = 1;
            TxtJogVel.Text = "0.000";
            TxtJogVel.TextAlignment = ContentAlignment.MiddleLeft;
            TxtJogVel.Type = Sunny.UI.UITextBox.UIEditType.Double;
            TxtJogVel.Watermark = "";
            TxtJogVel.KeyUp += TxtVel_KeyUp;
            // 
            // panel69
            // 
            panel69.Controls.Add(LalJogVelUnit);
            panel69.Dock = DockStyle.Fill;
            panel69.Location = new Point(289, 403);
            panel69.Name = "panel69";
            panel69.Size = new Size(61, 74);
            panel69.TabIndex = 49;
            // 
            // LalJogVelUnit
            // 
            LalJogVelUnit.AutoSize = true;
            LalJogVelUnit.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalJogVelUnit.Location = new Point(11, 29);
            LalJogVelUnit.Name = "LalJogVelUnit";
            LalJogVelUnit.Size = new Size(39, 16);
            LalJogVelUnit.TabIndex = 38;
            LalJogVelUnit.Text = "mm/s";
            // 
            // panel70
            // 
            panel70.Controls.Add(LalPosLimitUnit);
            panel70.Dock = DockStyle.Fill;
            panel70.Location = new Point(289, 483);
            panel70.Name = "panel70";
            panel70.Size = new Size(61, 79);
            panel70.TabIndex = 50;
            // 
            // LalPosLimitUnit
            // 
            LalPosLimitUnit.AutoSize = true;
            LalPosLimitUnit.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalPosLimitUnit.Location = new Point(19, 31);
            LalPosLimitUnit.Name = "LalPosLimitUnit";
            LalPosLimitUnit.Size = new Size(23, 16);
            LalPosLimitUnit.TabIndex = 8;
            LalPosLimitUnit.Text = "mm";
            // 
            // panel71
            // 
            panel71.Controls.Add(LalNegLimitUnit);
            panel71.Dock = DockStyle.Fill;
            panel71.Location = new Point(642, 483);
            panel71.Name = "panel71";
            panel71.Size = new Size(63, 79);
            panel71.TabIndex = 51;
            // 
            // LalNegLimitUnit
            // 
            LalNegLimitUnit.AutoSize = true;
            LalNegLimitUnit.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalNegLimitUnit.Location = new Point(20, 31);
            LalNegLimitUnit.Name = "LalNegLimitUnit";
            LalNegLimitUnit.Size = new Size(23, 16);
            LalNegLimitUnit.TabIndex = 10;
            LalNegLimitUnit.Text = "mm";
            // 
            // panel72
            // 
            panel72.Location = new Point(642, 403);
            panel72.Name = "panel72";
            panel72.Size = new Size(63, 74);
            panel72.TabIndex = 52;
            // 
            // panel9
            // 
            tableLayoutPanel4.SetColumnSpan(panel9, 3);
            panel9.Controls.Add(LED_Alarm);
            panel9.Controls.Add(LalZAlarm);
            panel9.Controls.Add(LED_Enable);
            panel9.Controls.Add(LalZEnable);
            panel9.Controls.Add(LED_Ready);
            panel9.Controls.Add(LalZReady);
            panel9.Dock = DockStyle.Fill;
            panel9.Location = new Point(3, 84);
            panel9.Name = "panel9";
            panel9.Size = new Size(347, 73);
            panel9.TabIndex = 0;
            // 
            // LED_Alarm
            // 
            LED_Alarm.BorderColor = Color.White;
            LED_Alarm.BorderWidth = 2;
            LED_Alarm.CenterColor = Color.White;
            LED_Alarm.FlashInterval = 500;
            LED_Alarm.GapWidth = 1;
            LED_Alarm.IsBorder = false;
            LED_Alarm.IsFlash = false;
            LED_Alarm.IsHighLight = true;
            LED_Alarm.LampColor = new Color[]
    {
    Color.Green,
    Color.Yellow
    };
            LED_Alarm.LEDColor = Color.Green;
            LED_Alarm.LedFalseColor = Color.Gray;
            LED_Alarm.LedStatus = false;
            LED_Alarm.LedTrueColor = Color.Red;
            LED_Alarm.Location = new Point(301, 25);
            LED_Alarm.Name = "LED_Alarm";
            LED_Alarm.Size = new Size(22, 23);
            LED_Alarm.TabIndex = 56;
            // 
            // LalZAlarm
            // 
            LalZAlarm.AutoSize = true;
            LalZAlarm.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalZAlarm.Location = new Point(251, 28);
            LalZAlarm.Name = "LalZAlarm";
            LalZAlarm.Size = new Size(39, 16);
            LalZAlarm.TabIndex = 48;
            LalZAlarm.Text = "报警";
            // 
            // LED_Enable
            // 
            LED_Enable.BorderColor = Color.White;
            LED_Enable.BorderWidth = 2;
            LED_Enable.CenterColor = Color.White;
            LED_Enable.FlashInterval = 500;
            LED_Enable.GapWidth = 1;
            LED_Enable.IsBorder = false;
            LED_Enable.IsFlash = false;
            LED_Enable.IsHighLight = true;
            LED_Enable.LampColor = new Color[]
    {
    Color.Green,
    Color.Yellow
    };
            LED_Enable.LEDColor = Color.Green;
            LED_Enable.LedFalseColor = Color.Gray;
            LED_Enable.LedStatus = false;
            LED_Enable.LedTrueColor = Color.Green;
            LED_Enable.Location = new Point(197, 25);
            LED_Enable.Name = "LED_Enable";
            LED_Enable.Size = new Size(22, 23);
            LED_Enable.TabIndex = 56;
            // 
            // LalZEnable
            // 
            LalZEnable.AutoSize = true;
            LalZEnable.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalZEnable.Location = new Point(148, 28);
            LalZEnable.Name = "LalZEnable";
            LalZEnable.Size = new Size(39, 16);
            LalZEnable.TabIndex = 46;
            LalZEnable.Text = "使能";
            // 
            // LED_Ready
            // 
            LED_Ready.BorderColor = Color.White;
            LED_Ready.BorderWidth = 2;
            LED_Ready.CenterColor = Color.White;
            LED_Ready.FlashInterval = 500;
            LED_Ready.GapWidth = 1;
            LED_Ready.IsBorder = false;
            LED_Ready.IsFlash = false;
            LED_Ready.IsHighLight = true;
            LED_Ready.LampColor = new Color[]
    {
    Color.Green,
    Color.Yellow
    };
            LED_Ready.LEDColor = Color.Green;
            LED_Ready.LedFalseColor = Color.Gray;
            LED_Ready.LedStatus = false;
            LED_Ready.LedTrueColor = Color.Green;
            LED_Ready.Location = new Point(94, 25);
            LED_Ready.Name = "LED_Ready";
            LED_Ready.Size = new Size(22, 23);
            LED_Ready.TabIndex = 56;
            // 
            // LalZReady
            // 
            LalZReady.AutoSize = true;
            LalZReady.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalZReady.Location = new Point(29, 28);
            LalZReady.Name = "LalZReady";
            LalZReady.Size = new Size(55, 16);
            LalZReady.TabIndex = 51;
            LalZReady.Text = "准备好";
            // 
            // AxisControl
            // 
            Controls.Add(tableLayoutPanel4);
            Name = "AxisControl";
            Size = new Size(714, 575);
            Load += 步进电机调试_Load;
            tableLayoutPanel4.ResumeLayout(false);
            panel10.ResumeLayout(false);
            panel36.ResumeLayout(false);
            panel41.ResumeLayout(false);
            panel41.PerformLayout();
            panel43.ResumeLayout(false);
            panel43.PerformLayout();
            panel44.ResumeLayout(false);
            panel44.PerformLayout();
            panel45.ResumeLayout(false);
            panel46.ResumeLayout(false);
            panel47.ResumeLayout(false);
            panel49.ResumeLayout(false);
            panel49.PerformLayout();
            panel50.ResumeLayout(false);
            panel51.ResumeLayout(false);
            panel51.PerformLayout();
            panel52.ResumeLayout(false);
            panel53.ResumeLayout(false);
            panel54.ResumeLayout(false);
            panel54.PerformLayout();
            panel55.ResumeLayout(false);
            panel56.ResumeLayout(false);
            panel57.ResumeLayout(false);
            panel57.PerformLayout();
            panel58.ResumeLayout(false);
            panel58.PerformLayout();
            panel59.ResumeLayout(false);
            panel59.PerformLayout();
            panel60.ResumeLayout(false);
            panel61.ResumeLayout(false);
            panel62.ResumeLayout(false);
            panel62.PerformLayout();
            panel63.ResumeLayout(false);
            panel63.PerformLayout();
            panel42.ResumeLayout(false);
            panel42.PerformLayout();
            panel64.ResumeLayout(false);
            panel65.ResumeLayout(false);
            panel65.PerformLayout();
            panel66.ResumeLayout(false);
            panel66.PerformLayout();
            panel68.ResumeLayout(false);
            panel69.ResumeLayout(false);
            panel69.PerformLayout();
            panel70.ResumeLayout(false);
            panel70.PerformLayout();
            panel71.ResumeLayout(false);
            panel71.PerformLayout();
            panel9.ResumeLayout(false);
            panel9.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private TableLayoutPanel tableLayoutPanel4;
        private Panel panel9;
        private Panel panel10;
        private Label LalZReady;
        private Panel panel36;
        public Button BtnZero;
        private Label LalZEnable;
        private Label LalZAlarm;
        private Panel panel41;
        private Label LalZCurenttPos;
        private Panel panel43;
        private Label LaltZTargetPos;
        private Panel panel44;
        private Label LalZTargetVel;
        private Panel panel45;
        private Panel panel46;
        private Button BtnZPos;
        private Panel panel47;
        private Button BtnZPause;
        private Panel panel48;
        private Panel panel49;
        private Label LalZPosComplete;
        private Panel panel50;
        private Panel panel51;
        private Label LalZJogVel;
        private Panel panel52;
        private Button BtnZFront;
        private Panel panel53;
        private Button BtnZBack;
        private Panel panel54;
        private Label LalZPosLimit;
        private Panel panel55;
        private Panel panel56;
        private Panel panel57;
        private Label LalZNegLimit;
        private Panel panel58;
        private Label lalZTextName;
        private Button button2;
        private Button button1;
        private Panel panel59;
        private Label lalZZero;
        private Panel panel60;
        private Panel panel61;
        private Panel panel62;
        private Label LalCurentPosUnit;
        private Panel panel63;
        private Label LalTargetPosUnit;
        private Panel panel42;
        private Label LalZCurenttVel;
        private Panel panel64;
        private Panel panel65;
        private Label LalCurentVelUnit;
        private Panel panel66;
        private Label LalTargetVelUnit;
        private Panel panel67;
        private Panel panel68;
        private Panel panel69;
        private Label LalJogVelUnit;
        private Panel panel70;
        private Label LalPosLimitUnit;
        private Panel panel71;
        private Label LalNegLimitUnit;
        private Panel panel72;
        private MyLED LED_Ready;
        private MyLED LED_Enable;
        private MyLED LED_Alarm;
        private MyLED LED_Zero;
        private MyLED LED_PosComplete;
        private Sunny.UI.UITextBox TxtPosLimit;
        private Sunny.UI.UITextBox TxtNegLimit;
        private Sunny.UI.UITextBox TxtJogVel;
        private Sunny.UI.UITextBox TxtTargetVel;
        private Sunny.UI.UITextBox TxtTargetPos;
        private Sunny.UI.UITextBox TxtCurentPos;
        private Sunny.UI.UITextBox TxtCurentVel;
    }
}
