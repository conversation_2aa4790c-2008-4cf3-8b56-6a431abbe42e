# 日志与事件ID专项合并版

## 一、概述与背景

WaferAligner项目在Phase 3重构计划中，将日志统一确定为优先级最高的任务之一。项目早期存在多种日志记录方式，导致日志行为不一致、格式不统一，增加了维护成本。本专项重构旨在统一日志接口、规范事件ID、提高日志系统的可维护性和可扩展性。

### 1.1 原有问题分析

1. **不同的日志变量**：
   - `_logger` - 使用Microsoft.Extensions.Logging.ILogger
   - `_loggingService` - 使用自定义ILoggingService接口
   - `Logger` - BasePage中的属性

2. **不同的调用模式**：
   - `_logger.LogXXX(message, eventId)`
   - `_loggingService.LogXXX(message, eventId)`
   - `Logger?.LogXXX(message, eventId)`

3. **事件ID问题**：
   - EventID重复使用（如3044同时用于"PLC Connected"和"FTitlePage3 Cleanup Complete"）
   - EventID无逻辑分类（相关功能的EventID分散）
   - 命名不规范（中英混合、描述过长、命名冗余）

## 二、统一方案设计

### 2.1 日志接口统一

将所有日志记录统一为使用`ILoggingService`接口：

```csharp
public interface ILoggingService
{
    void LogTrace(string message, EventId eventId = default);
    void LogDebug(string message, EventId eventId = default);
    void LogInformation(string message, EventId eventId = default);
    void LogWarning(string message, EventId eventId = default);
    void LogWarning(Exception exception, string message, EventId eventId = default);
    void LogError(string message, EventId eventId = default);
    void LogError(Exception exception, string message, EventId eventId = default);
    void LogCritical(string message, EventId eventId = default);
    void LogCritical(Exception exception, string message, EventId eventId = default);
}
```

### 2.2 扩展方法实现

创建`LoggingExtensions`类提供便捷的扩展方法：

```csharp
public static class LoggingExtensions
{
    // 简便的日志记录扩展方法
    public static void LogDebug(this ILoggingService logger, string message, int eventId, string eventName)
        => logger.LogDebug(message, new EventId(eventId, eventName));
    
    public static void LogInformation(this ILoggingService logger, string message, int eventId, string eventName)
        => logger.LogInformation(message, new EventId(eventId, eventName));
    
    // 其他扩展方法...
}
```

### 2.3 BasePage标准化

在BasePage中添加标准化的日志记录属性和方法：

```csharp
public abstract class BasePage : Form
{
    // 统一的日志服务
    protected ILoggingService Logger { get; private set; }
    
    protected BasePage()
    {
        // 从DI容器获取日志服务
        Logger = GetService<ILoggingService>();
    }
    
    // 简便的日志记录方法
    protected void LogDebug(string message, int eventId, string eventName = null)
        => Logger?.LogDebug(message, new EventId(eventId, eventName ?? "Debug"));
    
    // 其他日志方法...
}
```

### 2.4 EventID统一规范

#### 2.4.1 EventID分段架构

| 范围 | 用途 |
|------|------|
| 0-99 | 系统级别事件 |
| 100-199 | 用户管理服务 |
| 200-299 | 配置文件操作 |
| 1000-1099 | PLC连接和基础操作 |
| 2000-2099 | 轴控制基础操作 |
| 2100-2199 | 轴初始化和运动 |
| 3000-3099 | UI界面基础操作 |
| 3100-3199 | 权限控制相关 |
| 4000-4099 | Timer相关操作 |
| 5000-5099 | 窗体生命周期管理 |
| 6000-6099 | 错误处理和异常 |
| 7000-7099 | 清理和资源管理 |
| 8000-8099 | 性能监控和调试 |

#### 2.4.2 命名规范标准

**EventID编号规则**：
```csharp
// 格式：[模块代码][功能代码][序号]
// 示例：1001 = 1000(PLC模块) + 01(序号)

// 推荐格式：
new EventId(1001, "PLC_Connect_Failed")
new EventId(2001, "Axis_Enable_Failed") 
new EventId(3001, "UI_Update_Error")
```

**事件名称规范**：
```csharp
// 命名模式：[模块]_[动作]_[结果]
// 或者：[功能描述]_[状态]

// 推荐命名：
"PLC_Connect_Success"      // PLC连接成功
"User_Login_Failed"        // 用户登录失败
"Config_Load_Complete"     // 配置加载完成
```

#### 2.4.3 日志级别映射

| 日志级别 | EventID范围建议 | 使用场景 |
|----------|----------------|----------|
| **Critical** | 6000-6019 | 系统崩溃、数据丢失 |
| **Error** | 6020-6099, 1000-1099 | 功能失败、连接错误 |
| **Warning** | 各模块末尾10个 | 潜在问题、性能警告 |
| **Information** | 各模块前80个 | 状态变化、操作成功 |
| **Debug** | 8000-8099 | 调试信息、性能数据 |

## 三、实施过程与完成工作

### 3.1 适配器类更新 (100% 完成)

- ✅ **XyrAxisViewModelAdapter.cs**
  - 将ILogger<XyrAxisViewModelAdapter>替换为ILoggingService
  - 添加JYJ001.App.Services.Common.Interfaces和JYJ001.App.Services.Common.Extension命名空间引用
  - 为所有日志调用添加适当的EventIds

- ✅ **ZAxisViewModelAdapter.cs**
  - 将ILogger<ZAxisViewModelAdapter>替换为ILoggingService
  - 添加命名空间引用
  - 为所有日志调用添加适当的EventIds
  - 修复了设置Z轴原点偏移日志缺少EventId的问题

- ✅ **CameraAxisViewModelAdapter.cs**
  - 将ILogger<CameraAxisViewModelAdapter>替换为ILoggingService
  - 添加命名空间引用
  - 为所有日志调用添加适当的EventIds

### 3.2 工厂类更新 (100% 完成)

- ✅ **AxisViewModelFactory.cs**
  - 将所有ILogger<>替换为ILoggingService
  - 更新所有适配器构造函数调用

### 3.3 基础设施类更新 (100% 完成)

- ✅ **Phase2MigrationHelper.cs**
  - 将Microsoft.Extensions.Logging.ILogger<Phase2MigrationHelper>替换为ILoggingService
  - 更新所有适配器构造函数调用

- ✅ **BasePage.cs**
  - 将ILogger属性替换为ILoggingService
  - 为所有日志调用添加适当的EventIds

- ✅ **BaseForm.cs**
  - 为所有日志调用添加适当的EventIds
  - 修复了窗体资源释放、获取服务等日志调用缺少EventId的问题

- ✅ **TimerWrapper.cs**
  - 为所有日志调用添加适当的EventIds

### 3.4 服务类更新 (100% 完成)

- ✅ **PlcConnectionManager.cs**
  - 确认所有日志调用都使用ILoggingService和适当的EventIds

- ✅ **MainWindowViewModelService.cs**
  - 确认所有日志调用都使用ILoggingService和适当的EventIds

- ✅ **ConstValueCompatibilityService.cs**
  - 确认所有日志调用都使用ILoggingService和适当的EventIds

### 3.5 页面类更新 (100% 完成)

- ✅ **FTitlePage3.cs**
  - 修复了延迟执行SysPareExcute、清理CancellationTokenSource和FTitlePage3清理资源时的日志调用缺少EventId的问题

### 3.6 配置更新 (100% 完成)

- ✅ **Program.cs**
  - 从keyServices数组中移除了ILogger<>的引用

### 3.7 测试代码更新 (100% 完成)

- ✅ **Phase2MigrationTests.cs**
  - 将Mock<ILogger<Phase2MigrationHelper>>替换为Mock<ILoggingService>

## 四、技术细节与实现

### 4.1 统一命名空间引用

统一添加以下命名空间引用：
```csharp
using JYJ001.App.Services.Common.Interfaces;
using JYJ001.App.Services.Common.Extension;
```

### 4.2 统一日志调用格式

统一使用以下格式的日志调用：
```csharp
_logger?.LogXXX(message, WaferAligner.EventIds.EventIds.EventName);
_logger?.LogXXX(ex, message, WaferAligner.EventIds.EventIds.EventName);
```

### 4.3 事件ID具体分配

根据不同的日志内容，使用了以下EventIds：

- **系统事件**：ApplicationStarted, ApplicationExit, ConfigurationLoaded, ConfigurationError
- **PLC相关**：PlcConnectStarted, PlcConnected, PlcConnectFailed, PlcVariableReadError, PlcVariableWriteError
- **UI相关**：PageInitialized, PageClosed, UiUpdateError
- **轴控制**：AxisMoveStarted, AxisMoveCompleted, AxisMoveError, XyrPositionUpdateError
- **资源管理**：ResourceRegistered, ResourceReleased
- **错误处理**：UnhandledException, TimerError

### 4.4 EventID具体示例

| EventID | 名称 | 描述 | 优先级 |
|---------|------|------|--------|
| 0 | LogIn Event | 用户登录成功事件 | 高 |
| 1 | LogOut Event | 用户登出事件 | 高 |
| 20 | No config file exist | 无法找到配置文件 | 中 |
| 23 | Load configuration | 加载配置文件成功 | 中 |
| 1000 | PLC Connect Failed | PLC连接失败 | 高 |
| 1001 | PLC Not Initialized | PLC实例为空，界面功能可能受限 | 高 |
| 1002 | PLC Connected | PLC连接正常，开始启动Timer | 中 |
| 2000 | Axis Enable Failed | 轴使能失败 | 高 |
| 2001 | COM Connect Failed | COM连接失败 | 高 |
| 3001 | Update XYR Position Error | 更新XYR位置时发生错误 | 中 |
| 4001 | Input Timer Null at Load | 加载时tmrInput为null | 中 |
| 4002 | Timer Resumed | 界面变为可见，恢复Timer | 低 |

## 五、开发实践指南

### 5.1 EventID设计流程

1. **确定功能模块**：确定事件属于哪个功能模块（如PLC操作、用户管理、UI界面等）
2. **选择EventID范围**：根据功能模块选择对应的EventID范围
3. **设计事件名称**：遵循命名规范：[模块]_[动作]_[结果]
4. **选择日志级别**：根据事件重要性选择合适的日志级别
5. **编写日志消息**：提供详细但简洁的日志消息
6. **代码实现**：完整的EventID使用示例
7. **文档更新**：更新EventID文档
8. **代码审查**：确保符合规范

### 5.2 代码模板

**标准EventID声明模板**：
```csharp
// EventIds.cs - 集中管理EventID
public static class EventIds
{
    // PLC操作 (1000-1099)
    public static readonly EventId PlcConnectSuccess = new(1001, "PLC_Connect_Success");
    public static readonly EventId PlcConnectFailed = new(1002, "PLC_Connect_Failed");
    public static readonly EventId PlcInstanceNull = new(1003, "PLC_Instance_Null");
    
    // 用户管理 (100-199)
    public static readonly EventId UserLoginSuccess = new(101, "User_Login_Success");
    public static readonly EventId UserLoginFailed = new(102, "User_Login_Failed");
    
    // Timer操作 (4000-4099)
    public static readonly EventId TimerStarted = new(4001, "Timer_Started");
    public static readonly EventId TimerStopped = new(4002, "Timer_Stopped");
    public static readonly EventId TimerDisposed = new(4003, "Timer_Disposed");
}
```

**日志记录模板**：
```csharp
// 日志记录最佳实践模板
public class ServiceTemplate
{
    private readonly ILoggingService _logger;
    
    public async Task<bool> ConnectToPLC()
    {
        try
        {
            // 操作前记录
            _logger.LogInformation("开始连接PLC", EventIds.PlcConnectAttempt);
            
            // 执行操作
            var result = await _plc.ConnectAsync();
            
            // 成功记录
            _logger.LogInformation("PLC连接成功", EventIds.PlcConnectSuccess);
            return true;
        }
        catch (TimeoutException ex)
        {
            // 异常记录
            _logger.LogError(ex, "PLC连接超时", EventIds.PlcConnectFailed);
            return false;
        }
    }
}
```

## 六、性能和可维护性改进

1. **统一接口**：所有组件现在都使用ILoggingService接口，提高了代码一致性
2. **结构化日志**：通过添加EventIds，使日志更加结构化和可查询
3. **命名空间组织**：统一使用JYJ001.App.Services.Common命名空间下的接口和扩展方法
4. **依赖注入**：通过依赖注入获取ILoggingService，降低了组件间的耦合度

## 七、后续建议

1. **性能监控**：添加日志性能监控，评估日志系统对应用程序性能的影响
2. **日志分析工具**：开发或引入日志分析工具，利用结构化的EventIds进行日志分析
3. **日志级别优化**：根据生产环境需求，优化各组件的日志级别配置
4. **日志压缩和归档**：实现日志文件的压缩和归档机制，优化存储空间使用

## 八、结论

日志统一重构已经成功完成，符合Phase 3重构计划的要求。通过统一使用ILoggingService接口和添加适当的EventIds，提高了日志系统的可维护性和可扩展性，为后续的性能优化和功能扩展奠定了基础。 