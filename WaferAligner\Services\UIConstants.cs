using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WaferAligner.Services
{
    /// <summary>
    /// UI相关常量定义
    /// </summary>
    public static class UIConstants
    {
        /// <summary>
        /// 界面标题
        /// </summary>
        public static class Titles
        {
            public const string MAIN_WINDOW_TITLE = "晶圆对准机控制系统";
            public const string RECIPE_WINDOW_TITLE = "配方参数设置";
            public const string SYSTEM_SETUP_TITLE = "系统设置";
        }

        /// <summary>
        /// 界面尺寸常量
        /// </summary>
        public static class Dimensions
        {
            public const int DEFAULT_WINDOW_WIDTH = 1024;
            public const int DEFAULT_WINDOW_HEIGHT = 768;
            public const int DIALOG_WINDOW_WIDTH = 800;
            public const int DIALOG_WINDOW_HEIGHT = 600;
        }

        /// <summary>
        /// 界面颜色常量
        /// </summary>
        public static class Colors
        {
            public const string PRIMARY_COLOR = "#007ACC";
            public const string SECONDARY_COLOR = "#F0F0F0";
            public const string WARNING_COLOR = "#FFA500";
            public const string ERROR_COLOR = "#FF0000";
            public const string SUCCESS_COLOR = "#4CAF50";
        }

        /// <summary>
        /// 刷新间隔常量
        /// </summary>
        public static class RefreshIntervals
        {
            public const int STATUS_REFRESH_MS = 500;
            public const int POSITION_REFRESH_MS = 100;
            public const int SLOW_REFRESH_MS = 1000;
        }

        /// <summary>
        /// 文件路径常量
        /// </summary>
        public static class Paths
        {
            public const string LOG_FOLDER = "Logs";
            public const string RECIPE_FOLDER = "Recipes";
            public const string CONFIG_FOLDER = "Config";
        }
    }
} 