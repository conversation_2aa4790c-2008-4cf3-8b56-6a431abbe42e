﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using WaferAligner;
using WaferAligner.Services;

namespace AlignerUI
{
    public partial class MainWindowViewModel
    {
        #region Parameters Setting variable
        [ObservableProperty] private float inch6Heigh = 0;
        [ObservableProperty] private float realTimeInch6Heigh = 0;

        [ObservableProperty] private float inch6MarkDistance = 0;
        [ObservableProperty] private float realTimeInch6MarkDistance = 0;

        [ObservableProperty] private float inch8Heigh = 0;
        [ObservableProperty] private float realTimeInch8Heigh = 0;

        [ObservableProperty] private float inch8MarkDistance = 0;
        [ObservableProperty] private float realTimeInch8MarkDistance = 0;


        [ObservableProperty] private int initStepNumber = 0;
        [ObservableProperty] private int autoStepNumber = 0;
        [ObservableProperty] private int uvwCalStepNumber = 0;
        [ObservableProperty] private int rCalStepNumber = 0;
        [ObservableProperty] private int uvwStepNumber = 0;
        [ObservableProperty] private int rStepNumber = 0;

        [ObservableProperty] private int state1 = 0;
        [ObservableProperty] private int state2 = 0;
        [ObservableProperty] private int state3 = 0;
        [ObservableProperty] private int state4 = 0;
        [ObservableProperty] private int currentUPosition = 0;
        [ObservableProperty] private int currentVPosition = 0;
        [ObservableProperty] private int currentWPosition = 0;
        [ObservableProperty] private int feedbackUPosition = 0;
        [ObservableProperty] private int feedbackVPosition = 0;
        [ObservableProperty] private int feedbackWPosition = 0;
        [ObservableProperty] private int currentRPosition = 0;
        [ObservableProperty] private int feedbackRPosition = 0;
        [RelayCommand]
        #endregion

        #region Parameter Setting Command
        public async Task SetInch6Height() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.JY_6寸厚度", (UInt16)Inch6Heigh);
        [RelayCommand]
        public async Task SetInch6MarkDistance() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.JY_6寸MARK距离", (UInt16)Inch6MarkDistance);
        [RelayCommand]
        public async Task SetInch8Height() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.JY_8寸厚度", (UInt16)Inch8Heigh);
        [RelayCommand]
        public async Task SetInch8MarkDistance() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.JY_8寸MARK距离", (UInt16)Inch8MarkDistance);


        #endregion
    }
}
