using System;
using System.Threading.Tasks;
using WaferAligner.Interfaces;

namespace WaferAligner.Services
{
    public interface IAxisEventService
    {
        Task RegisterAxisEventAsync(string axisName, string variableName, Action<object> handler);
        void RegisterMainWindowEvent(string variableName, Action<object> handler);
        Task<double> GetAxisPositionAsync(string axisName);
        Task<(double X, double Y, double R)> GetXYRPositionsAsync();
        Task MoveCameraAxesToPositionAsync(double lx, double ly, double lz, double rx, double ry, double rz);
        bool CheckCameraAxesArrived();
        Task SendMsgAsync(int p1, int p2, int p3, int p4, int p5);
        Task<string> ReceiveMsgAsync();
        Task MoveXYRAxesToPositionAsync(float x, float y, float r);
        Task MoveToPositionAsync(string axisName, double position);
        (int XState, int YState, int RState) GetXYRRunStates();
        Task<string> GetXYRPosAsync();
        Task StopAllAxesAsync();
        Task SetZAxisHomeOffsetAsync(double offset);
        Task SetXYRPositionAsync(float x, float y, float r);
        Task SetTopGapParameterAsync(double value);
        Task SetCameraOffsetParameterAsync(double value);
        Task SetBottomPhotoParameterAsync(double value);
        Task SetBottomGapParameterAsync(double value);
        Task SetAxisSpeedsAsync(
            uint xRunSpeed, uint xJogSpeed,
            uint yRunSpeed, uint yJogSpeed,
            uint rRunSpeed, uint rJogSpeed,
            uint zRunSpeed, uint zJogSpeed);
        object GetMainPLCInstance();
        int GetCylinderState(string cylinderType);
        Task<ICameraAxisViewModel> GetCameraAxisViewModelAsync(string position, string axis);
        Task CylinderControlAsync(string cylinderType, int targetState);
        Task CalibrateXYRAsync();
        
        /// <summary>
        /// 设置当前选择的轴ID（替代ConstValue.AXISPOSNUM）
        /// </summary>
        /// <param name="axisId">轴ID</param>
        void SetCurrentAxisId(int axisId);
        
        /// <summary>
        /// 获取当前选择的轴ID（替代ConstValue.AXISPOSNUM）
        /// </summary>
        /// <returns>当前轴ID</returns>
        int GetCurrentAxisId();
        
        /// <summary>
        /// 注销轴事件
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="variableName">变量名称</param>
        /// <returns>异步任务</returns>
        Task UnregisterAxisEventAsync(string axisName, string variableName);
    }
} 