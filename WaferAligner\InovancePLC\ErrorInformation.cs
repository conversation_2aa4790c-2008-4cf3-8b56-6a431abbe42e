﻿namespace AlignerUI
{
    public struct ErrorInformation
    {
        public ErrorProcess Process;
        public int ErrorCode { set; get; }
        public string Message { set; get; }
        public string RecoverySuggesstion { set; get; }
        public override string ToString()
        {
            var suggestMessage = string.IsNullOrEmpty(RecoverySuggesstion) ? "" : $"建议操作 : {RecoverySuggesstion}";
            return $"当前系统存在错误:\n错误代码 : {ErrorCode}, 错误信息 : {Message}\n{suggestMessage}";
        }
    };

    public enum ErrorProcess 
    {
        Add,Remove
    }
}
