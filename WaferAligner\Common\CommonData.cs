﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WaferAligner.Common
{
    class CommonData
    {
        #region 弹出框变量

        public static bool MessageBoxOpenFlag = false;
        ////////////////////////////弹出框变量/////////////////////////////////
        public static string Message = String.Empty;
        public static int MessageBoxTypeNum = -1;//1:提示  2：警告  3：错误
        public static int MessageBoxButtonNum = -1;
        public static int Message_ReturnNum = -1;

        #endregion 弹出框变量

        #region 软件注册

        //加解密密钥及向量（8位）
        public static string EncryptKeyStr = "CETCCETCCETCCETCCETCCETCCETCCETC";

        public static string EncryptIVStr = "WDZZBYJBWDZZBYJB";

        //登录时间校验
        public static long LastLoginTimeInt64 = -1L;

        public static long TrialExpirationTimeInt64 = -1L;

        public static string EquipmentSN = String.Empty;

        //获取当前时间戳
        public static string GetTimeStamp()
        {
            TimeSpan ts = DateTime.Now.ToUniversalTime() - new DateTime(1970, 1, 1);
            return Convert.ToInt64(ts.TotalSeconds).ToString();
        }

        //用户输入的注册码
        public static string RegCode = String.Empty;        //消息框显示

        //是否提前注册标志
        public static bool BeforeExpRegFlag = false;

        //提前注册是否确定标志
        public static bool BeforeExpRegSuccessFlag = false;

        #endregion 软件注册

        public static void Message_behavior(String RMessage, int RMessageBoxButtonNum, int RMessageBoxTypeNum)
        {
            Message = RMessage;
            MessageBoxTypeNum = RMessageBoxTypeNum;
            MessageBoxButtonNum = RMessageBoxButtonNum;
            MessageBoxOpenFlag = true;
            Message_弹出窗口 MessageDlg = new Message_弹出窗口();
            MessageDlg.ShowDialog();
        }
    }
}
