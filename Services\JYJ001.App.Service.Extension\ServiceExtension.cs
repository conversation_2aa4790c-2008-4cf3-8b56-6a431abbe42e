﻿using Aya.DataBase;
using Craft.Executer;
using JYJ001.App.Service.Common;
using JYJ001.App.Service.Common.Interface;
using JYJ001.App.Service.Record;
using JYJ001.App.Service.SubSystem;
using JYJ001.App.Service.SubSystem.Interface;
using JYJ001.App.Services.Common;
using JYJ001.App.Services.Common.Interfaces;
using JYJ001.App.Services.Interfaces;
using JYJ001.App.Services.Recipe;
using JYJ001.App.Services.Recipe.Interface;
using JYJ001.App.Services.Record.Interfaces;
using JYJ001.App.Services.SubSystem;
using JYJ001.App.Serviec.SubSystem.Interface;
using Microsoft.Extensions.DependencyInjection;

namespace JYJ001.App.Service.Extension
{
    public static class ServiceExtension
    {
        public static IServiceCollection AddRecipeExecutor(this IServiceCollection services)
        {
            services.AddSingleton<IHeatSystemCraftExecutor, HeatSystemCraftExecutor>();
            services.AddSingleton<IVacuumSystemCraftExecutor, VacuumSystemCraftExecutor>();
            services.AddSingleton<IChamberCraftExecutor, ChamberCraftExecutor>();
            services.AddSingleton<IPressurizationSystemCraftExecutor, PressurizationSystemCraftExecutor>();
            services.AddSingleton<IWarningService, WarningService>();
            return services;
        }
        
        public static IServiceCollection AddNecesseryService(this IServiceCollection services)
        {
            services.AddSingleton<IConfig, JsonFileConfiguration>();
            services.AddSingleton<IUserRepository, UserRepository>();
            services.AddSingleton<IAccountService, AccountService>();
            services.AddTransient<IStringCommandProcessor, StringCommandProcessor>();
            return services;
        }

        public static IServiceCollection AddAbstractEquipment(this IServiceCollection services)
        {
            services.AddSingleton<ISubSystemDataFetcher, SubSystemDataFetcher>();
            services.AddSingleton<IPressurizationSystemService, PressurizationSystemService>();
            services.AddSingleton<IVacuumSystemService, VacuumSystemService>();
            services.AddSingleton<IHeatSystemService, HeatSystemService>();
            services.AddSingleton<IChamberService, ChamberService>();
            services.AddSingleton<IWarningService, WarningService>();
            services.AddTransient<IStringCommandDispatcher, StringCommandDispatcher>();
            services.AddSingleton<IRecipeTaskService, RecipeTaskService>();
            return services;
        }

        public static IServiceCollection AddRecorder(this IServiceCollection services)
        {
            services.AddSingleton<IRecipeCache, BondingRecipeCache>();

            services.AddTransient<IFileStorageService, FileStorageService>();
            services.AddTransient<IInfluxDbService, InfluxDbRecordService>();
            services.AddSingleton<IRecordService, RecordService>();
            return services;
        }
    }
}
