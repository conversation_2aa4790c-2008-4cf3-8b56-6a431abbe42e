﻿namespace PLC.Inovance.Client
{
    public class InvoanceVariableChangedEventArgs : EventArgs
    {
        public InvoanceVariableChangedEventArgs(string name, uint handler, object value)
        {
            Value = value;
            Name = name;
            Handle = handler;
        }
        public object Value;
        public string Name;
        public uint Handle;
        public string Message = string.Empty;

    }
}
