using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Aya.PLC.Base;
using Microsoft.Extensions.Logging;
using PLC.Inovance;
using WaferAligner.Interfaces;
using System.Threading;
using JYJ001.App.Services.Common.Interfaces;
using JYJ001.App.Services.Common.Extension;
using WaferAligner.Common;
using WaferAligner.EventIds;

namespace WaferAligner.Services
{
    /// <summary>
    /// PLC连接管理器实现
    /// </summary>
    public class PlcConnectionManager : IPlcConnectionManager
    {
        private readonly ILoggingService _loggingService;
        private readonly ConcurrentDictionary<string, PlcConnectionInfo> _connections;
        private volatile bool _disposed = false;

        public event EventHandler<PlcConnectionEventArgs> ConnectionStateChanged;

        public PlcConnectionManager(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _connections = new ConcurrentDictionary<string, PlcConnectionInfo>();
        }

        public async Task<bool> ConnectAsync(string connectionName, string address, int port)
        {
            if (_disposed || string.IsNullOrEmpty(connectionName))
                return false;

            try
            {
                // 开发模式检测：如果配置文件或环境变量指示为开发模式，直接返回成功
                bool isDevelopmentMode = System.Configuration.ConfigurationManager.AppSettings["DevelopmentMode"] == "true" ||
                                       Environment.GetEnvironmentVariable("WAFER_ALIGNER_DEV_MODE") == "true" ||
                                       System.Diagnostics.Debugger.IsAttached; // 检测是否在调试器中运行
                
                var plcInstance = new InvoancePlcInstance();
                var connectionInfo = new PlcConnectionInfo
                {
                    Name = connectionName,
                    Address = address,
                    Port = port,
                    PlcInstance = plcInstance,
                    IsConnected = false
                };

                // 添加到连接字典
                _connections.AddOrUpdate(connectionName, connectionInfo, (key, oldValue) => connectionInfo);

                bool success;
                
                if (isDevelopmentMode)
                {
                    // 开发模式：模拟连接成功
                    _loggingService.LogInformation($"开发模式：模拟PLC连接 {connectionName} ({address}:{port}) 成功", WaferAligner.EventIds.EventIds.Plc_Connection_Started);
                    success = true;
                }
                else
                {
                    // 生产模式：执行实际连接，添加超时控制
                    _loggingService.LogInformation($"⚡ 生产模式：尝试连接实际PLC {connectionName} ({address}:{port})", WaferAligner.EventIds.EventIds.Plc_Connection_Started);
                    success = await Task.Run(() => 
                    {
                        try
                        {
                            // 使用CancellationToken添加超时控制
                            using (var cts = new CancellationTokenSource(5000)) // 5秒超时
                            {
                                var connectTask = Task.Run(() =>
                                {
                                    try
                                    {
                                        // 使用正确的Connect方法签名，添加默认网络ID参数
                                        plcInstance.Connect(address, port, 0);
                                        return plcInstance.IsConnected;
                                    }
                                    catch
                                    {
                                        return false;
                                    }
                                }, cts.Token);
                                
                                return connectTask.Wait(5000) ? connectTask.Result : false;
                            }
                        }
                        catch
                        {
                            return false;
                        }
                    });
                }

                connectionInfo.IsConnected = success;
                
                string statusIcon = success ? "[成功]" : "[失败]";
                string modeDesc = isDevelopmentMode ? "开发模式模拟" : "实际硬件";
                if (success)
                    _loggingService.LogInformation($"{statusIcon} PLC连接 {connectionName} ({address}:{port}) 成功 - {modeDesc}", WaferAligner.EventIds.EventIds.Plc_Connection_Succeeded);
                else
                    _loggingService.LogWarning($"{statusIcon} PLC连接 {connectionName} ({address}:{port}) 失败 - {modeDesc}", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                
                // 触发连接状态变化事件
                ConnectionStateChanged?.Invoke(this, new PlcConnectionEventArgs
                {
                    ConnectionName = connectionName,
                    IsConnected = success,
                    Address = address,
                    Port = port
                });

                return success;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"连接PLC {connectionName} 时发生错误", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                
                ConnectionStateChanged?.Invoke(this, new PlcConnectionEventArgs
                {
                    ConnectionName = connectionName,
                    IsConnected = false,
                    Address = address,
                    Port = port,
                    Exception = ex
                });
                
                return false;
            }
        }

        public async Task<bool> DisconnectAsync(string connectionName)
        {
            if (_disposed || !_connections.TryGetValue(connectionName, out var connectionInfo))
                return false;

            try
            {
                await Task.Run(() =>
                {
                    try
                    {
                        // 使用InvoancePlcInstance的Disconnect方法断开连接
                        var plc = connectionInfo.PlcInstance as InvoancePlcInstance;
                        if (plc != null && plc.IsConnected)
                        {
                            plc.Disconnect(); // 使用Disconnect方法替代Dispose
                        }
                        connectionInfo.IsConnected = false;
                    }
                    catch (Exception ex)
                    {
                        _loggingService.LogWarning(ex, $"断开PLC连接 {connectionName} 时发生警告", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                    }
                });

                _loggingService.LogInformation($"PLC连接 {connectionName} 已断开", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                
                ConnectionStateChanged?.Invoke(this, new PlcConnectionEventArgs
                {
                    ConnectionName = connectionName,
                    IsConnected = false,
                    Address = connectionInfo.Address,
                    Port = connectionInfo.Port
                });

                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"断开PLC连接 {connectionName} 时发生错误", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return false;
            }
        }

        public async Task DisconnectAllAsync()
        {
            var tasks = _connections.Keys.Select(DisconnectAsync);
            await Task.WhenAll(tasks);
        }

        public bool IsConnected(string connectionName)
        {
            return _connections.TryGetValue(connectionName, out var info) && info.IsConnected;
        }

        public IPlcInstance GetPlcInstance(string connectionName)
        {
            return _connections.TryGetValue(connectionName, out var info) ? info.PlcInstance : null;
        }

        public IEnumerable<string> GetAllConnectionNames()
        {
            return _connections.Keys.ToList();
        }

        public async Task<bool> ConnectAllAsync()
        {
            // 预定义的PLC连接配置
            var connections = new[]
            {
                ("XYRAxis", "************", 502),
                ("ZAxis", "************", 502),
                ("LeftX", "************", 502),
                ("LeftY", "************", 502),
                ("LeftZ", "************", 502),
                ("RightX", "************", 502),
                ("RightY", "************", 502),
                ("RightZ", "************", 502)
            };

            var tasks = connections.Select(conn => ConnectAsync(conn.Item1, conn.Item2, conn.Item3));
            var results = await Task.WhenAll(tasks);
            
            return results.All(result => result);
        }

        public async Task<Dictionary<string, bool>> GetAllConnectionStatesAsync()
        {
            return await Task.FromResult(_connections.ToDictionary(
                kvp => kvp.Key, 
                kvp => kvp.Value.IsConnected));
        }

        public async Task InitializeAsync()
        {
            _loggingService.LogInformation("初始化PLC连接管理器", WaferAligner.EventIds.EventIds.Plc_Connection_Started);
            await ConnectAllAsync();
        }

        public async Task CleanupAsync()
        {
            _loggingService.LogInformation("清理PLC连接管理器", WaferAligner.EventIds.EventIds.Resource_Released);
            await DisconnectAllAsync();
            _connections.Clear();
        }

        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;
            
            CleanupAsync().Wait(TimeSpan.FromSeconds(5));
        }

        private class PlcConnectionInfo
        {
            public string Name { get; set; }
            public string Address { get; set; }
            public int Port { get; set; }
            public IPlcInstance PlcInstance { get; set; }
            public bool IsConnected { get; set; }
        }
    }
} 