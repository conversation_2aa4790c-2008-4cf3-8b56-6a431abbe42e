﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <_LastSelectedProfileId>D:\对准设备\仪综所\WaferAligner-20250209 -仪综所\WaferAligner\Properties\PublishProfiles\FolderProfile.pubxml</_LastSelectedProfileId>
  </PropertyGroup>
  <ItemGroup>
    <Compile Update="Common\BaseForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Common\BasePage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Common\LoadingIndicator.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Common\弹出窗口.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="FHeaderMainFooter.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\CustomContro\MyLED.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\CustomContro\InstrumnetControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\CustomContro\传感器.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\CustomContro\AxisControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\CustomContro\电磁阀.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\CustomContro\ULine.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\CustomContro\提示消息.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\CustomContro\确认消息.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Pages\FAddUserDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Pages\FChangePasswordDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Pages\FEditUserDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Pages\FLoginPage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Pages\FTitlePage1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Pages\FTitlePage2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Pages\FTitlePage3.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Pages\FTitlePage4.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="FHeaderMainFooter.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Forms\Pages\FTitlePage1.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Forms\Pages\FTitlePage2.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Forms\Pages\FTitlePage4.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
</Project>