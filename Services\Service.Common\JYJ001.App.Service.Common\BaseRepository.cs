using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace JYJ001.App.Service.Common
{
    public class BaseRepository<T,DC>
        where T : class, new()
        where DC : DbContext
    {
        private readonly DC myDbContext;

        public BaseRepository(DC myDbContext)
        {
            this.myDbContext = myDbContext;
        }

        public async ValueTask<EntityEntry<T>> Insert(T entity)
        {
            return await myDbContext.Set<T>().AddAsync(entity);
        }

        public void Update(T entity)
        {
            myDbContext.Set<T>().Update(entity);
        }

        public async Task<bool> UpdateAsycn(Expression<Func<T, bool>> whereLambda, T entity)
        {
            return await myDbContext.Set<T>().Where(whereLambda).UpdateAsync(entity);
        }

        public bool Delete(T entity)
        {
            return myDbContext.Set<T>().Remove(entity).State switch
            {
                EntityState.Detached => false,
                EntityState.Unchanged => false,
                EntityState.Deleted => true,
                EntityState.Modified => false,
                EntityState.Added => false,
                _ => false,
            };
        }

        public async Task<bool> IsExist(Expression<Func<T, bool>> whereLambda)
        {
            return await myDbContext.Set<T>().AnyAsync(whereLambda);
        }

        public async Task<T> GetEntity(Expression<Func<T, bool>> whereLambda)
        {
            return await myDbContext.Set<T>().AsNoTracking().FirstOrDefaultAsync(whereLambda);
        }

        public async Task<List<T>> Select()
        {
            return await myDbContext.Set<T>().ToListAsync();
        }

        public async Task<List<T>> Select(Expression<Func<T, bool>> whereLambda)
        {
            return await myDbContext.Set<T>().Where(whereLambda).ToListAsync();
        }

        public async Task<Tuple<List<T>, int>> Select<S>(int pageSize, int pageIndex, Expression<Func<T, bool>> whereLambda, Expression<Func<T, S>> orderByLambda, bool isAsc)
        {
            var total = await myDbContext.Set<T>().Where(whereLambda).CountAsync();

            if (isAsc)
            {
                var entities = await myDbContext.Set<T>().Where(whereLambda)
                                      .OrderBy<T, S>(orderByLambda)
                                      .Skip(pageSize * (pageIndex - 1))
                                      .Take(pageSize).ToListAsync();

                return new Tuple<List<T>, int>(entities, total);
            }
            else
            {
                var entities = await myDbContext.Set<T>().Where(whereLambda)
                                      .OrderByDescending<T, S>(orderByLambda)
                                      .Skip(pageSize * (pageIndex - 1))
                                      .Take(pageSize).ToListAsync();

                return new Tuple<List<T>, int>(entities, total);
            }
        }
    }

    public static class IQueryableExtension
    {
        public static Task<bool> UpdateAsync<T>(this IQueryable<T> q, T entity, CancellationToken cancellationToken = default(CancellationToken)) where T : class, new()
        {
            
            return Task.Run(() => q.Update(entity), cancellationToken);
        }

        public static bool Update<T>(this IQueryable<T> query, T entity) where T : class
        {
            var dbcontext = query as DbContext;
            var entry = dbcontext.Update(entity);
            return entry.State switch
            {
                EntityState.Detached => false,
                EntityState.Unchanged => true,
                EntityState.Deleted => false,
                EntityState.Modified => true,
                EntityState.Added => true,
            };
        }
    }
}

