﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using System.Reflection;
using System.Threading;
using System.IO.Ports;
//using SemiautoParallelWeld.自定义.控件;
using System.Drawing;
namespace WaferAligner
{
    public class PublicFunc
    {
        public string FloatToHexByte(float str)
        {
            string s1 = BitConverter.ToString(BitConverter.GetBytes(str));
            string s1a = s1.Substring(9, 2) + " " + s1.Substring(6, 2);
            return s1a;
        }
        public static void AddList(Control obj, List<InstrumnetControl> list)
        {
            foreach (Control control in obj.Controls)
            {
                if (control is InstrumnetControl) list.Add((InstrumnetControl)control);
                AddList(control,list);
            }
        }
        //public static string WriteGasToHex(float gas, string address)
        //{
        //    string s1 = BitConverter.ToString(BitConverter.GetBytes(gas));//浮点转16进制
        //    string str = s1.Substring(9, 2) + s1.Substring(6, 2);
        //    //string str = int.Parse(temper.ToString("F0")).ToString("X2");
        //    string gasStr = "0000".Substring(0, 4 - str.Length) + str;
        //    str = gasStr.Substring(0, 2) + " " + gasStr.Substring(2, 2);
        //    str = "07 06 " + address + str;
        //    string checkStr = new Crc16().ToModbusCRC16(str);
        //    str = str + " " + checkStr.Substring(0, 2) + " " + checkStr.Substring(2, 2);
        //    return str;
        //}
        //public static string WriteSewToHex(int value, string address)
        //{
        //    string valuestr = value.ToString("x2");//转化为16进制
        //    valuestr = "0000".Substring(0, 4 - valuestr.Length) + valuestr;
        //    valuestr = valuestr.Substring(0, 2) + " " + valuestr.Substring(2, 2);
        //    valuestr = "01 06 " + address + valuestr;
        //    string checkStr = new Crc16().ToModbusCRC16(valuestr);
        //    valuestr = valuestr + " " + checkStr.Substring(0, 2) + " " + checkStr.Substring(2, 2);
        //    return valuestr;
        //}

        //延迟一段时间
        public static void Delay(int mm)
        {
            DateTime current = DateTime.Now;
            while (current.AddMilliseconds(mm) > DateTime.Now)
            {
                Application.DoEvents();
            }
            return;
        }
        #region 禁止系统休眠
        [DllImport("kernel32.dll")]
        static extern uint SetThreadExecutionState(uint Flags);
        const uint ES_SYSTEM_REQUIRED = 0x00000001;
        const uint ES_DISPLAY_REQUIRED = 0x00000002;
        const uint ES_CONTINUOUS = 0x80000000;

        public static void SleepControl(bool isSleep)
        {
            if (isSleep)
            {
                //阻止休眠时调用
                SetThreadExecutionState(ES_CONTINUOUS | ES_DISPLAY_REQUIRED | ES_SYSTEM_REQUIRED);
            }
            else
            {
                //恢复休眠时调用
                SetThreadExecutionState(ES_CONTINUOUS);
            }
        }
        #endregion
        public static void CallOnClick(Button btn)
        {
            Type t = typeof(Button);
            object[] p = new object[1];
            MethodInfo m = t.GetMethod("OnClick", BindingFlags.NonPublic | BindingFlags.Instance);
            p[0] = EventArgs.Empty;
            m.Invoke(btn, p);
            return;
        }
        public static void Bt_MouseEnter(object sender, EventArgs e)
        {
            Button bt = (Button)sender;
            bt.Width = Convert.ToInt32(82 * 1.05);
            bt.Height = Convert.ToInt32(44 * 1.05);
            Font font = new (bt.Font.FontFamily, 13);
            bt.Font = font;
        }
        public static void Bt_MouseLeave(object sender, EventArgs e)
        {
            Button bt = sender as Button;
            bt.Width = 82;
            bt.Height = 44;
            Font font = new (bt.Font.FontFamily, 12);
            bt.Font = font;
        }      

    }
}
