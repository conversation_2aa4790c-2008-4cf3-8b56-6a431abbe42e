using System;
using System.Windows.Forms;

namespace WaferAligner.Common
{
    /// <summary>
    /// 提供安全的UI线程调用扩展方法
    /// </summary>
    public static class SafeInvokeExtensions
    {
        /// <summary>
        /// 安全地在UI线程上调用方法，避免ObjectDisposedException
        /// </summary>
        /// <param name="control">控件</param>
        /// <param name="action">要执行的操作</param>
        /// <returns>如果成功调用返回true，否则返回false</returns>
        public static bool SafeInvoke(this Control control, Action action)
        {
            if (control == null || action == null)
                return false;

            try
            {
                // 第一层检查：基本状态验证
                if (!control.IsHandleCreated || control.IsDisposed || control.Disposing)
                    return false;

                if (control.InvokeRequired)
                {
                    try
                    {
                        // 第二层检查：在调用前再次验证状态
                        if (control.IsDisposed || control.Disposing || !control.IsHandleCreated)
                            return false;

                        // 使用BeginInvoke代替Invoke，更安全且不会阻塞
                        control.BeginInvoke((MethodInvoker)(() =>
                        {
                            try
                            {
                                // 第三层检查：UI线程内最终验证
                                if (!control.IsDisposed && !control.Disposing && control.IsHandleCreated)
                                {
                                    action();
                                }
                            }
                            catch (ObjectDisposedException)
                            {
                                // UI线程中控件已释放，静默忽略
                            }
                            catch (InvalidOperationException)
                            {
                                // UI线程中操作失败，静默忽略
                            }
                            catch (Exception)
                            {
                                // UI线程中的其他异常
                            }
                        }));
                        return true;
                    }
                    catch (ObjectDisposedException)
                    {
                        // BeginInvoke调用时控件已释放
                    }
                    catch (InvalidOperationException)
                    {
                        // BeginInvoke调用失败
                    }
                    catch (System.ComponentModel.Win32Exception)
                    {
                        // Windows句柄相关异常
                    }
                }
                else
                {
                    // 已在UI线程中，直接执行
                    try
                    {
                        if (!control.IsDisposed && !control.Disposing && control.IsHandleCreated)
                        {
                            action();
                            return true;
                        }
                    }
                    catch (ObjectDisposedException)
                    {
                        // 直接执行时控件已释放
                    }
                    catch (InvalidOperationException)
                    {
                        // 直接执行时操作失败
                    }
                }
            }
            catch (ObjectDisposedException)
            {
                // 最外层捕获
            }
            catch (InvalidOperationException)
            {
                // 最外层捕获
            }
            catch (System.ComponentModel.Win32Exception)
            {
                // Windows句柄相关异常
            }
            catch (Exception)
            {
                // 其他未预期的异常
            }

            return false;
        }

        /// <summary>
        /// 安全地在UI线程上调用方法并返回结果
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="control">控件</param>
        /// <param name="func">要执行的函数</param>
        /// <param name="defaultValue">异常时的默认返回值</param>
        /// <returns>函数执行结果或默认值</returns>
        public static T SafeInvoke<T>(this Control control, Func<T> func, T defaultValue = default(T))
        {
            if (control == null || func == null)
                return defaultValue;

            try
            {
                // 第一层检查：基本状态验证
                if (!control.IsHandleCreated || control.IsDisposed || control.Disposing)
                    return defaultValue;

                if (control.InvokeRequired)
                {
                    try
                    {
                        // 第二层检查：在调用前再次验证状态
                        if (control.IsDisposed || control.Disposing || !control.IsHandleCreated)
                            return defaultValue;

                        // 对于有返回值的调用，仍需使用Invoke，但添加更多保护
                        return (T)control.Invoke(func);
                    }
                    catch (ObjectDisposedException)
                    {
                        // Invoke调用时控件已释放
                    }
                    catch (InvalidOperationException)
                    {
                        // Invoke调用失败
                    }
                    catch (System.ComponentModel.Win32Exception)
                    {
                        // Windows句柄相关异常
                    }
                }
                else
                {
                    // 已在UI线程中，直接执行
                    try
                    {
                        if (!control.IsDisposed && !control.Disposing && control.IsHandleCreated)
                        {
                            return func();
                        }
                    }
                    catch (ObjectDisposedException)
                    {
                        // 直接执行时控件已释放
                    }
                    catch (InvalidOperationException)
                    {
                        // 直接执行时操作失败
                    }
                }
            }
            catch (ObjectDisposedException)
            {
                // 最外层捕获
            }
            catch (InvalidOperationException)
            {
                // 最外层捕获
            }
            catch (System.ComponentModel.Win32Exception)
            {
                // Windows句柄相关异常
            }
            catch (Exception)
            {
                // 其他未预期的异常
            }

            return defaultValue;
        }
    }
} 