﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace WaferAligner.Interfaces
{
    /// <summary>
    /// PLC控制轴的通用接口，提供PLC轴特有操作方法
    /// </summary>
    public interface IPlcAxisViewModel : IAxisViewModel
    {
        // PLC轴特有操作
        Task<bool> WritePlcVariableAsync<T>(string variableName, T value, CancellationToken cancellationToken = default);
        Task<T> ReadPlcVariableAsync<T>(string variableName, CancellationToken cancellationToken = default);
        Task<bool> RegisterPlcVariableAsync(string variableName, Action<object> action, CancellationToken cancellationToken = default);
        Task<bool> UnregisterPlcVariableAsync(string variableName, CancellationToken cancellationToken = default);
        Task<bool> SetSafetyCheckEnabledAsync(bool enabled, CancellationToken cancellationToken = default);
        Task<bool> IsHomedAsync(CancellationToken cancellationToken = default);
        Task<bool> IsAtPositionAsync(double position, double tolerance = 0.1, CancellationToken cancellationToken = default);
    }
}
