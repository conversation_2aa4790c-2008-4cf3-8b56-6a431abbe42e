# WaferAligner项目开发文档索引

## 📚 文档导航

### 📋 总体概览
- [项目重构总体概览](./项目重构总体概览.md) - 项目重构的整体视角和各阶段成果

### 🔄 重构计划与进展
- [重构计划](./重构计划.md) - 整体重构规划和目标
- [已完成的工作](./已完成的工作.md) - 已完成重构任务的汇总

### 📊 专项文档（合并版）
- [日志与事件ID专项合并版](./日志与事件ID专项合并版.md) - 日志系统统一和事件ID规范
- [静态兼容层与工具类重构专项合并版](./静态兼容层与工具类重构专项合并版.md) - 静态依赖消除和工具类优化
- [异步与定时器优化专项合并版](./异步与定时器优化专项合并版.md) - 异步编程模式和定时器封装
- [性能优化专项合并版](./性能优化专项合并版.md) - 性能监控和优化措施
- [用户管理与权限专项合并版](./用户管理与权限专项合并版.md) - 用户系统和权限控制

### 📝 重构阶段报告
- [Phase1_Completion_Report.md](./Phase1_Completion_Report.md) - Phase 1完成报告
- [Phase2_Implementation_Progress.md](./Phase2_Implementation_Progress.md) - Phase 2实施进展
- [Phase3RestructuringPlan.md](./Phase3RestructuringPlan.md) - Phase 3重构计划

### 🔧 技术规范与指南
- [EventID_Unified_Standard.md](./EventID_Unified_Standard.md) - 事件ID统一标准
- [EventID_Development_Guide.md](./EventID_Development_Guide.md) - 事件ID开发指南
- [EventID_Quick_Reference.md](./EventID_Quick_Reference.md) - 事件ID快速参考

### 📑 用户指南
- [用户管理操作手册.md](./用户管理操作手册.md) - 用户管理功能操作指南
- [用户角色权限管理说明.md](./用户角色权限管理说明.md) - 权限系统说明文档

## 📈 重构阶段概述

### Phase 1: 基础架构重构
- 完成时间：2024年初
- 主要成果：页面基类迁移、SafeInvoke重复代码消除、资源管理优化等
- 详细文档：[Phase1_Completion_Report.md](./Phase1_Completion_Report.md)

### Phase 2: 服务层重构与静态依赖消除
- 完成时间：2024年第二季度
- 主要成果：接口抽象层设计、适配器模式实现、工厂模式实现等
- 详细文档：[Phase2_Implementation_Progress.md](./Phase2_Implementation_Progress.md)

### Phase 3: 性能优化与规范统一
- 进行中：2024年第三季度至今
- 主要任务：日志统一、异步编程优化、PLC通信优化、性能监控等
- 详细文档：[Phase3RestructuringPlan.md](./Phase3RestructuringPlan.md)

## 🔍 如何使用本文档

1. **新团队成员**：从[项目重构总体概览](./项目重构总体概览.md)开始，了解项目架构和重构历程
2. **开发人员**：参考各专项合并版文档，了解具体技术实现和最佳实践
3. **项目管理**：查阅重构计划和已完成工作，了解项目进展和后续规划
4. **用户支持**：参考用户指南文档，了解系统功能和操作方法

## 📅 文档更新日志

- 2024-XX-XX：完成文档整合，创建专项合并版和总体概览
- 2024-XX-XX：更新Phase 3进展和后续计划
- 2024-XX-XX：添加用户管理与权限专项文档 