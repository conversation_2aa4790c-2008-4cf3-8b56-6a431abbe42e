﻿"DeployProject"
{
"VSVersion" = "3:800"
"ProjectType" = "8:{978C614F-708E-4E1A-B201-565925725DBA}"
"IsWebType" = "8:FALSE"
"ProjectName" = "8:WaferAlignerSetup"
"LanguageId" = "3:2052"
"CodePage" = "3:936"
"UILanguageId" = "3:2052"
"SccProjectName" = "8:"
"SccLocalPath" = "8:"
"SccAuxPath" = "8:"
"SccProvider" = "8:"
    "Hierarchy"
    {
        "Entry"
        {
        "MsmKey" = "8:_029C85BFD51B4898B1063D587FD29B84"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_A39F605A9D6C444EB568AD229E2FD949"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_C0F3A159D9EA4A939F0BAD090D71E3A2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_5DA4CE8E1D2C4B3DAAAC2AEEEE2D0A5F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_18238076331A4C9190688680E4FE069E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_EF50A469EC0F7F2FFB8AD62556873D59"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_DA0BB685481D54439842637264420F91"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_3902BDCDF8E8B9815A577F205995E88E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_A6AF3FAF2F203E52B718C8BEABFB71EB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_8BA258AEEE0955BE8879A146EB2C68BB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_578B458B5B1288A7DFD9D7CBC907DBFF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_C84290DF2BCD0CBD62E9285AE9117CA4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_D5F64030570545BBB4655B7CC7288CB5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_8BFCDD999D9BF5A198DB8C096A3D2D64"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_E9BC6DF1BE0391F2694C013D0D02C4CA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_6998EBD1D8B04B98B6B6C25523DD78DF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_F3F664FB3A4009AE63C284EA2D976404"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_FE2C26A1C9354229B52813D9997C0200"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_05A8E84DF65236EA05796094770AED94"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_2DA8C6B921F145CE8986BD343CF4043F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_D1732C39B5804058AA99F82392A001B1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_F2ECD6C04D2F494CB78750C527190C89"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_F2031F8A12CC4FCAB4DB6C1DCA1F53A0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_D1FB0D33AF78ACFBCFA573904A10330B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_054075C1BFFB2189AB23548392ABA4C8"
        "OwnerKey" = "8:_A96C539F4F7747E6B88350DACEB2CDFE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_05A8E84DF65236EA05796094770AED94"
        "OwnerKey" = "8:_2DA8C6B921F145CE8986BD343CF4043F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_05A8E84DF65236EA05796094770AED94"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_05A8E84DF65236EA05796094770AED94"
        "OwnerKey" = "8:_F3F664FB3A4009AE63C284EA2D976404"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_060DD0C0ECDB2050EDC645393A52864A"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0876D84086430509E41E51152E77E106"
        "OwnerKey" = "8:_0E9BF086371446C28CB59005F426C733"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0A571815C84E468EA8A1843DFB8739CA"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0E9BF086371446C28CB59005F426C733"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0F9F80019D10366D94E3E592C8991303"
        "OwnerKey" = "8:_DA0BB685481D54439842637264420F91"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0F9F80019D10366D94E3E592C8991303"
        "OwnerKey" = "8:_029C85BFD51B4898B1063D587FD29B84"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0F9F80019D10366D94E3E592C8991303"
        "OwnerKey" = "8:_4C488CBEE9D24C948DAEAF47BD799221"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0F9F80019D10366D94E3E592C8991303"
        "OwnerKey" = "8:_DA9FD68E285A4F66A0BF77E6544C8307"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0F9F80019D10366D94E3E592C8991303"
        "OwnerKey" = "8:_5DA4CE8E1D2C4B3DAAAC2AEEEE2D0A5F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0F9F80019D10366D94E3E592C8991303"
        "OwnerKey" = "8:_18238076331A4C9190688680E4FE069E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0F9F80019D10366D94E3E592C8991303"
        "OwnerKey" = "8:_5A4758B680C2C6C2F7E2921BF1338D80"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0F9F80019D10366D94E3E592C8991303"
        "OwnerKey" = "8:_4AB7E9686F487BAE8291FF978E84C179"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0F9F80019D10366D94E3E592C8991303"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0F9F80019D10366D94E3E592C8991303"
        "OwnerKey" = "8:_BCB43F32D71FC84B00CC9372B0DFB3EA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0F9F80019D10366D94E3E592C8991303"
        "OwnerKey" = "8:_EF50A469EC0F7F2FFB8AD62556873D59"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_18238076331A4C9190688680E4FE069E"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_19922708D7D91B9070B2E362FBE3D9C7"
        "OwnerKey" = "8:_7D260ED8A73141C1B89B143B762B382E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_19922708D7D91B9070B2E362FBE3D9C7"
        "OwnerKey" = "8:_8BDB3B1D826F70AB11675D9D9E2FC017"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1CE8CDC04A6E333DDD41EF447FA991FC"
        "OwnerKey" = "8:_8F6AB9C24BED419386330133FA454C1B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1F3931817ACB47AE95E65F5F842370A9"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_23A36623C3514FC39D7E7918ADBF5D96"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_254B8CCB382E4AB4B557BB1E099DAC94"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_277D7431A62748D18949AE9658785ECD"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2DA8C6B921F145CE8986BD343CF4043F"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2F8E06972FDA4098B644D7A2764B1909"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_317BD0D6813E4FEB81381F4FD289A266"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_31866AC0C94E55C346FDCA2DC8D73FF3"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_319D7A30A855F11A44434A46C57EFABE"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_325493729D304BFEB3E7CE84F7B6E0F4"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3902BDCDF8E8B9815A577F205995E88E"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3B0E2EFE91F345C9A6879D946683498E"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3CF3851904240FE4AC6EAD004DD84A8B"
        "OwnerKey" = "8:_0E9BF086371446C28CB59005F426C733"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_41714E969EDF4702BB591D3CB1A78D47"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4258A07C97984574B46320005057BF48"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_441BB3C80DFE4B2AA4FDB0CDAA1C7FC3"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45617518ABC5E63195C9B907914CD8A1"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45A6C04DE5434D1598D4E75A3D15115A"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45CEBDB23F9A49518B1B2D5A550FFB5E"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4703B794908242E19C1E514222E90BD3"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4AB7E9686F487BAE8291FF978E84C179"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4C488CBEE9D24C948DAEAF47BD799221"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4EFD151F2ED14254ADAED0FBEFD99606"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_55EA06484CDD1EBF44AFA45B425CF67E"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_55F264174A45F91C09670C44B6ADCAA6"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5628BE3525A348D08169ECC56935FAE0"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_578B458B5B1288A7DFD9D7CBC907DBFF"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_58FE4E2EE3A64232AA1BA03467F9A85B"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5A4758B680C2C6C2F7E2921BF1338D80"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5BC2E1EDFB7246FF990F8A80B8D49F8E"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5DA4CE8E1D2C4B3DAAAC2AEEEE2D0A5F"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5F90ADF5198C4A2F94EFD5ECC6A7872B"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5FC27F3A3381453A969D8259865D3A6D"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5FD2E248D35B6DD51DDE49D456A545D4"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_652941B2F6E3D1D206B0CFEC0B216B11"
        "OwnerKey" = "8:_0E9BF086371446C28CB59005F426C733"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_66B525429BFB3B8EDF8D9CDC5E76B60D"
        "OwnerKey" = "8:_8423CC19C1FDD793DCDBC6BE36CFCFEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_66B525429BFB3B8EDF8D9CDC5E76B60D"
        "OwnerKey" = "8:_BE87E6BFD412415ABCB282CC75F5B8C1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_68EC67F6CE004E9293AF26E5F7FFA001"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6998EBD1D8B04B98B6B6C25523DD78DF"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6CACAB74D0D04C72B1D26CC88EF05B63"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6F97B38982EAD1CEC2ECC527529427A8"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7022F8202AEBC8054618744DCB134D35"
        "OwnerKey" = "8:_7D260ED8A73141C1B89B143B762B382E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7022F8202AEBC8054618744DCB134D35"
        "OwnerKey" = "8:_8BDB3B1D826F70AB11675D9D9E2FC017"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_70CD6C441141F152379B1C89DAAA80EE"
        "OwnerKey" = "8:_D8F051496109496A846131C2A0ED4128"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_70CD6C441141F152379B1C89DAAA80EE"
        "OwnerKey" = "8:_3CF3851904240FE4AC6EAD004DD84A8B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_70CD6C441141F152379B1C89DAAA80EE"
        "OwnerKey" = "8:_0E9BF086371446C28CB59005F426C733"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7125AA0C5BE3469DA293E5535C4B1F5F"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7A36DF40AFE54841AA2DB790DCF60AF9"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7D260ED8A73141C1B89B143B762B382E"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81BEC32DB6AF4F02BADFECC8EF984F8E"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_82FDD6D0A71E9F5E45C65867F29EBD60"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_82FDD6D0A71E9F5E45C65867F29EBD60"
        "OwnerKey" = "8:_D5B7276BCCEA4B079A980BADFA00B662"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_82FDD6D0A71E9F5E45C65867F29EBD60"
        "OwnerKey" = "8:_0F9F80019D10366D94E3E592C8991303"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8423CC19C1FDD793DCDBC6BE36CFCFEE"
        "OwnerKey" = "8:_D1732C39B5804058AA99F82392A001B1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8423CC19C1FDD793DCDBC6BE36CFCFEE"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8423CC19C1FDD793DCDBC6BE36CFCFEE"
        "OwnerKey" = "8:_8BA258AEEE0955BE8879A146EB2C68BB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8423CC19C1FDD793DCDBC6BE36CFCFEE"
        "OwnerKey" = "8:_FE2C26A1C9354229B52813D9997C0200"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8423CC19C1FDD793DCDBC6BE36CFCFEE"
        "OwnerKey" = "8:_05A8E84DF65236EA05796094770AED94"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_86197D63F6424942B2AE5FD74685A1D9"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_88EBFA00D3424D8AB8B6AA81B8C587A7"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8BA258AEEE0955BE8879A146EB2C68BB"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8BDB3B1D826F70AB11675D9D9E2FC017"
        "OwnerKey" = "8:_0E9BF086371446C28CB59005F426C733"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8BFCDD999D9BF5A198DB8C096A3D2D64"
        "OwnerKey" = "8:_E9BC6DF1BE0391F2694C013D0D02C4CA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8BFCDD999D9BF5A198DB8C096A3D2D64"
        "OwnerKey" = "8:_C0F3A159D9EA4A939F0BAD090D71E3A2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8BFCDD999D9BF5A198DB8C096A3D2D64"
        "OwnerKey" = "8:_5DA4CE8E1D2C4B3DAAAC2AEEEE2D0A5F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8BFCDD999D9BF5A198DB8C096A3D2D64"
        "OwnerKey" = "8:_EF50A469EC0F7F2FFB8AD62556873D59"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8BFCDD999D9BF5A198DB8C096A3D2D64"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8DD514D95956472C8842D7F8919CA8D3"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8F6AB9C24BED419386330133FA454C1B"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8F8CD6BFD940872D6A5E9E4AB5CF1BC0"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_91285A190004459693BF431F4D905A0C"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_95419F7C92F94046A61A03794DE79317"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_96012B4109C643EB8284990B3D5B3DD6"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_995405911EF6E81A4BCBDD931A4D3929"
        "OwnerKey" = "8:_D1732C39B5804058AA99F82392A001B1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_995405911EF6E81A4BCBDD931A4D3929"
        "OwnerKey" = "8:_3902BDCDF8E8B9815A577F205995E88E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_995405911EF6E81A4BCBDD931A4D3929"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_995405911EF6E81A4BCBDD931A4D3929"
        "OwnerKey" = "8:_8BA258AEEE0955BE8879A146EB2C68BB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_995405911EF6E81A4BCBDD931A4D3929"
        "OwnerKey" = "8:_D5F64030570545BBB4655B7CC7288CB5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_995405911EF6E81A4BCBDD931A4D3929"
        "OwnerKey" = "8:_BE87E6BFD412415ABCB282CC75F5B8C1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_995405911EF6E81A4BCBDD931A4D3929"
        "OwnerKey" = "8:_FE2C26A1C9354229B52813D9997C0200"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_995405911EF6E81A4BCBDD931A4D3929"
        "OwnerKey" = "8:_F3F664FB3A4009AE63C284EA2D976404"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_995405911EF6E81A4BCBDD931A4D3929"
        "OwnerKey" = "8:_2DA8C6B921F145CE8986BD343CF4043F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_995405911EF6E81A4BCBDD931A4D3929"
        "OwnerKey" = "8:_05A8E84DF65236EA05796094770AED94"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_995405911EF6E81A4BCBDD931A4D3929"
        "OwnerKey" = "8:_8423CC19C1FDD793DCDBC6BE36CFCFEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9F62AC9603614CED9684FE0B2A843607"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A0B3A4DCBC52644FF4C0232F0D634800"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A39F605A9D6C444EB568AD229E2FD949"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A5E6F285DCD54850879208CA48D4EA90"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A6AF3FAF2F203E52B718C8BEABFB71EB"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A7A69131CF844B4CA583FB94CF5893A9"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A8486F2372EE47D393D168EA6858C207"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A96C539F4F7747E6B88350DACEB2CDFE"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B5B8DD088C09456888419B0E45A1D0A1"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B80B147410B94913975D345594A8F3CA"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BCB43F32D71FC84B00CC9372B0DFB3EA"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BCB66B8D09C6A84223C2B1CD77629922"
        "OwnerKey" = "8:_81BEC32DB6AF4F02BADFECC8EF984F8E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BCB66B8D09C6A84223C2B1CD77629922"
        "OwnerKey" = "8:_652941B2F6E3D1D206B0CFEC0B216B11"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BCB66B8D09C6A84223C2B1CD77629922"
        "OwnerKey" = "8:_0E9BF086371446C28CB59005F426C733"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BE87E6BFD412415ABCB282CC75F5B8C1"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C0F3A159D9EA4A939F0BAD090D71E3A2"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C2E8180C610648D4844F996610E17A21"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C4F10864C02F4A1EA3C338FC3BF1414E"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C84290DF2BCD0CBD62E9285AE9117CA4"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1732C39B5804058AA99F82392A001B1"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1FB0D33AF78ACFBCFA573904A10330B"
        "OwnerKey" = "8:_A96C539F4F7747E6B88350DACEB2CDFE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1FB0D33AF78ACFBCFA573904A10330B"
        "OwnerKey" = "8:_18238076331A4C9190688680E4FE069E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1FB0D33AF78ACFBCFA573904A10330B"
        "OwnerKey" = "8:_DA0BB685481D54439842637264420F91"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1FB0D33AF78ACFBCFA573904A10330B"
        "OwnerKey" = "8:_A6AF3FAF2F203E52B718C8BEABFB71EB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1FB0D33AF78ACFBCFA573904A10330B"
        "OwnerKey" = "8:_578B458B5B1288A7DFD9D7CBC907DBFF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1FB0D33AF78ACFBCFA573904A10330B"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1FB0D33AF78ACFBCFA573904A10330B"
        "OwnerKey" = "8:_C84290DF2BCD0CBD62E9285AE9117CA4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1FB0D33AF78ACFBCFA573904A10330B"
        "OwnerKey" = "8:_6998EBD1D8B04B98B6B6C25523DD78DF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1FB0D33AF78ACFBCFA573904A10330B"
        "OwnerKey" = "8:_F3F664FB3A4009AE63C284EA2D976404"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1FB0D33AF78ACFBCFA573904A10330B"
        "OwnerKey" = "8:_05A8E84DF65236EA05796094770AED94"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1FB0D33AF78ACFBCFA573904A10330B"
        "OwnerKey" = "8:_2DA8C6B921F145CE8986BD343CF4043F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1FB0D33AF78ACFBCFA573904A10330B"
        "OwnerKey" = "8:_D1732C39B5804058AA99F82392A001B1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1FB0D33AF78ACFBCFA573904A10330B"
        "OwnerKey" = "8:_F2ECD6C04D2F494CB78750C527190C89"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D24C080CC25663D08D163079216EE147"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D5B7276BCCEA4B079A980BADFA00B662"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D5F64030570545BBB4655B7CC7288CB5"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D8F051496109496A846131C2A0ED4128"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DA0BB685481D54439842637264420F91"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DA0BB685481D54439842637264420F91"
        "OwnerKey" = "8:_5DA4CE8E1D2C4B3DAAAC2AEEEE2D0A5F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DA0BB685481D54439842637264420F91"
        "OwnerKey" = "8:_EF50A469EC0F7F2FFB8AD62556873D59"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DA9FD68E285A4F66A0BF77E6544C8307"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "OwnerKey" = "8:_9F62AC9603614CED9684FE0B2A843607"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "OwnerKey" = "8:_19922708D7D91B9070B2E362FBE3D9C7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DFD23A972917448B9D4431ED00DDAADD"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E25EBFFB63842C699C26E8EA8A30EB54"
        "OwnerKey" = "8:_45617518ABC5E63195C9B907914CD8A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E25EBFFB63842C699C26E8EA8A30EB54"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E7BE391B25C46E76F611FC215D36A61D"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E8D9F63FD8E02A95C345767FE460CE3D"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E8F664DCDCB35A26A6CC296A14651810"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E9BC6DF1BE0391F2694C013D0D02C4CA"
        "OwnerKey" = "8:_6998EBD1D8B04B98B6B6C25523DD78DF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E9BC6DF1BE0391F2694C013D0D02C4CA"
        "OwnerKey" = "8:_5DA4CE8E1D2C4B3DAAAC2AEEEE2D0A5F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E9BC6DF1BE0391F2694C013D0D02C4CA"
        "OwnerKey" = "8:_18238076331A4C9190688680E4FE069E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E9BC6DF1BE0391F2694C013D0D02C4CA"
        "OwnerKey" = "8:_EF50A469EC0F7F2FFB8AD62556873D59"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E9BC6DF1BE0391F2694C013D0D02C4CA"
        "OwnerKey" = "8:_DA0BB685481D54439842637264420F91"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E9BC6DF1BE0391F2694C013D0D02C4CA"
        "OwnerKey" = "8:_A6AF3FAF2F203E52B718C8BEABFB71EB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EE441F3030F94F6BB4AB0A93C384EB42"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EE72111447A448A4A39E143A292A261A"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EF50A469EC0F7F2FFB8AD62556873D59"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F077E18D14C745CFB5163E98ADC115D3"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F0EF5395F6954CF99F26AFF8AF4E9975"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F2031F8A12CC4FCAB4DB6C1DCA1F53A0"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F2ECD6C04D2F494CB78750C527190C89"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F3F664FB3A4009AE63C284EA2D976404"
        "OwnerKey" = "8:_FE2C26A1C9354229B52813D9997C0200"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F3F664FB3A4009AE63C284EA2D976404"
        "OwnerKey" = "8:_8BA258AEEE0955BE8879A146EB2C68BB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F3F664FB3A4009AE63C284EA2D976404"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F4A82C6E625C4CE4886BD80895570C72"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F4B940144E41E686F10A1D1B61E94F3A"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F4FF5FB69FA74DDA8CECD571C62F9D3F"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F6C0CD14065843DAA2C01D689951ADE5"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FB8C13FA077B4AB9A3354EEF15270C6F"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FCCF27C6E4DD48BDB5EEC17F21F18183"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE0F2B09A78B4D9692EF0EAFDB871151"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE2C26A1C9354229B52813D9997C0200"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE68471BEED19124E91850C068EE681D"
        "OwnerKey" = "8:_6998EBD1D8B04B98B6B6C25523DD78DF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE68471BEED19124E91850C068EE681D"
        "OwnerKey" = "8:_3902BDCDF8E8B9815A577F205995E88E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE68471BEED19124E91850C068EE681D"
        "OwnerKey" = "8:_A6AF3FAF2F203E52B718C8BEABFB71EB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE68471BEED19124E91850C068EE681D"
        "OwnerKey" = "8:_CD5FDA2787654598AF896EF94EF82FA2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE68471BEED19124E91850C068EE681D"
        "OwnerKey" = "8:_D5F64030570545BBB4655B7CC7288CB5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6F97B38982EAD1CEC2ECC527529427A8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0E9BF086371446C28CB59005F426C733"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_652941B2F6E3D1D206B0CFEC0B216B11"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F6C0CD14065843DAA2C01D689951ADE5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FCCF27C6E4DD48BDB5EEC17F21F18183"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0A571815C84E468EA8A1843DFB8739CA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_81BEC32DB6AF4F02BADFECC8EF984F8E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_7022F8202AEBC8054618744DCB134D35"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8F6AB9C24BED419386330133FA454C1B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DB7347ACB032EF5E1F868A8A9DAE34E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_060DD0C0ECDB2050EDC645393A52864A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8F8CD6BFD940872D6A5E9E4AB5CF1BC0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D24C080CC25663D08D163079216EE147"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F4B940144E41E686F10A1D1B61E94F3A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_55EA06484CDD1EBF44AFA45B425CF67E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_319D7A30A855F11A44434A46C57EFABE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E7BE391B25C46E76F611FC215D36A61D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E8D9F63FD8E02A95C345767FE460CE3D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5FD2E248D35B6DD51DDE49D456A545D4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_55F264174A45F91C09670C44B6ADCAA6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_45617518ABC5E63195C9B907914CD8A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E25EBFFB63842C699C26E8EA8A30EB54"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E8F664DCDCB35A26A6CC296A14651810"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A0B3A4DCBC52644FF4C0232F0D634800"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_31866AC0C94E55C346FDCA2DC8D73FF3"
        "MsmSig" = "8:_UNDEFINED"
        }
    }
    "Configurations"
    {
        "Debug"
        {
        "DisplayName" = "8:Debug"
        "IsDebugOnly" = "11:TRUE"
        "IsReleaseOnly" = "11:FALSE"
        "OutputFilename" = "8:Debug\\WaferAlignerSetup.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-2147483648"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
        }
        "Release"
        {
        "DisplayName" = "8:Release"
        "IsDebugOnly" = "11:FALSE"
        "IsReleaseOnly" = "11:TRUE"
        "OutputFilename" = "8:Release\\WaferAlignerSetup.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-2147483648"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
        }
    }
    "Deployable"
    {
        "CustomAction"
        {
        }
        "DefaultFeature"
        {
        "Name" = "8:DefaultFeature"
        "Title" = "8:"
        "Description" = "8:"
        }
        "ExternalPersistence"
        {
            "LaunchCondition"
            {
                "{A06ECF26-33A3-4562-8140-9B0E340D4F24}:_49B3B5EBF2DB4F0FBE21F62DF8CB4448"
                {
                "Name" = "8:.NET Framework"
                "Message" = "8:[VSDNETMSG]"
                "FrameworkVersion" = "8:.NETFramework,Version=v4.7.2"
                "AllowLaterVersions" = "11:FALSE"
                "InstallUrl" = "8:http://go.microsoft.com/fwlink/?LinkId=863262"
                }
            }
        }
        "File"
        {
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_029C85BFD51B4898B1063D587FD29B84"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Logging.EventSource, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_029C85BFD51B4898B1063D587FD29B84"
                    {
                    "Name" = "8:Microsoft.Extensions.Logging.EventSource.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Logging.EventSource.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_054075C1BFFB2189AB23548392ABA4C8"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_054075C1BFFB2189AB23548392ABA4C8"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.Abstractions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Configuration.Abstractions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_05A8E84DF65236EA05796094770AED94"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration.FileExtensions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_05A8E84DF65236EA05796094770AED94"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.FileExtensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Configuration.FileExtensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_060DD0C0ECDB2050EDC645393A52864A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Xml.XPath.XDocument, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_060DD0C0ECDB2050EDC645393A52864A"
                    {
                    "Name" = "8:System.Xml.XPath.XDocument.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Xml.XPath.XDocument.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0876D84086430509E41E51152E77E106"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CommunityToolkit.Mvvm, Version=8.2.0.0, Culture=neutral, PublicKeyToken=4aff67a105548ee2, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_0876D84086430509E41E51152E77E106"
                    {
                    "Name" = "8:CommunityToolkit.Mvvm.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CommunityToolkit.Mvvm.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0A571815C84E468EA8A1843DFB8739CA"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Configuration.Install, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_0A571815C84E468EA8A1843DFB8739CA"
                    {
                    "Name" = "8:System.Configuration.Install.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\System.Configuration.Install.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0E9BF086371446C28CB59005F426C733"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:WaferAligner, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_0E9BF086371446C28CB59005F426C733"
                    {
                    "Name" = "8:WaferAligner.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\WaferAligner.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0F9F80019D10366D94E3E592C8991303"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Logging, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_0F9F80019D10366D94E3E592C8991303"
                    {
                    "Name" = "8:Microsoft.Extensions.Logging.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Logging.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_18238076331A4C9190688680E4FE069E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Logging.Configuration, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_18238076331A4C9190688680E4FE069E"
                    {
                    "Name" = "8:Microsoft.Extensions.Logging.Configuration.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Logging.Configuration.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_19922708D7D91B9070B2E362FBE3D9C7"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:ZstdNet, Version=1.4.5.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_19922708D7D91B9070B2E362FBE3D9C7"
                    {
                    "Name" = "8:ZstdNet.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:ZstdNet.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_1CE8CDC04A6E333DDD41EF447FA991FC"
            {
            "SourcePath" = "8:Microsoft.JScript.tlb"
            "TargetName" = "8:Microsoft.JScript.tlb"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:2"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_1F3931817ACB47AE95E65F5F842370A9"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\MySQL\\softreg.sql"
            "TargetName" = "8:softreg.sql"
            "Tag" = "8:"
            "Folder" = "8:_7DDF7A29851941778A13162F8EA4BC0A"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_23A36623C3514FC39D7E7918ADBF5D96"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\ProductPara\\test1.xml"
            "TargetName" = "8:test1.xml"
            "Tag" = "8:"
            "Folder" = "8:_5235DAF71E854BC0B330C47EE670058A"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_254B8CCB382E4AB4B557BB1E099DAC94"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\WaferAligner.exe"
            "TargetName" = "8:WaferAligner.exe"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_277D7431A62748D18949AE9658785ECD"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\SerialCom.dll"
            "TargetName" = "8:SerialCom.dll"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2DA8C6B921F145CE8986BD343CF4043F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration.Json, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_2DA8C6B921F145CE8986BD343CF4043F"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.Json.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Configuration.Json.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_2F8E06972FDA4098B644D7A2764B1909"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\ModbusTcpAPI.lib"
            "TargetName" = "8:ModbusTcpAPI.lib"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_317BD0D6813E4FEB81381F4FD289A266"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\ProductPara\\1.xml"
            "TargetName" = "8:1.xml"
            "Tag" = "8:"
            "Folder" = "8:_5235DAF71E854BC0B330C47EE670058A"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_31866AC0C94E55C346FDCA2DC8D73FF3"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.StackTrace, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_31866AC0C94E55C346FDCA2DC8D73FF3"
                    {
                    "Name" = "8:System.Diagnostics.StackTrace.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.StackTrace.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_319D7A30A855F11A44434A46C57EFABE"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Serialization.Xml, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_319D7A30A855F11A44434A46C57EFABE"
                    {
                    "Name" = "8:System.Runtime.Serialization.Xml.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Serialization.Xml.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_325493729D304BFEB3E7CE84F7B6E0F4"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\System.Configuration.Install.xml"
            "TargetName" = "8:System.Configuration.Install.xml"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3902BDCDF8E8B9815A577F205995E88E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_3902BDCDF8E8B9815A577F205995E88E"
                    {
                    "Name" = "8:Microsoft.Extensions.Hosting.Abstractions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Hosting.Abstractions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_3B0E2EFE91F345C9A6879D946683498E"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\ModbusTcpAPI.dll"
            "TargetName" = "8:ModbusTcpAPI.dll"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3CF3851904240FE4AC6EAD004DD84A8B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:PLC.Inovance, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_3CF3851904240FE4AC6EAD004DD84A8B"
                    {
                    "Name" = "8:PLC.Inovance.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:PLC.Inovance.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_41714E969EDF4702BB591D3CB1A78D47"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:PLC.Base, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_41714E969EDF4702BB591D3CB1A78D47"
                    {
                    "Name" = "8:PLC.Base.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\PLC.Base.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4258A07C97984574B46320005057BF48"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:SunnyUI.Common, Version=3.5.0.0, Culture=neutral, PublicKeyToken=5a271fb7ba597231, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_4258A07C97984574B46320005057BF48"
                    {
                    "Name" = "8:SunnyUI.Common.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\SunnyUI.Common.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_441BB3C80DFE4B2AA4FDB0CDAA1C7FC3"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\CalPara\\产品2.xml"
            "TargetName" = "8:产品2.xml"
            "Tag" = "8:"
            "Folder" = "8:_5C72CB80A4964797B4A1E2382C235637"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_45617518ABC5E63195C9B907914CD8A1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.IO.Compression.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_45617518ABC5E63195C9B907914CD8A1"
                    {
                    "Name" = "8:System.IO.Compression.FileSystem.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.Compression.FileSystem.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_45A6C04DE5434D1598D4E75A3D15115A"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\CalPara\\产品3.xml"
            "TargetName" = "8:产品3.xml"
            "Tag" = "8:"
            "Folder" = "8:_5C72CB80A4964797B4A1E2382C235637"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_45CEBDB23F9A49518B1B2D5A550FFB5E"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\PLC.Inovance.pdb"
            "TargetName" = "8:PLC.Inovance.pdb"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_4703B794908242E19C1E514222E90BD3"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\SysPara - 副本\\ConfPara_Home.xml"
            "TargetName" = "8:ConfPara_Home.xml"
            "Tag" = "8:"
            "Folder" = "8:_6996E028A8C5477389F80295B701086C"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4AB7E9686F487BAE8291FF978E84C179"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Logging.EventLog, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_4AB7E9686F487BAE8291FF978E84C179"
                    {
                    "Name" = "8:Microsoft.Extensions.Logging.EventLog.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Logging.EventLog.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4C488CBEE9D24C948DAEAF47BD799221"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Logging.EventLog, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_4C488CBEE9D24C948DAEAF47BD799221"
                    {
                    "Name" = "8:Microsoft.Extensions.Logging.EventLog.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Logging.EventLog.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_4EFD151F2ED14254ADAED0FBEFD99606"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\SysPara - 副本\\ConfPara_Page2.xml"
            "TargetName" = "8:ConfPara_Page2.xml"
            "Tag" = "8:"
            "Folder" = "8:_6996E028A8C5477389F80295B701086C"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_55EA06484CDD1EBF44AFA45B425CF67E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Security.Cryptography.Algorithms, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_55EA06484CDD1EBF44AFA45B425CF67E"
                    {
                    "Name" = "8:System.Security.Cryptography.Algorithms.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Cryptography.Algorithms.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_55F264174A45F91C09670C44B6ADCAA6"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_55F264174A45F91C09670C44B6ADCAA6"
                    {
                    "Name" = "8:System.Net.Http.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Http.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_5628BE3525A348D08169ECC56935FAE0"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\SysPara\\ConfPara_Home.xml"
            "TargetName" = "8:ConfPara_Home.xml"
            "Tag" = "8:"
            "Folder" = "8:_C696B3C433974CC4BC749C05B0D39F54"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_578B458B5B1288A7DFD9D7CBC907DBFF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration.EnvironmentVariables, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_578B458B5B1288A7DFD9D7CBC907DBFF"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.EnvironmentVariables.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Configuration.EnvironmentVariables.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_58FE4E2EE3A64232AA1BA03467F9A85B"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\MySQL\\jyjh.sql"
            "TargetName" = "8:jyjh.sql"
            "Tag" = "8:"
            "Folder" = "8:_7DDF7A29851941778A13162F8EA4BC0A"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5A4758B680C2C6C2F7E2921BF1338D80"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Logging.EventSource, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_5A4758B680C2C6C2F7E2921BF1338D80"
                    {
                    "Name" = "8:Microsoft.Extensions.Logging.EventSource.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Logging.EventSource.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5BC2E1EDFB7246FF990F8A80B8D49F8E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CommunityToolkit.Mvvm, Version=8.2.0.0, Culture=neutral, PublicKeyToken=4aff67a105548ee2, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_5BC2E1EDFB7246FF990F8A80B8D49F8E"
                    {
                    "Name" = "8:CommunityToolkit.Mvvm.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\CommunityToolkit.Mvvm.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5DA4CE8E1D2C4B3DAAAC2AEEEE2D0A5F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Logging.Console, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_5DA4CE8E1D2C4B3DAAAC2AEEEE2D0A5F"
                    {
                    "Name" = "8:Microsoft.Extensions.Logging.Console.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Logging.Console.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_5F90ADF5198C4A2F94EFD5ECC6A7872B"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\WaferAligner.runtimeconfig.json"
            "TargetName" = "8:WaferAligner.runtimeconfig.json"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_5FC27F3A3381453A969D8259865D3A6D"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\StandardModbusApi.dll"
            "TargetName" = "8:StandardModbusApi.dll"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5FD2E248D35B6DD51DDE49D456A545D4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Net.Sockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_5FD2E248D35B6DD51DDE49D456A545D4"
                    {
                    "Name" = "8:System.Net.Sockets.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Sockets.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_652941B2F6E3D1D206B0CFEC0B216B11"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:SunnyUI, Version=3.5.0.0, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_652941B2F6E3D1D206B0CFEC0B216B11"
                    {
                    "Name" = "8:SunnyUI.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:SunnyUI.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_66B525429BFB3B8EDF8D9CDC5E76B60D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.FileSystemGlobbing, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_66B525429BFB3B8EDF8D9CDC5E76B60D"
                    {
                    "Name" = "8:Microsoft.Extensions.FileSystemGlobbing.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.FileSystemGlobbing.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_68EC67F6CE004E9293AF26E5F7FFA001"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\CalPara\\CalPara.xml"
            "TargetName" = "8:CalPara.xml"
            "Tag" = "8:"
            "Folder" = "8:_5C72CB80A4964797B4A1E2382C235637"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6998EBD1D8B04B98B6B6C25523DD78DF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Diagnostics, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_6998EBD1D8B04B98B6B6C25523DD78DF"
                    {
                    "Name" = "8:Microsoft.Extensions.Diagnostics.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Diagnostics.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_6CACAB74D0D04C72B1D26CC88EF05B63"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\CalPara\\产品6.xml"
            "TargetName" = "8:产品6.xml"
            "Tag" = "8:"
            "Folder" = "8:_5C72CB80A4964797B4A1E2382C235637"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6F97B38982EAD1CEC2ECC527529427A8"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Data.Common, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_6F97B38982EAD1CEC2ECC527529427A8"
                    {
                    "Name" = "8:System.Data.Common.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Data.Common.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7022F8202AEBC8054618744DCB134D35"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Ubiety.Dns.Core, Version=2.2.1.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_7022F8202AEBC8054618744DCB134D35"
                    {
                    "Name" = "8:Ubiety.Dns.Core.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Ubiety.Dns.Core.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_70CD6C441141F152379B1C89DAAA80EE"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:PLC.Base, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_70CD6C441141F152379B1C89DAAA80EE"
                    {
                    "Name" = "8:PLC.Base.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:PLC.Base.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_7125AA0C5BE3469DA293E5535C4B1F5F"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\WaferAligner.dll.config"
            "TargetName" = "8:WaferAligner.dll.config"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_7A36DF40AFE54841AA2DB790DCF60AF9"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\ProductPara\\test.xml"
            "TargetName" = "8:test.xml"
            "Tag" = "8:"
            "Folder" = "8:_5235DAF71E854BC0B330C47EE670058A"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7D260ED8A73141C1B89B143B762B382E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:MySql.Data, Version=8.0.30.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_7D260ED8A73141C1B89B143B762B382E"
                    {
                    "Name" = "8:MySql.Data.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\MySql.Data.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_81BEC32DB6AF4F02BADFECC8EF984F8E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:SunnyUI, Version=3.5.0.0, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_81BEC32DB6AF4F02BADFECC8EF984F8E"
                    {
                    "Name" = "8:SunnyUI.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\SunnyUI.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_82FDD6D0A71E9F5E45C65867F29EBD60"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.DependencyInjection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_82FDD6D0A71E9F5E45C65867F29EBD60"
                    {
                    "Name" = "8:Microsoft.Extensions.DependencyInjection.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.DependencyInjection.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8423CC19C1FDD793DCDBC6BE36CFCFEE"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.FileProviders.Physical, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8423CC19C1FDD793DCDBC6BE36CFCFEE"
                    {
                    "Name" = "8:Microsoft.Extensions.FileProviders.Physical.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.FileProviders.Physical.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_86197D63F6424942B2AE5FD74685A1D9"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Properties\\Resources.resx"
            "TargetName" = "8:Resources.resx"
            "Tag" = "8:"
            "Folder" = "8:_D6DB2B12F4844D728383DAC270CC99F4"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_88EBFA00D3424D8AB8B6AA81B8C587A7"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\SerialCom.lib"
            "TargetName" = "8:SerialCom.lib"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8BA258AEEE0955BE8879A146EB2C68BB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration.UserSecrets, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8BA258AEEE0955BE8879A146EB2C68BB"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.UserSecrets.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Configuration.UserSecrets.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8BDB3B1D826F70AB11675D9D9E2FC017"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:MySql.Data, Version=8.0.30.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8BDB3B1D826F70AB11675D9D9E2FC017"
                    {
                    "Name" = "8:MySql.Data.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:MySql.Data.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8BFCDD999D9BF5A198DB8C096A3D2D64"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration.Binder, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8BFCDD999D9BF5A198DB8C096A3D2D64"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.Binder.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Configuration.Binder.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8DD514D95956472C8842D7F8919CA8D3"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.FileProviders.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8DD514D95956472C8842D7F8919CA8D3"
                    {
                    "Name" = "8:Microsoft.Extensions.FileProviders.Abstractions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.FileProviders.Abstractions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8F6AB9C24BED419386330133FA454C1B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.JScript, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_8F6AB9C24BED419386330133FA454C1B"
                    {
                    "Name" = "8:Microsoft.JScript.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.JScript.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8F8CD6BFD940872D6A5E9E4AB5CF1BC0"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8F8CD6BFD940872D6A5E9E4AB5CF1BC0"
                    {
                    "Name" = "8:System.ValueTuple.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ValueTuple.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_91285A190004459693BF431F4D905A0C"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\SysPara\\ConfPara_Page3.xml"
            "TargetName" = "8:ConfPara_Page3.xml"
            "Tag" = "8:"
            "Folder" = "8:_C696B3C433974CC4BC749C05B0D39F54"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_95419F7C92F94046A61A03794DE79317"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Diagnostics.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_95419F7C92F94046A61A03794DE79317"
                    {
                    "Name" = "8:Microsoft.Extensions.Diagnostics.Abstractions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Diagnostics.Abstractions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_96012B4109C643EB8284990B3D5B3DD6"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Api.log"
            "TargetName" = "8:Api.log"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_995405911EF6E81A4BCBDD931A4D3929"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.FileProviders.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_995405911EF6E81A4BCBDD931A4D3929"
                    {
                    "Name" = "8:Microsoft.Extensions.FileProviders.Abstractions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.FileProviders.Abstractions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9F62AC9603614CED9684FE0B2A843607"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:ZstdNet, Version=1.4.5.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_9F62AC9603614CED9684FE0B2A843607"
                    {
                    "Name" = "8:ZstdNet.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\ZstdNet.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A0B3A4DCBC52644FF4C0232F0D634800"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.Tracing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_A0B3A4DCBC52644FF4C0232F0D634800"
                    {
                    "Name" = "8:System.Diagnostics.Tracing.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.Tracing.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A39F605A9D6C444EB568AD229E2FD949"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration.Binder, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_A39F605A9D6C444EB568AD229E2FD949"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.Binder.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Configuration.Binder.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_A5E6F285DCD54850879208CA48D4EA90"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\CalPara\\产品4.xml"
            "TargetName" = "8:产品4.xml"
            "Tag" = "8:"
            "Folder" = "8:_5C72CB80A4964797B4A1E2382C235637"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A6AF3FAF2F203E52B718C8BEABFB71EB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Diagnostics, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_A6AF3FAF2F203E52B718C8BEABFB71EB"
                    {
                    "Name" = "8:Microsoft.Extensions.Diagnostics.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Diagnostics.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_A7A69131CF844B4CA583FB94CF5893A9"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.JScript.xml"
            "TargetName" = "8:Microsoft.JScript.xml"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_A8486F2372EE47D393D168EA6858C207"
            {
            "SourcePath" = "8:..\\..\\WaferAligner.ico"
            "TargetName" = "8:WaferAligner.ico"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A96C539F4F7747E6B88350DACEB2CDFE"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration.CommandLine, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_A96C539F4F7747E6B88350DACEB2CDFE"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.CommandLine.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Configuration.CommandLine.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_B5B8DD088C09456888419B0E45A1D0A1"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\System.Management.xml"
            "TargetName" = "8:System.Management.xml"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_B80B147410B94913975D345594A8F3CA"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\StandardModbusApi.lib"
            "TargetName" = "8:StandardModbusApi.lib"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_BCB43F32D71FC84B00CC9372B0DFB3EA"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Logging.Debug, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_BCB43F32D71FC84B00CC9372B0DFB3EA"
                    {
                    "Name" = "8:Microsoft.Extensions.Logging.Debug.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Logging.Debug.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_BCB66B8D09C6A84223C2B1CD77629922"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:SunnyUI.Common, Version=3.5.0.0, Culture=neutral, PublicKeyToken=5a271fb7ba597231, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_BCB66B8D09C6A84223C2B1CD77629922"
                    {
                    "Name" = "8:SunnyUI.Common.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:SunnyUI.Common.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_BE87E6BFD412415ABCB282CC75F5B8C1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.FileProviders.Physical, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_BE87E6BFD412415ABCB282CC75F5B8C1"
                    {
                    "Name" = "8:Microsoft.Extensions.FileProviders.Physical.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.FileProviders.Physical.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C0F3A159D9EA4A939F0BAD090D71E3A2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Options.ConfigurationExtensions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C0F3A159D9EA4A939F0BAD090D71E3A2"
                    {
                    "Name" = "8:Microsoft.Extensions.Options.ConfigurationExtensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Options.ConfigurationExtensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_C2E8180C610648D4844F996610E17A21"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\CalPara\\产品5.xml"
            "TargetName" = "8:产品5.xml"
            "Tag" = "8:"
            "Folder" = "8:_5C72CB80A4964797B4A1E2382C235637"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_C4F10864C02F4A1EA3C338FC3BF1414E"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\PLC.Base.pdb"
            "TargetName" = "8:PLC.Base.pdb"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C84290DF2BCD0CBD62E9285AE9117CA4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration.CommandLine, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C84290DF2BCD0CBD62E9285AE9117CA4"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.CommandLine.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Configuration.CommandLine.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_CD5FDA2787654598AF896EF94EF82FA2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Hosting, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_CD5FDA2787654598AF896EF94EF82FA2"
                    {
                    "Name" = "8:Microsoft.Extensions.Hosting.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Hosting.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D1732C39B5804058AA99F82392A001B1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration.FileExtensions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_D1732C39B5804058AA99F82392A001B1"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.FileExtensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Configuration.FileExtensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D1FB0D33AF78ACFBCFA573904A10330B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_D1FB0D33AF78ACFBCFA573904A10330B"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Configuration.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D24C080CC25663D08D163079216EE147"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Threading.Overlapped, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_D24C080CC25663D08D163079216EE147"
                    {
                    "Name" = "8:System.Threading.Overlapped.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.Overlapped.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D5B7276BCCEA4B079A980BADFA00B662"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Logging, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_D5B7276BCCEA4B079A980BADFA00B662"
                    {
                    "Name" = "8:Microsoft.Extensions.Logging.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Logging.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D5F64030570545BBB4655B7CC7288CB5"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_D5F64030570545BBB4655B7CC7288CB5"
                    {
                    "Name" = "8:Microsoft.Extensions.Hosting.Abstractions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Hosting.Abstractions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D8F051496109496A846131C2A0ED4128"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:PLC.Inovance, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_D8F051496109496A846131C2A0ED4128"
                    {
                    "Name" = "8:PLC.Inovance.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\PLC.Inovance.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DA0BB685481D54439842637264420F91"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Logging.Configuration, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_DA0BB685481D54439842637264420F91"
                    {
                    "Name" = "8:Microsoft.Extensions.Logging.Configuration.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Logging.Configuration.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DA9FD68E285A4F66A0BF77E6544C8307"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Logging.Debug, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_DA9FD68E285A4F66A0BF77E6544C8307"
                    {
                    "Name" = "8:Microsoft.Extensions.Logging.Debug.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Logging.Debug.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DB7347ACB032EF5E1F868A8A9DAE34E6"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:netstandard, Version=2.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_DB7347ACB032EF5E1F868A8A9DAE34E6"
                    {
                    "Name" = "8:netstandard.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:netstandard.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DFD23A972917448B9D4431ED00DDAADD"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_DFD23A972917448B9D4431ED00DDAADD"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.Abstractions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Configuration.Abstractions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E25EBFFB63842C699C26E8EA8A30EB54"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_E25EBFFB63842C699C26E8EA8A30EB54"
                    {
                    "Name" = "8:System.IO.Compression.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.Compression.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E7BE391B25C46E76F611FC215D36A61D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Serialization.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_E7BE391B25C46E76F611FC215D36A61D"
                    {
                    "Name" = "8:System.Runtime.Serialization.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Serialization.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E8D9F63FD8E02A95C345767FE460CE3D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.InteropServices.RuntimeInformation, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_E8D9F63FD8E02A95C345767FE460CE3D"
                    {
                    "Name" = "8:System.Runtime.InteropServices.RuntimeInformation.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.InteropServices.RuntimeInformation.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E8F664DCDCB35A26A6CC296A14651810"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Globalization.Extensions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_E8F664DCDCB35A26A6CC296A14651810"
                    {
                    "Name" = "8:System.Globalization.Extensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Globalization.Extensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E9BC6DF1BE0391F2694C013D0D02C4CA"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Options.ConfigurationExtensions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_E9BC6DF1BE0391F2694C013D0D02C4CA"
                    {
                    "Name" = "8:Microsoft.Extensions.Options.ConfigurationExtensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Options.ConfigurationExtensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_EE441F3030F94F6BB4AB0A93C384EB42"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\SysPara - 副本\\ConfPara_Page3.xml"
            "TargetName" = "8:ConfPara_Page3.xml"
            "Tag" = "8:"
            "Folder" = "8:_6996E028A8C5477389F80295B701086C"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_EE72111447A448A4A39E143A292A261A"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\WaferAligner.pdb"
            "TargetName" = "8:WaferAligner.pdb"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_EF50A469EC0F7F2FFB8AD62556873D59"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Logging.Console, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_EF50A469EC0F7F2FFB8AD62556873D59"
                    {
                    "Name" = "8:Microsoft.Extensions.Logging.Console.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Logging.Console.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_F077E18D14C745CFB5163E98ADC115D3"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\WaferAligner.deps.json"
            "TargetName" = "8:WaferAligner.deps.json"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_F0EF5395F6954CF99F26AFF8AF4E9975"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\SysPara\\ConfPara_Page2.xml"
            "TargetName" = "8:ConfPara_Page2.xml"
            "Tag" = "8:"
            "Folder" = "8:_C696B3C433974CC4BC749C05B0D39F54"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F2031F8A12CC4FCAB4DB6C1DCA1F53A0"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_F2031F8A12CC4FCAB4DB6C1DCA1F53A0"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Configuration.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F2ECD6C04D2F494CB78750C527190C89"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration.EnvironmentVariables, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_F2ECD6C04D2F494CB78750C527190C89"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.EnvironmentVariables.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Configuration.EnvironmentVariables.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F3F664FB3A4009AE63C284EA2D976404"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration.Json, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_F3F664FB3A4009AE63C284EA2D976404"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.Json.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Configuration.Json.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F4A82C6E625C4CE4886BD80895570C72"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.FileSystemGlobbing, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_F4A82C6E625C4CE4886BD80895570C72"
                    {
                    "Name" = "8:Microsoft.Extensions.FileSystemGlobbing.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.FileSystemGlobbing.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F4B940144E41E686F10A1D1B61E94F3A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Security.SecureString, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_F4B940144E41E686F10A1D1B61E94F3A"
                    {
                    "Name" = "8:System.Security.SecureString.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.SecureString.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_F4FF5FB69FA74DDA8CECD571C62F9D3F"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\MySql.Data.xml"
            "TargetName" = "8:MySql.Data.xml"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F6C0CD14065843DAA2C01D689951ADE5"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Ubiety.Dns.Core, Version=2.2.1.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_F6C0CD14065843DAA2C01D689951ADE5"
                    {
                    "Name" = "8:Ubiety.Dns.Core.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Ubiety.Dns.Core.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_FB8C13FA077B4AB9A3354EEF15270C6F"
            {
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\CalPara\\产品1.xml"
            "TargetName" = "8:产品1.xml"
            "Tag" = "8:"
            "Folder" = "8:_5C72CB80A4964797B4A1E2382C235637"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FCCF27C6E4DD48BDB5EEC17F21F18183"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Management, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_FCCF27C6E4DD48BDB5EEC17F21F18183"
                    {
                    "Name" = "8:System.Management.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\System.Management.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FE0F2B09A78B4D9692EF0EAFDB871151"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.DependencyInjection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_FE0F2B09A78B4D9692EF0EAFDB871151"
                    {
                    "Name" = "8:Microsoft.Extensions.DependencyInjection.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.DependencyInjection.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FE2C26A1C9354229B52813D9997C0200"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Configuration.UserSecrets, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_FE2C26A1C9354229B52813D9997C0200"
                    {
                    "Name" = "8:Microsoft.Extensions.Configuration.UserSecrets.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:C:\\对准设备2\\对准设备软件\\WaferAligner\\Debug\\net6.0-windows7.0\\Microsoft.Extensions.Configuration.UserSecrets.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FE68471BEED19124E91850C068EE681D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Extensions.Diagnostics.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_FE68471BEED19124E91850C068EE681D"
                    {
                    "Name" = "8:Microsoft.Extensions.Diagnostics.Abstractions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Extensions.Diagnostics.Abstractions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
        }
        "FileType"
        {
        }
        "Folder"
        {
            "{3C67513D-01DD-4637-8A68-80971EB9504F}:_13DB55F04BF348D78DEDED0C87B14329"
            {
            "DefaultLocation" = "8:[ProgramFilesFolder][Manufacturer]\\[ProductName]"
            "Name" = "8:#1925"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:TARGETDIR"
                "Folders"
                {
                    "{9EF0B969-E518-4E46-987F-47570745A589}:_5235DAF71E854BC0B330C47EE670058A"
                    {
                    "Name" = "8:ProductPara"
                    "AlwaysCreate" = "11:FALSE"
                    "Condition" = "8:"
                    "Transitive" = "11:FALSE"
                    "Property" = "8:_1A02A2033D8F4BB9BD9D1AE664FDA68F"
                        "Folders"
                        {
                        }
                    }
                    "{9EF0B969-E518-4E46-987F-47570745A589}:_5C72CB80A4964797B4A1E2382C235637"
                    {
                    "Name" = "8:CalPara"
                    "AlwaysCreate" = "11:FALSE"
                    "Condition" = "8:"
                    "Transitive" = "11:FALSE"
                    "Property" = "8:_2902F38EF0B74CD799F3F7C67064B3F4"
                        "Folders"
                        {
                        }
                    }
                    "{9EF0B969-E518-4E46-987F-47570745A589}:_6996E028A8C5477389F80295B701086C"
                    {
                    "Name" = "8:SysPara - 副本"
                    "AlwaysCreate" = "11:FALSE"
                    "Condition" = "8:"
                    "Transitive" = "11:FALSE"
                    "Property" = "8:_E4CCF857D63446258C1F1943DDA3E5C4"
                        "Folders"
                        {
                        }
                    }
                    "{9EF0B969-E518-4E46-987F-47570745A589}:_7DDF7A29851941778A13162F8EA4BC0A"
                    {
                    "Name" = "8:MySQL"
                    "AlwaysCreate" = "11:FALSE"
                    "Condition" = "8:"
                    "Transitive" = "11:FALSE"
                    "Property" = "8:_A07BA1B81FE94868858AA2C9796B221A"
                        "Folders"
                        {
                        }
                    }
                    "{9EF0B969-E518-4E46-987F-47570745A589}:_C696B3C433974CC4BC749C05B0D39F54"
                    {
                    "Name" = "8:SysPara"
                    "AlwaysCreate" = "11:FALSE"
                    "Condition" = "8:"
                    "Transitive" = "11:FALSE"
                    "Property" = "8:_A7F7B26B6E6E42C392B37B1B5B0E9097"
                        "Folders"
                        {
                        }
                    }
                    "{9EF0B969-E518-4E46-987F-47570745A589}:_D6DB2B12F4844D728383DAC270CC99F4"
                    {
                    "Name" = "8:Properties"
                    "AlwaysCreate" = "11:FALSE"
                    "Condition" = "8:"
                    "Transitive" = "11:FALSE"
                    "Property" = "8:_B61153914ECF47488FA538B1C8DB653E"
                        "Folders"
                        {
                        }
                    }
                }
            }
            "{1525181F-901A-416C-8A58-119130FE478E}:_6BA91EC18B694868BFBF3F653A5F94A5"
            {
            "Name" = "8:#1916"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:DesktopFolder"
                "Folders"
                {
                }
            }
            "{1525181F-901A-416C-8A58-119130FE478E}:_AD84133D13B2485FBEC558D08C78C50B"
            {
            "Name" = "8:#1919"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:ProgramMenuFolder"
                "Folders"
                {
                }
            }
        }
        "LaunchCondition"
        {
        }
        "Locator"
        {
        }
        "MsiBootstrapper"
        {
        "LangId" = "3:2052"
        "RequiresElevation" = "11:FALSE"
        }
        "Product"
        {
        "Name" = "8:Microsoft Visual Studio"
        "ProductName" = "8:WaferAlignerSetup"
        "ProductCode" = "8:{CC3DA69D-646D-4463-A39F-5A9AB8A3EBDD}"
        "PackageCode" = "8:{80CF1D56-263F-4D1A-A509-6803A1D84273}"
        "UpgradeCode" = "8:{C51DBFB8-F6FC-4D58-B85D-2CA53751B7AF}"
        "AspNetVersion" = "8:2.0.50727.0"
        "RestartWWWService" = "11:FALSE"
        "RemovePreviousVersions" = "11:FALSE"
        "DetectNewerInstalledVersion" = "11:TRUE"
        "InstallAllUsers" = "11:FALSE"
        "ProductVersion" = "8:1.0.0"
        "Manufacturer" = "8:Ray Zhao"
        "ARPHELPTELEPHONE" = "8:"
        "ARPHELPLINK" = "8:"
        "Title" = "8:WaferAlignerSetup"
        "Subject" = "8:"
        "ARPCONTACT" = "8:Ray Zhao"
        "Keywords" = "8:"
        "ARPCOMMENTS" = "8:"
        "ARPURLINFOABOUT" = "8:"
        "ARPPRODUCTICON" = "8:"
        "ARPIconIndex" = "3:0"
        "SearchPath" = "8:"
        "UseSystemSearchPath" = "11:TRUE"
        "TargetPlatform" = "3:0"
        "PreBuildEvent" = "8:"
        "PostBuildEvent" = "8:"
        "RunPostBuildEvent" = "3:0"
        }
        "Registry"
        {
            "HKLM"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_0A0FCEC7198742DF902D6464E1000AFB"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_BD9AAD34C42A4CA592AFB71B8C26965E"
                            {
                            "Name" = "8:[Manufacturer]"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCU"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_D83E6B196B074057A73A9455D614C631"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_BBAC5549F2794569871C7E3DA978F620"
                            {
                            "Name" = "8:[Manufacturer]"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCR"
            {
                "Keys"
                {
                }
            }
            "HKU"
            {
                "Keys"
                {
                }
            }
            "HKPU"
            {
                "Keys"
                {
                }
            }
        }
        "Sequences"
        {
        }
        "Shortcut"
        {
            "{970C0BB2-C7D0-45D7-ABFA-7EC378858BC0}:_9E0EF992F1014ED1B93E482FD30D0616"
            {
            "Name" = "8:WaferAligner.exe快捷方式"
            "Arguments" = "8:"
            "Description" = "8:"
            "ShowCmd" = "3:1"
            "IconIndex" = "3:0"
            "Transitive" = "11:FALSE"
            "Target" = "8:_254B8CCB382E4AB4B557BB1E099DAC94"
            "Folder" = "8:_6BA91EC18B694868BFBF3F653A5F94A5"
            "WorkingFolder" = "8:_13DB55F04BF348D78DEDED0C87B14329"
            "Icon" = "8:_A8486F2372EE47D393D168EA6858C207"
            "Feature" = "8:"
            }
        }
        "UserInterface"
        {
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_554A0A24EFE94307B3444B62088A976B"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdUserInterface.wim"
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_7581333852464E99A7E8646C272C5FA9"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:1"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_C1CD47C8F3964BD6A5BC7317D8985C75"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:已完成"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "UpdateText"
                            {
                            "Name" = "8:UpdateText"
                            "DisplayName" = "8:#1058"
                            "Description" = "8:#1158"
                            "Type" = "3:15"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1258"
                            "DefaultValue" = "8:#1258"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_99EE7C2158694FA1A4FD695E4B5EADFE"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdBasicDialogs.wim"
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_A89DC3C2B2894355B295C8D8834D23B0"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:1"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_01CEA38EA8334BE0B5A390E3BF951DB4"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:安装文件夹"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "InstallAllUsersVisible"
                            {
                            "Name" = "8:InstallAllUsersVisible"
                            "DisplayName" = "8:#1059"
                            "Description" = "8:#1159"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_571175AC50074959B4D4DC447F6D66D2"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:欢迎使用"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_D891E53877614592A2A36B3DA04F7E13"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:确认安装"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_BF0E99F127FD4BAAAF5125561B45561B"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:1"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_1E7B235368174A9685649A907A23D6AB"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:进度"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_DD8819748970409EABAFEF8410A36B6B"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:2"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_733D0AEC34EC49D1BC05D6E599302937"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:进度"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_E8B636DADD714F01AC443FEE2E0061EA"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:2"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_0A1E8F67BDF24383B42E86D1BD30BB3F"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:欢迎使用"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_A5B7136E33464011AE22DC93BE452BDE"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:安装文件夹"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_ACED2199507B4E85B89C2AFA36F5E980"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:确认安装"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_ECE095F021154D15B093E7DA0A6CA60D"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:2"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_93F21943CD444B50A48A8C5BDDC0E498"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:已完成"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
        }
        "MergeModule"
        {
        }
        "ProjectOutput"
        {
        }
    }
}
