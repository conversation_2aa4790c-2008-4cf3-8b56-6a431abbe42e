# 部署到硬件环境检查清单

## 概述

本文档提供了将WaferAligner项目从开发环境部署到硬件连接环境的完整检查清单。确保在部署前完成所有检查项，以保证系统在硬件环境中正常运行。

## 🔧 必须修改的配置

### 1. **App.config 开发模式设置**
**文件位置**: `WaferAligner/App.config`

**修改内容**:
```xml
<!-- 将开发模式从 true 改为 false -->
<add key="DevelopmentMode" value="false" />
```

**说明**: 这是唯一必须修改的配置。设置为 `false` 后，系统将启用所有PLC和串口通信功能。

## 📋 需要确认的配置

### 2. **PLC IP地址配置**

#### **当前默认配置**:
| 连接名称 | IP地址 | 端口 | 用途 |
|---------|--------|------|------|
| XYRAxis | ************ | 502 | 主PLC (XYR轴控制) |
| ZAxis | ************ | 502 | Z轴控制 |
| LeftX | ************ | 502 | 左侧X轴相机 |
| LeftY | ************ | 502 | 左侧Y轴相机 |
| LeftZ | ************ | 502 | 左侧Z轴相机 |
| RightX | ************ | 502 | 右侧X轴相机 |
| RightY | ************ | 502 | 右侧Y轴相机 |
| RightZ | ************ | 502 | 右侧Z轴相机 |

#### **配置文件位置**:
1. **主配置文件**: `WaferAligner/appsettings.json`
2. **连接管理器**: `WaferAligner/Services/PlcConnectionManager.cs` (第210-217行)
3. **资源文件**: `PLC/PLC.Inovance/Properties/Resources.resx`
4. **硬编码配置**: `WaferAligner/InovancePLC/PLC.cs` (第145行)

#### **如何修改IP地址**:
如果实际PLC IP地址与默认配置不同，请修改 `appsettings.json`:
```json
{
  "PlcSettings": {
    "Connections": {
      "Main": {
        "Address": "您的实际IP地址",
        "Port": 502,
        "Timeout": 5000
      }
      // ... 其他连接配置
    }
  }
}
```

### 3. **串口设备配置**

#### **当前默认配置**:
```xml
<!-- App.config 中的串口配置 -->
<add key="DefaultComPort" value="1" />        <!-- 串口号：COM1 -->
<add key="DefaultBaudRate" value="115200" />  <!-- 波特率：115200 -->
<add key="DefaultDataBits" value="8" />       <!-- 数据位：8位 -->
<add key="DefaultStopBits" value="1" />       <!-- 停止位：1位 -->
<add key="DefaultParity" value="None" />      <!-- 校验位：无校验 -->
<add key="DefaultHandshake" value="None" />   <!-- 流控制：无 -->
<add key="SerialTimeout" value="5000" />      <!-- 串口超时：5秒 -->
<add key="SerialRetryCount" value="3" />      <!-- 重试次数：3次 -->
```

#### **串口轴配置说明**:
- **XYR轴使用串口通信** - 通过SerialCom.dll与运动控制器通信
- **轴地址映射**:
  - X轴: 地址2
  - Y轴: 地址1
  - R轴: 地址3

#### **部署前检查**:
- [ ] 确认串口设备已正确连接
- [ ] 确认串口号配置正确（默认COM1）
- [ ] 确认波特率配置正确（默认115200）
- [ ] 检查串口设备驱动程序已安装
- [ ] 验证设备管理器中串口设备状态正常

#### **如何修改串口配置**:
如果实际串口设备与默认配置不同，请修改 `App.config`:
```xml
<!-- 例如：使用COM3端口，波特率9600，偶校验 -->
<add key="DefaultComPort" value="3" />
<add key="DefaultBaudRate" value="9600" />
<add key="DefaultDataBits" value="8" />
<add key="DefaultStopBits" value="1" />
<add key="DefaultParity" value="Even" />      <!-- None/Odd/Even/Mark/Space -->
<add key="DefaultHandshake" value="None" />   <!-- None/XOnXOff/RTS/RTSXOnXOff -->
<add key="SerialTimeout" value="10000" />     <!-- 增加超时时间到10秒 -->
<add key="SerialRetryCount" value="5" />      <!-- 增加重试次数到5次 -->
```

#### **配置参数说明**:
| 参数 | 可选值 | 说明 |
|------|--------|------|
| **DefaultComPort** | 1-256 | COM端口号 |
| **DefaultBaudRate** | 9600, 19200, 38400, 57600, 115200等 | 通信速率 |
| **DefaultDataBits** | 5, 6, 7, 8 | 数据位数 |
| **DefaultStopBits** | 1, 1.5, 2 | 停止位数 |
| **DefaultParity** | None, Odd, Even, Mark, Space | 校验方式 |
| **DefaultHandshake** | None, XOnXOff, RTS, RTSXOnXOff | 流控制方式 |
| **SerialTimeout** | >0 | 超时时间(毫秒) |
| **SerialRetryCount** | ≥0 | 重试次数 |

### 4. **环境变量检查**
确保没有设置开发模式的环境变量:
```bash
# 确保这个环境变量没有设置为 "true"
WAFER_ALIGNER_DEV_MODE=false
```

## 🔍 部署前检查项

### 硬件连接检查
- [ ] 汇川PLC设备已上电并正常运行
- [ ] 网络连接正常，能够ping通PLC IP地址
- [ ] 串口设备已连接并被系统识别
- [ ] 所有轴的硬件设备已正确安装

### 网络配置检查
- [ ] 计算机与PLC在同一网段
- [ ] 防火墙允许502端口通信
- [ ] 网络延迟在可接受范围内

### 软件配置检查
- [ ] `DevelopmentMode` 已设置为 `false`
- [ ] PLC IP地址配置正确
- [ ] 串口配置正确
- [ ] 数据库连接字符串正确（如果使用）

## 🚀 部署步骤

### 1. 停止开发环境
- 关闭所有正在运行的开发实例
- 确保没有进程占用串口设备

### 2. 更新配置
- 修改 `App.config` 中的 `DevelopmentMode`
- 根据实际情况更新PLC IP地址
- 验证所有配置文件的正确性

### 3. 部署应用程序
- 编译Release版本
- 复制到目标环境
- 确保所有依赖项已正确部署

### 4. 启动验证
- 启动应用程序
- 观察启动日志
- 检查PLC连接状态

## ✅ 部署后验证

### 1. **启动验证**
- [ ] 程序启动无卡顿
- [ ] 无异常错误日志
- [ ] 界面正常显示

### 2. **连接状态验证**
- [ ] 日志显示PLC连接成功
- [ ] 各轴连接状态正常
- [ ] 串口设备通信正常

### 3. **功能验证**
- [ ] 选项卡切换流畅
- [ ] 各轴状态正确显示
- [ ] 轴控制功能正常
- [ ] 报警状态正确显示
- [ ] 位置信息实时更新

### 4. **性能验证**
- [ ] 界面响应流畅
- [ ] 无周期性卡顿
- [ ] 内存使用正常
- [ ] CPU占用率合理

## 🚨 常见问题排查

### 问题1: PLC连接失败
**症状**: 日志显示"汇川PLC连接失败"
**排查步骤**:
1. 检查网络连接: `ping PLC_IP_ADDRESS`
2. 检查防火墙设置
3. 验证PLC设备状态
4. 确认IP地址配置正确

### 问题2: 串口通信异常
**症状**: XYR轴状态不更新
**排查步骤**:
1. 检查串口设备连接
2. 验证设备管理器中的串口状态
3. 检查串口被其他程序占用
4. 确认串口参数配置

### 问题3: 界面仍然卡顿
**症状**: 选项卡切换或操作响应慢
**排查步骤**:
1. 确认 `DevelopmentMode = false`
2. 检查网络延迟
3. 查看错误日志
4. 监控系统资源使用

### 问题4: 轴状态显示异常
**症状**: 轴状态不正确或不更新
**排查步骤**:
1. 检查对应的PLC/串口连接
2. 验证轴设备硬件状态
3. 查看通信日志
4. 确认轴配置参数

## 📝 日志监控

### 关键日志信息
部署后需要关注以下日志信息:

#### **成功连接日志**:
```
汇川PLC连接成功: ************:502
轴0 完整初始化完成
选项卡切换完成，初始化在后台进行
```

#### **需要关注的警告**:
```
轴X等待PLC连接超时，继续使用基本模式
检查PLC连接状态时发生异常
```

#### **错误日志**:
```
汇川PLC连接失败: IP:PORT
轴初始化失败
串口通信异常
```

## 🔄 回滚计划

如果部署后发现问题，可以快速回滚:

### 临时回滚到开发模式
```xml
<!-- 紧急情况下，可临时设置为开发模式 -->
<add key="DevelopmentMode" value="true" />
```

### 完整回滚
1. 停止应用程序
2. 恢复原始配置文件
3. 重新部署之前的版本
4. 验证功能正常

## 📞 技术支持

如果在部署过程中遇到问题:

1. **收集信息**:
   - 错误日志
   - 系统配置
   - 硬件连接状态
   - 网络配置

2. **联系技术支持**:
   - 提供详细的错误描述
   - 附上相关日志文件
   - 说明已尝试的解决方案

## 📋 检查清单总结

**部署前必做**:
- [ ] 修改 `App.config` 中的 `DevelopmentMode` 为 `false`
- [ ] 确认PLC IP地址配置正确
- [ ] 验证硬件设备连接正常
- [ ] 测试网络连通性

**部署后必验证**:
- [ ] 程序启动正常
- [ ] PLC连接成功
- [ ] 各轴功能正常
- [ ] 界面响应流畅

**持续监控**:
- [ ] 定期检查日志
- [ ] 监控系统性能
- [ ] 验证功能稳定性

---

**注意**: 本检查清单基于界面卡顿修复后的代码版本。如果您使用的是不同版本，请根据实际情况调整检查项。
