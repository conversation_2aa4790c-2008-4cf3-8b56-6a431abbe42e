﻿using System;
using System.Threading;
using System.Threading.Tasks;

namespace WaferAligner.SerialControl.Interfaces
{
    /// <summary>
    /// 串口轴控制器接口
    /// </summary>
    public interface ISerialAxisController : IDisposable
    {
        /// <summary>
        /// 轴名称
        /// </summary>
        string AxisName { get; }
        
        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; }
        
        /// <summary>
        /// 位置变化事件
        /// </summary>
        event EventHandler<SerialAxisEventArgs> PositionChanged;
        
        /// <summary>
        /// 状态变化事件
        /// </summary>
        event EventHandler<SerialAxisEventArgs> StateChanged;
        
        /// <summary>
        /// 使能轴
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> EnableAxisAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取位置
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        Task<int> GetPositionAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 移动到指定位置
        /// </summary>
        /// <param name="position">目标位置</param>
        /// <param name="isRelative">是否为相对位置</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> MoveToPositionAsync(int position, bool isRelative = false, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 停止
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> StopAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 回零
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> HomeAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 清除错误
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> ClearErrorAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取运行状态
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        Task<int> GetRunStateAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取报警状态
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        Task<int> GetAlarmStateAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 设置运行速度
        /// </summary>
        /// <param name="speed">速度值</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> SetRunSpeedAsync(uint speed, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 设置点动速度
        /// </summary>
        /// <param name="speed">速度值</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> SetJogSpeedAsync(uint speed, CancellationToken cancellationToken = default);
    }
    
    /// <summary>
    /// 串口轴事件参数
    /// </summary>
    public class SerialAxisEventArgs : EventArgs
    {
        /// <summary>
        /// 轴名称
        /// </summary>
        public string AxisName { get; set; }
        
        /// <summary>
        /// 位置
        /// </summary>
        public int Position { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public int State { get; set; }
    }
}
