using System;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using JYJ001.App.Services.Common.Interfaces;
using JYJ001.App.Services.Common.Extension;
using Sunny.UI;
using System.Threading;

namespace WaferAligner.Common
{
    /// <summary>
    /// 基础窗体类
    /// 提供统一的UI功能、资源管理和生命周期控制
    /// </summary>
    public abstract class BaseForm : UIForm, IDisposable
    {
        private readonly ILoggingService _logger;
        private readonly WaferAligner.Common.ResourceManager _resourceManager;
        private volatile bool _isDisposed = false;
        private volatile bool _isInitialized = false;

        protected IServiceProvider ServiceProvider => CommonFun.host?.Services;

        /// <summary>
        /// 获取日志记录器
        /// </summary>
        protected ILoggingService Logger => _logger;

        /// <summary>
        /// 获取资源管理器
        /// </summary>
        protected WaferAligner.Common.ResourceManager ResourceManager => _resourceManager;

        /// <summary>
        /// 窗体是否已初始化
        /// </summary>
        protected bool IsInitialized => _isInitialized;

        public BaseForm()
        {
            try
            {
                // 获取服务
                _logger = ServiceProvider?.GetService<ILoggingService>();
                _resourceManager = ServiceProvider?.GetService<WaferAligner.Common.ResourceManager>();

                // 设置基本属性
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.MaximizeBox = false;
                this.MinimizeBox = false;
                this.StartPosition = FormStartPosition.CenterParent;

                // 注册事件
                this.Load += BaseForm_Load;
                this.FormClosing += BaseForm_FormClosing;
                this.FormClosed += BaseForm_FormClosed;

                _logger?.LogDebug($"创建窗体: {this.GetType().Name}", WaferAligner.EventIds.EventIds.Page_Initialize_Completed);
            }
            catch (Exception ex)
            {
                // 如果初始化失败，记录错误但不抛出异常
                Console.WriteLine($"BaseForm初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安全地在UI线程执行操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        /// <returns>操作是否成功执行</returns>
        protected bool SafeInvoke(Action action)
        {
            return this.SafeInvoke(action, _logger);
        }

        /// <summary>
        /// 异步安全地在UI线程执行操作
        /// </summary>
        protected Task<bool> SafeInvokeAsync(Action action)
        {
            return this.SafeInvokeAsync(action, _logger);
        }

        /// <summary>
        /// 安全地设置控件文本
        /// </summary>
        protected bool SafeSetText(Control control, string text)
        {
            return control.SafeSetText(text, _logger);
        }

        /// <summary>
        /// 注册需要管理的资源
        /// </summary>
        protected void RegisterResource(string name, IDisposable resource)
        {
            _resourceManager?.RegisterResource($"{this.GetType().Name}_{name}", resource);
        }

        /// <summary>
        /// 注册定时器
        /// </summary>
        protected void RegisterTimer(string name, System.Timers.Timer timer)
        {
            _resourceManager?.RegisterTimer($"{this.GetType().Name}_{name}", timer);
        }

        /// <summary>
        /// 注册BackgroundWorker，当窗体关闭时自动取消
        /// </summary>
        [Obsolete("BackgroundWorker已全部迁移至Task-based异步模式，请使用RegisterResource注册Task或CancellationTokenSource", false)]
        protected void RegisterBackgroundWorker(string name, BackgroundWorker worker)
        {
            _logger?.LogWarning($"窗体{this.GetType().Name}使用已过期的RegisterBackgroundWorker方法: {name}",
                WaferAligner.EventIds.EventIds.Deprecated_Api_Usage);
            _resourceManager?.RegisterBackgroundWorker($"{this.GetType().Name}_{name}", worker);
        }

        /// <summary>
        /// 注册CancellationTokenSource，当窗体关闭时自动取消并释放
        /// </summary>
        protected void RegisterCancellationTokenSource(string name, CancellationTokenSource cts)
        {
            if (cts == null) return;
            
            _resourceManager?.RegisterResource($"{this.GetType().Name}_CTS_{name}", new BasePage.CancellationTokenSourceWrapper(cts));
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        protected void ShowError(string message)
        {
            SafeInvoke(() =>
            {
                UIMessageBox.ShowError(message);
                _logger?.LogInformation($"显示错误消息: {message}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
            });
        }

        /// <summary>
        /// 显示信息消息
        /// </summary>
        protected void ShowInfo(string message)
        {
            SafeInvoke(() =>
            {
                UIMessageBox.ShowInfo(message);
                _logger?.LogInformation($"显示信息消息: {message}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
            });
        }

        /// <summary>
        /// 显示警告消息
        /// </summary>
        protected void ShowWarning(string message)
        {
            SafeInvoke(() =>
            {
                UIMessageBox.ShowWarning(message);
                _logger?.LogWarning($"显示警告消息: {message}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
            });
        }

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        protected bool ShowConfirm(string message)
        {
            if (!IsFormUsable())
                return false;

            bool result = false;
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(() =>
                {
                    result = CommonFun.ShowAskDialog2(message);
                    _logger?.LogInformation($"显示确认对话框: {message}, 结果: {result}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                }));
            }
            else
            {
                result = CommonFun.ShowAskDialog2(message);
                _logger?.LogInformation($"显示确认对话框: {message}, 结果: {result}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
            }
            return result;
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private async void BaseForm_Load(object sender, EventArgs e)
        {
            try
            {
                await OnInitializeAsync();
                _isInitialized = true;
                _logger?.LogDebug($"窗体初始化完成: {this.GetType().Name}", WaferAligner.EventIds.EventIds.Page_Initialize_Completed);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"窗体初始化失败: {this.GetType().Name}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                ShowError($"窗体初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 窗体关闭中事件
        /// </summary>
        private async void BaseForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_isDisposed) return;

            try
            {
                // 调用子类的清理方法
                var canClose = await OnClosingAsync();
                if (!canClose)
                {
                    e.Cancel = true;
                    return;
                }

                _logger?.LogDebug($"窗体开始关闭: {this.GetType().Name}", WaferAligner.EventIds.EventIds.Page_Closed);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"窗体关闭过程中发生错误: {this.GetType().Name}", WaferAligner.EventIds.EventIds.Page_Closed);
                // 即使出错也要允许关闭
            }
        }

        /// <summary>
        /// 窗体已关闭事件
        /// </summary>
        private void BaseForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            try
            {
                Dispose();
                _logger?.LogDebug($"窗体关闭完成: {this.GetType().Name}", WaferAligner.EventIds.EventIds.Page_Closed);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"窗体关闭清理失败: {this.GetType().Name}", WaferAligner.EventIds.EventIds.Page_Closed);
            }
        }

        /// <summary>
        /// 子类重写此方法实现异步初始化逻辑
        /// </summary>
        protected virtual Task OnInitializeAsync()
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// 子类重写此方法实现关闭前的清理逻辑
        /// </summary>
        /// <returns>返回true表示可以关闭，false表示取消关闭</returns>
        protected virtual Task<bool> OnClosingAsync()
        {
            return Task.FromResult(true);
        }

        /// <summary>
        /// 子类重写此方法实现自定义清理逻辑
        /// </summary>
        protected virtual void OnDispose()
        {
            // 子类可以重写此方法添加自定义清理逻辑
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public new void Dispose()
        {
            if (_isDisposed) return;

            _isDisposed = true;

            try
            {
                // 调用子类的清理方法
                OnDispose();

                // 注销事件
                this.Load -= BaseForm_Load;
                this.FormClosing -= BaseForm_FormClosing;
                this.FormClosed -= BaseForm_FormClosed;

                _logger?.LogDebug($"窗体资源释放完成: {this.GetType().Name}", WaferAligner.EventIds.EventIds.Page_Closed);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"窗体资源释放失败: {this.GetType().Name}", WaferAligner.EventIds.EventIds.Page_Closed);
            }
            finally
            {
                base.Dispose();
            }
        }

        /// <summary>
        /// 检查窗体是否可用
        /// </summary>
        protected bool IsFormUsable()
        {
            return !_isDisposed && !this.IsDisposed && this.IsHandleCreated;
        }

        /// <summary>
        /// 获取服务实例
        /// </summary>
        protected T GetService<T>() where T : class
        {
            try
            {
                return ServiceProvider?.GetService<T>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"获取服务失败: {typeof(T).Name}", WaferAligner.EventIds.EventIds.Unhandled_Exception);
                return null;
            }
        }

        /// <summary>
        /// 获取必需的服务实例
        /// </summary>
        protected T GetRequiredService<T>() where T : class
        {
            try
            {
                return ServiceProvider?.GetRequiredService<T>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"获取必需服务失败: {typeof(T).Name}", WaferAligner.EventIds.EventIds.Unhandled_Exception);
                throw;
            }
        }
    }
} 