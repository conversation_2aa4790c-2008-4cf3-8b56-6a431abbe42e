﻿using Aya.PLC.Base;
using CommunityToolkit.Mvvm.ComponentModel;
using JYJ001.App.Services.Common.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PLC.Inovance;
using PLC.Inovance.Client;
using System.Collections.Concurrent;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Threading.Channels;
using WaferAligner.Common;
using WaferAligner.EventIds;
using JYJ001.App.Services.Common.Extension;
using WaferAligner.Services;

namespace AlignerUI
{
    internal class AutoStepDescription
    {
        public int Step { set; get; }
        public string Description { set; get; }
    }
    internal class InitStepDescription
    {
        public int Step { set; get; }
        public string Description { set; get; }
    }

    internal class VisionCalStepDescription
    {
        public int Step { set; get; }
        public string Description { set; get; }
    }
    public partial class MainWindowViewModel : ObservableObject
    {
        private readonly IPlcInstance plcInstance;
        //private readonly IConfiguration configuration;
        private readonly ILoggingService _loggingService;
        private readonly IAxisEventService _axisEventService;
        private readonly IPlcVariableService _plcVariableService;
        private readonly IRecipeService _recipeService;
        private readonly IAlignerParaService _alignerParaService;
        private readonly IStatusUpdateService _statusUpdateService;

        private ObservableCollection<ErrorInformation> errorMessages = new();


        private List<AutoStepDescription> autoStepDescriptions = new();

        private List<InitStepDescription> initStepDescriptions = new();
        private List<VisionCalStepDescription> visionCalStepDescriptions = new();

        Channel<ErrorInformation> channel;
        // 根据Phase3重构计划，移除未使用的BackgroundWorker实例，完成Task-based异步模式迁移
        // private BackgroundWorker backgroundWorker = new();
        private CancellationTokenSource _cleanupCts = new CancellationTokenSource();
        private EventHandler<InvoanceVariableChangedEventArgs> _plcVariableChangedHandler;


        public MainWindowViewModel(
            IPlcInstance plcInstance, 
            ILoggingService loggingService = null,
            IAxisEventService axisEventService = null, 
            IPlcVariableService plcVariableService = null,
            IRecipeService recipeService = null,
            IAlignerParaService alignerParaService = null,
            IStatusUpdateService statusUpdateService = null)
        {
            this.plcInstance = plcInstance;
            
            // 通过构造函数参数获取服务
            _loggingService = loggingService;
            _axisEventService = axisEventService;
            _plcVariableService = plcVariableService;
            _recipeService = recipeService;
            _alignerParaService = alignerParaService;
            _statusUpdateService = statusUpdateService;

            AddRegistry();
            
            // 保存委托引用以便后续注销
            _plcVariableChangedHandler = PLCVariableChanged;
            plcInstance.AddNotification<InvoanceVariableChangedEventArgs>(_plcVariableChangedHandler);

            RegistryVariable();
        }


        private void ReportErrorMessage(ErrorInformation info)
        {

            //if (info.Process == ErrorProcess.Add)
            //{
            //    if (!ErrorMessages.Any(item => item.ErrorCode == info.ErrorCode))
            //    {
            //        System.Windows.Application.Current.Dispatcher.Invoke(() =>
            //        {
            //            ErrorMessages.Add(info);
            //            ErrorMessage = info.ToString();
            //        });
            //    }
            //}
            //else
            //{
            //    DeleteErrorMessage(info.ErrorCode);
            //    if (ErrorMessages.Count <= 0)
            //    {
            //        ErrorMessage = String.Empty;
            //    }
            ////}
        }

        private void DeleteErrorMessage(int code)
        {
            //System.Windows.Application.Current.Dispatcher.Invoke(() =>
            //{
            //    if (ErrorMessages.Any(item => item.ErrorCode == code))
            //    {
            //        // d_e the errormessage need to be deleted
            //        var d_e = ErrorMessages.Where(item => item.ErrorCode == code).FirstOrDefault();
            //        ErrorMessages.Remove(d_e);
            //        if (ErrorMessages.Count > 0)
            //        {
            //            ErrorMessage = ErrorMessages[0].ToString();
            //        }
            //    }
            //});

        }
        protected Dictionary<string, Type> MonitorVariables = new();
        public Dictionary<uint, string> DataMap = new();
        protected Dictionary<string, Action<object>> VariableChangeActions = new();
        private ConcurrentDictionary<string, ConcurrentBag<Action<object>>> ExportActions = new();

        public void RegistryAction(string name, Action<object> action)
        {
            if (ExportActions.TryGetValue(name, out var a))
            {
                a.Add(action);
            }
        }


        private void RegistryVariable()//AddRegistry
        {
            // 检查PLC实例是否为null（适用于测试或模拟环境）
            if (plcInstance == null)
            {
                _loggingService?.LogWarning($"PLC实例为null，跳过变量注册 - MainWindowViewModel", EventIds.Plc_Connection_Failed);
                return;
            }
            
            var registerResult = plcInstance.RegisterMonitorVariables(MonitorVariables, new NotificationSettings(TransMode.Cyclic, 200, 0));
            if (registerResult != null)
            {
                foreach (var item in registerResult)
                {
                    DataMap.Add(item.Key, item.Value.Item1);
                }
            }
            else
            {
                _loggingService?.LogError($"PLC变量注册失败", EventIds.Plc_Variable_Read_Failed);
            }


        }

        protected async void PLCVariableChanged(object? sender, InvoanceVariableChangedEventArgs e)
        {
            
            if (DataMap.TryGetValue(e.Handle, out var name))
            {
                try
                {
                    //if (VariableChangeActions.TryGetValue(e.Name, out var action))
                    //{
                    //    action(e.Value);
                    //}
                    if (ExportActions.TryGetValue(e.Name, out var exportAct))
                    {
                        foreach (var item in exportAct)
                        {
                            item?.Invoke(e.Value);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _loggingService.LogError($"PLC变量方法获取失败", EventIds.Plc_Variable_Read_Failed);

                    MessageBox.Show(ex.ToString());
                }
            }
            await Task.CompletedTask;
        }




        private async Task<bool> WritePLCVariable(string name, object value)
        {
            try
            {
                // 优先使用依赖注入的服务
                if (_plcVariableService != null)
                {
                    return await _plcVariableService.WriteVariableSafelyAsync(name, value);
                }
                
                // 作为回退方案，使用直接PLC访问
                // 检查PLC实例是否为null（适用于测试或模拟环境）
                if (plcInstance == null)
                {
                    _loggingService?.LogWarning($"PLC实例为null，跳过变量写入 - MainWindowViewModel, 变量: {name}", EventIds.Plc_Connection_Failed);
                    return false;
                }
                
                var ret = await Task.Run<bool>(async () =>
                {
                    return await plcInstance.WriteVariableAsync(new PLCVarWriteInfo { Name = name, Value = value }, cancle: CancellationToken.None);
                });

                if (!ret)
                {
                    _loggingService?.LogError($"PLC变量:{name}:{value.ToString()}写入失败", EventIds.Plc_Variable_Write_Failed);
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"PLC变量写入异常: {name}:{value}, 错误: {ex.Message}", EventIds.Plc_Variable_Write_Failed);
                return false;
            }
        }

        public void UnRegistryAction(string name)
        {
            try
            {
                if (ExportActions.TryRemove(name, out var actions))
                {
                    _loggingService?.LogInformation($"成功注销PLC变量监听: {name}", EventIds.Resource_Released);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"注销PLC变量监听失败: {name}, 错误: {ex.Message}", EventIds.Plc_Variable_Read_Failed);
            }
        }

        public void CleanUp()
        {
            try
            {
                // 1. 取消和释放CancellationTokenSource（替代BackgroundWorker）
                try
                {
                    // 已迁移：从BackgroundWorker改为CancellationTokenSource
                    if (_cleanupCts != null)
                    {
                        _cleanupCts.Cancel();
                        _cleanupCts.Dispose();
                        _cleanupCts = null;
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError($"清理CancellationTokenSource时发生错误: {ex.Message}", EventIds.Unhandled_Exception);
                }

                // 2. 清理PLC事件监听
                try
                {
                    if (_plcVariableChangedHandler != null)
                    {
                        // 使用反射调用RemoveNotification方法，因为它不在接口中
                        var removeNotificationMethod = plcInstance?.GetType().GetMethod("RemoveNotification");
                        if (removeNotificationMethod != null)
                        {
                            var genericMethod = removeNotificationMethod.MakeGenericMethod(typeof(InvoanceVariableChangedEventArgs));
                            genericMethod.Invoke(plcInstance, new object[] { _plcVariableChangedHandler });
                        }
                        _plcVariableChangedHandler = null;
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError($"移除PLC通知时发生错误: {ex.Message}", EventIds.Unhandled_Exception);
                }

                // 3. 清理所有导出的Action
                try
                {
                    ExportActions?.Clear();
                    VariableChangeActions?.Clear();
                    DataMap?.Clear();
                    MonitorVariables?.Clear();
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError($"清理集合时发生错误: {ex.Message}", EventIds.Unhandled_Exception);
                }

                // 4. 清理Channel
                try
                {
                    if (channel != null)
                    {
                        channel.Writer.Complete();
                        channel = null;
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError($"关闭Channel时发生错误: {ex.Message}", EventIds.Unhandled_Exception);
                }

                _loggingService?.LogInformation("MainWindowViewModel资源清理完成", EventIds.Resource_Released);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"MainWindowViewModel清理资源时发生错误: {ex.Message}", EventIds.Unhandled_Exception);
            }
        }
    }
}
    