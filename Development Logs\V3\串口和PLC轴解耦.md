# 串口和PLC轴解耦方案（最终版）

## 一、问题分析

当前WaferAligner项目中存在串口轴(XYR)和PLC轴(Z和相机轴)架构耦合问题：

1. **不同通信机制混用**：
   - XYR轴使用串口通信（SerialAxisViewModel + SerialAxisController）
   - Z轴和相机轴使用PLC通信（继承自GenericAxis和CommonAxis）

2. **架构不一致**：
   - 串口轴使用**组合模式**：SerialAxisViewModel使用ISerialAxisController
   - PLC轴使用**继承模式**：Z轴和相机轴继承自GenericAxis和CommonAxis

3. **代码重复和维护困难**：
   - 相同功能在两种实现中重复编写
   - 特殊功能和通用功能混合在一起
   - 使用分部类导致代码分散

## 二、解耦设计

### 总体方案

经过对当前代码的分析，发现项目中已经包含了大部分需要的组件：

1. **SerialControl文件夹**包含了完整的串口轴控制系统
2. **IPlcConnectionManager和PlcConnectionManager**已实现PLC连接管理
3. **Services文件夹**中包含了其他可利用的通用服务
4. **AxisViewModelFactory**已经实现完整的轴视图模型工厂

最简的解决方案是：

1. 保留现有**SerialControl**实现不变
2. 利用**现有PLC连接管理器**，创建轻量级**PLC通信组件**
3. 设计**统一的视图模型基类**，作为两种轴类型的基础
4. 更新现有**AxisViewModelFactory**，创建新的PLC轴实现

### 接口设计

```
┌───────────────┐    ┌───────────────┐
│  IAxisViewModel│◄───┤ 轴视图模型子类  │
└───────┬───────┘    └───────────────┘
        │
        │
┌───────┴───────┐
│AxisViewModelBase│
└───────┬───────┘
        │
        ├─────────────────┐
        │                 │
┌───────▼──────┐  ┌───────▼────────┐
│SerialAxisViewModel│  │PlcAxisViewModelBase│
└────────────┬─┘  └────────┬───────┘
            │              │
            │              ├──────────────────────┐
            │              │                      │
            │      ┌───────▼────────┐    ┌────────▼─────────┐
            │      │  ZAxisViewModel │    │CameraAxisViewModel│
            │      └────────────────┘    └──────────────────┘
            │
            │
┌───────────▼─────────┐   使用    ┌──────────────────┐
│ISerialAxisController├─────────►│SerialAxisController│
└─────────────────────┘          └──────────────────┘


            使用                 使用
┌─────────────────┐        ┌───────────────┐
│IPlcConnectionManager│◄──────┤PlcCommunication│
└─────────────────┘        └───────┬───────┘
                                   ▲
                                   │ 使用
                          ┌────────┴────────┐
                          │PlcAxisViewModelBase│
                          └─────────────────┘
```

## 三、接口与类定义

### 1. 视图模型基类

```csharp
/// <summary>
/// 轴视图模型的统一基类，提供通用功能
/// </summary>
public abstract class AxisViewModelBase : IAxisViewModel
{
    protected readonly ILoggingService _loggingService;
    
    // 通用状态属性
    public string AxisName { get; }
    public int CurrentPosition { get; protected set; }
    public bool IsMoving { get; protected set; }
    public bool IsHomed { get; protected set; }
    public bool IsConnected { get; protected set; }
    
    // 事件
    public event EventHandler<AxisPositionChangedEventArgs> PositionChanged;
    public event EventHandler<AxisStateChangedEventArgs> StateChanged;
    
    // 构造函数
    protected AxisViewModelBase(string axisName, ILoggingService loggingService)
    {
        AxisName = axisName;
        _loggingService = loggingService;
    }
    
    // 通用方法实现
    public virtual Task InitializeAsync() => Task.CompletedTask;
    
    // 抽象方法，需要子类实现
    public abstract Task<bool> MoveToPositionAsync(int position, bool isRelative = false);
    public abstract Task<bool> HomeAsync();
    public abstract Task<bool> StopAsync();
    public abstract Task<int> GetPositionAsync();
    
    // 事件触发方法
    protected virtual void OnPositionChanged(int position)
    {
        CurrentPosition = position;
        PositionChanged?.Invoke(this, new AxisPositionChangedEventArgs(AxisName, position));
    }
    
    protected virtual void OnStateChanged(bool isMoving, bool isHomed, bool isConnected)
    {
        IsMoving = isMoving;
        IsHomed = isHomed;
        IsConnected = isConnected;
        StateChanged?.Invoke(this, new AxisStateChangedEventArgs(AxisName, isMoving, isHomed, isConnected));
    }
}
```

### 2. PLC轴视图模型基类

```csharp
/// <summary>
/// PLC轴视图模型的基类，提供PLC轴特有功能
/// </summary>
public abstract class PlcAxisViewModelBase : AxisViewModelBase
{
    protected readonly PlcCommunication _plcCommunication;
    
    public PlcAxisViewModelBase(
        string axisName,
        PlcCommunication plcCommunication,
        ILoggingService loggingService) 
        : base(axisName, loggingService)
    {
        _plcCommunication = plcCommunication;
    }
    
    public override async Task<bool> MoveToPositionAsync(int position, bool isRelative = false)
    {
        if (!IsConnected)
        {
            _loggingService.LogWarning(WaferAligner.EventIds.EventIds.AxisNotConnected, $"{AxisName} 未连接，无法移动");
            return false;
        }
        
        if (IsMoving)
        {
            _loggingService.LogWarning(WaferAligner.EventIds.EventIds.AxisIsMoving, $"{AxisName} 正在移动中，无法执行新的移动指令");
            return false;
        }
        
        try
        {
            OnStateChanged(true, IsHomed, IsConnected);
            var result = await _plcCommunication.MoveToPositionAsync(AxisName, position, isRelative);
            
            if (result)
            {
                var newPosition = await _plcCommunication.GetPositionAsync(AxisName);
                OnPositionChanged(newPosition);
            }
            
            OnStateChanged(false, IsHomed, IsConnected);
            return result;
        }
        catch (Exception ex)
        {
            _loggingService.LogError(WaferAligner.EventIds.EventIds.AxisMoveFailed, ex, $"{AxisName} 移动失败: {ex.Message}");
            OnStateChanged(false, IsHomed, IsConnected);
            return false;
        }
    }
    
    public override async Task<bool> HomeAsync()
    {
        if (!IsConnected)
        {
            _loggingService.LogWarning(WaferAligner.EventIds.EventIds.AxisNotConnected, $"{AxisName} 未连接，无法回原点");
            return false;
        }
        
        try
        {
            OnStateChanged(true, false, IsConnected);
            var result = await _plcCommunication.HomeAsync(AxisName);
            
            if (result)
            {
                OnStateChanged(false, true, IsConnected);
                var newPosition = await _plcCommunication.GetPositionAsync(AxisName);
                OnPositionChanged(newPosition);
            }
            else
            {
                OnStateChanged(false, false, IsConnected);
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _loggingService.LogError(WaferAligner.EventIds.EventIds.AxisHomeFailed, ex, $"{AxisName} 回原点失败: {ex.Message}");
            OnStateChanged(false, false, IsConnected);
            return false;
        }
    }
    
    public override async Task<bool> StopAsync()
    {
        if (!IsConnected)
        {
            _loggingService.LogWarning(WaferAligner.EventIds.EventIds.AxisNotConnected, $"{AxisName} 未连接，无法停止");
            return false;
        }
        
        try
        {
            var result = await _plcCommunication.StopAsync(AxisName);
            OnStateChanged(false, IsHomed, IsConnected);
            return result;
        }
        catch (Exception ex)
        {
            _loggingService.LogError(WaferAligner.EventIds.EventIds.AxisStopFailed, ex, $"{AxisName} 停止失败: {ex.Message}");
            return false;
        }
    }
    
    public override async Task<int> GetPositionAsync()
    {
        if (!IsConnected)
        {
            _loggingService.LogWarning(WaferAligner.EventIds.EventIds.AxisNotConnected, $"{AxisName} 未连接，无法获取位置");
            return 0;
        }
        
        try
        {
            var position = await _plcCommunication.GetPositionAsync(AxisName);
            OnPositionChanged(position);
            return position;
        }
        catch (Exception ex)
        {
            _loggingService.LogError(WaferAligner.EventIds.EventIds.AxisGetPositionFailed, ex, $"{AxisName} 获取位置失败: {ex.Message}");
            return 0;
        }
    }
    
    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        
        IsConnected = _plcCommunication.IsConnected;
        if (IsConnected)
        {
            var position = await GetPositionAsync();
            OnPositionChanged(position);
        }
        
        OnStateChanged(false, false, IsConnected);
    }
}
```

### 3. PLC通信层

```csharp
/// <summary>
/// PLC通信接口
/// </summary>
public interface IPlcCommunication
{
    bool IsConnected { get; }
    
    Task<int> GetPositionAsync(string axisName);
    Task<bool> MoveToPositionAsync(string axisName, int position, bool isRelative = false);
    Task<bool> HomeAsync(string axisName);
    Task<bool> StopAsync(string axisName);
}

/// <summary>
/// PLC通信实现，使用已有的IPlcConnectionManager
/// </summary>
public class PlcCommunication : IPlcCommunication
{
    private readonly IPlcConnectionManager _plcConnectionManager;
    private readonly ILoggingService _loggingService;
    
    // PLC变量名映射表
    private readonly Dictionary<string, (string PositionVar, string MoveVar, string HomeVar, string StopVar, string StatusVar)> _axisVarMap;
    
    public bool IsConnected => _plcConnectionManager?.IsConnected ?? false;
    
    public PlcCommunication(
        IPlcConnectionManager plcConnectionManager,
        ILoggingService loggingService)
    {
        _plcConnectionManager = plcConnectionManager;
        _loggingService = loggingService;
        
        // 初始化PLC变量映射表，可以从配置读取
        _axisVarMap = new Dictionary<string, (string, string, string, string, string)>
        {
            // 根据实际PLC地址配置
            ["ZAxis"] = ("Z_POS", "Z_MOVE_TO", "Z_HOME", "Z_STOP", "Z_STATUS"),
            ["CameraAxis"] = ("CAM_POS", "CAM_MOVE_TO", "CAM_HOME", "CAM_STOP", "CAM_STATUS")
        };
    }
    
    public async Task<int> GetPositionAsync(string axisName)
    {
        if (!IsConnected || !_axisVarMap.ContainsKey(axisName))
            return 0;
            
        var posVar = _axisVarMap[axisName].PositionVar;
        var plc = _plcConnectionManager.GetPlcInstance();
        
        try
        {
            var position = await Task.Run(() => (int)plc.ReadVariable(posVar));
            return position;
        }
        catch (Exception ex)
        {
            _loggingService.LogError(WaferAligner.EventIds.EventIds.PlcReadError, ex, $"读取轴{axisName}位置失败：{ex.Message}");
            return 0;
        }
    }
    
    public async Task<bool> MoveToPositionAsync(string axisName, int position, bool isRelative = false)
    {
        if (!IsConnected || !_axisVarMap.ContainsKey(axisName))
            return false;
            
        var (posVar, moveVar, _, _, statusVar) = _axisVarMap[axisName];
        var plc = _plcConnectionManager.GetPlcInstance();
        
        try
        {
            // 如果是相对运动，需要先获取当前位置
            if (isRelative)
            {
                var currentPos = await GetPositionAsync(axisName);
                position = currentPos + position;
            }
            
            // 写入目标位置并启动运动
            await Task.Run(() => 
            {
                plc.WriteVariable(posVar, position);
                plc.WriteVariable(moveVar, 1); // 触发移动
            });
            
            // 等待运动完成
            bool isMoving = true;
            while (isMoving)
            {
                await Task.Delay(50); // 避免过度查询
                isMoving = (int)plc.ReadVariable(statusVar) == 1;
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _loggingService.LogError(WaferAligner.EventIds.EventIds.PlcWriteError, ex, $"轴{axisName}移动失败：{ex.Message}");
            return false;
        }
    }
    
    public async Task<bool> HomeAsync(string axisName)
    {
        if (!IsConnected || !_axisVarMap.ContainsKey(axisName))
            return false;
            
        var homeVar = _axisVarMap[axisName].HomeVar;
        var statusVar = _axisVarMap[axisName].StatusVar;
        var plc = _plcConnectionManager.GetPlcInstance();
        
        try
        {
            // 触发回原点
            await Task.Run(() => plc.WriteVariable(homeVar, 1));
            
            // 等待回原点完成
            bool isHoming = true;
            while (isHoming)
            {
                await Task.Delay(100);
                isHoming = (int)plc.ReadVariable(statusVar) == 2; // 假设状态2表示正在回原点
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _loggingService.LogError(WaferAligner.EventIds.EventIds.PlcHomeError, ex, $"轴{axisName}回原点失败：{ex.Message}");
            return false;
        }
    }
    
    public async Task<bool> StopAsync(string axisName)
    {
        if (!IsConnected || !_axisVarMap.ContainsKey(axisName))
            return false;
            
        var stopVar = _axisVarMap[axisName].StopVar;
        var plc = _plcConnectionManager.GetPlcInstance();
        
        try
        {
            // 触发停止
            await Task.Run(() => plc.WriteVariable(stopVar, 1));
            return true;
        }
        catch (Exception ex)
        {
            _loggingService.LogError(WaferAligner.EventIds.EventIds.PlcWriteError, ex, $"轴{axisName}停止失败：{ex.Message}");
            return false;
        }
    }
}
```

### 4. Z轴和相机轴实现

```csharp
/// <summary>
/// Z轴视图模型实现
/// </summary>
public class ZAxisViewModel : PlcAxisViewModelBase, IZAxisViewModel
{
    private readonly IAxisEventService _axisEventService;
    
    public ZAxisViewModel(
        PlcCommunication plcCommunication,
        ILoggingService loggingService,
        IAxisEventService axisEventService) 
        : base("ZAxis", plcCommunication, loggingService)
    {
        _axisEventService = axisEventService;
    }
    
    // 实现Z轴特有的方法
    public async Task<bool> WaitSaftyPositionAsync()
    {
        try
        {
            _loggingService.LogInformation(WaferAligner.EventIds.EventIds.AxisOperation, $"Z轴等待安全位置");
            
            // 实现Z轴特定的安全位置检查逻辑
            var position = await GetPositionAsync();
            bool isSafePosition = position >= 1000; // 假设1000是安全位置
            
            if (!isSafePosition)
            {
                return await MoveToPositionAsync(1000); // 移动到安全位置
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _loggingService.LogError(WaferAligner.EventIds.EventIds.AxisOperationFailed, ex, $"Z轴等待安全位置失败：{ex.Message}");
            return false;
        }
    }
    
    // 其他Z轴特有方法...
    
    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        
        // 订阅轴事件
        _axisEventService.SubscribeAxisEvent("ZAxis", OnAxisEvent);
    }
    
    private void OnAxisEvent(object sender, AxisEventArgs args)
    {
        // 处理轴事件
    }
}

/// <summary>
/// 相机轴视图模型实现
/// </summary>
public class CameraAxisViewModel : PlcAxisViewModelBase, ICameraAxisViewModel
{
    public CameraAxisViewModel(
        PlcCommunication plcCommunication,
        ILoggingService loggingService) 
        : base("CameraAxis", plcCommunication, loggingService)
    {
    }
    
    // 实现相机轴特有的方法
    public async Task<bool> MoveToWorkPositionAsync()
    {
        try
        {
            _loggingService.LogInformation(WaferAligner.EventIds.EventIds.AxisOperation, $"相机轴移动到工作位置");
            return await MoveToPositionAsync(500); // 假设500是工作位置
        }
        catch (Exception ex)
        {
            _loggingService.LogError(WaferAligner.EventIds.EventIds.AxisOperationFailed, ex, $"相机轴移动到工作位置失败：{ex.Message}");
            return false;
        }
    }
    
    // 其他相机轴特有方法...
}
```

### 5. 依赖注入配置

```csharp
public static class AxisServiceExtensions
{
    public static IServiceCollection AddAxisServices(this IServiceCollection services)
    {
        // 已有的SerialControl服务注册保持不变
        
        // 注册PLC通信服务
        services.AddSingleton<PlcCommunication>();
        
        // 注册轴视图模型
        services.AddSingleton<IZAxisViewModel, ZAxisViewModel>();
        services.AddSingleton<ICameraAxisViewModel, CameraAxisViewModel>();
        
        // 其他依赖注册...
        
        return services;
    }
}
```

## 四、实施替换清单

为确保原始功能的正确和完整，需要创建以下新类：

1. **基础架构：**
   - `AxisViewModelBase` - 所有轴视图模型的基类
   - `PlcAxisViewModelBase` - PLC轴视图模型的基类
   - `IPlcCommunication` - PLC通信接口
   - `PlcCommunication` - PLC通信实现类

2. **PLC轴实现：**
   - 新的 `ZAxisViewModel` - 继承自PlcAxisViewModelBase，替换现有ZAxisViewModel
   - 新的 `CameraAxisViewModel` - 继承自PlcAxisViewModelBase，替换现有CameralHoldAxisViewModel

3. **配置类：**
   - `AxisServiceExtensions` - 依赖注入配置扩展方法

4. **需要修改的现有类：**
   - `AxisViewModelFactory` - 仅修改CreateZAxisAsync、CreateLeftXAxisAsync等方法，使用新实现类

**特别注意事项：**

- 新的ZAxisViewModel必须保留所有原有特殊方法（如ZHomeStop、ZTakeForceStop和HomeOffset）
- 新的CameraAxisViewModel必须支持相机位置参数（"Left"或"Right"）
- PlcCommunication中的变量映射表要根据实际PLC变量配置正确映射

## 五、实施计划

### 阶段1：准备工作（1天）

1. 创建新分支
2. 编写单元测试用例，确保能验证重构后的功能
3. 确认当前功能的行为，记录下关键调用点

### 阶段2：基础架构实现（2天）

1. 创建轴视图模型基类（AxisViewModelBase）
2. 创建PLC轴视图模型基类（PlcAxisViewModelBase）
3. 实现PLC通信层（PlcCommunication）
4. 创建依赖注入配置扩展方法

### 阶段3：Z轴和相机轴重构（2天）

1. 实现新的Z轴视图模型
2. 实现新的相机轴视图模型
3. 更新AxisViewModelFactory
   - **无需重新实现完整的AxisViewModelFactory**
   - 只需修改现有工厂类中的CreateZAxisAsync、CreateLeftXAxisAsync等方法
   - 使其返回新实现的基于PlcAxisViewModelBase的视图模型实例
   - 其余工厂方法（如CreateXAxisAsync等串口相关方法）保持不变

### 阶段4：集成和测试（1-2天）

1. 替换现有Z轴和相机轴的实现
2. 运行测试用例，确保功能正确性
3. 解决可能出现的问题和边界情况

### 阶段5：验收和上线（1-2天）

1. 全面测试系统功能
2. 整理文档和说明
3. 合并到主分支

## 六、风险分析与应对措施

### 潜在风险

1. **特殊功能缺失**：Z轴和相机轴可能有未被识别的特殊功能
   - 对策：详细比对原有实现代码，确保所有特殊功能都被迁移

2. **PLC变量映射错误**：PLC变量名或地址配置错误
   - 对策：创建配置文件存储映射关系，便于调整；添加验证逻辑

3. **集成问题**：新旧代码混合使用可能产生兼容性问题
   - 对策：渐进式替换，先在测试环境验证

4. **性能降低**：不恰当的异步实现可能导致性能问题
   - 对策：添加性能测试，比较重构前后的性能差异

## 七、总结

此方案利用已有的SerialControl组件和PlcConnectionManager，专注于解决架构耦合问题，而不是重新实现已有功能。通过统一的视图模型基类和组合模式替代继承，提高代码的可维护性和可扩展性。

总体实施时间预计为7-9个工作日，风险可控，能够保证原有功能的完整性和正确性。 
    - 无需后期移除临时适配器 
    - 无需重新实现AxisViewModelFactory，只需修改特定方法 

## 八、实施评估与结论

### 1. 实施完成度
- 统一基类（AxisViewModelBase/PlcAxisViewModelBase）、PLC通信层（IPlcCommunication/PlcCommunication）已全部实现。
- 新版Z轴（ZAxisViewModelNew）和相机轴（CameraAxisViewModelNew）已开发，功能覆盖原有所有特殊方法。
- 工厂类（AxisViewModelFactory）已支持新旧实现的切换，兼容性良好。
- 事件、日志、依赖注入等配套机制已完善。

### 2. 与方案对比
- 设计方案中的主要目标和技术路线已全部落地。
- 变量映射、特殊功能迁移、架构解耦等关键点均已实现。
- 代码结构清晰，便于维护和扩展。

### 3. 风险与建议
- 需进一步在实际业务场景下做全量测试，验证边界情况和异常处理。
- 建议持续关注PLC变量配置变更、特殊工艺需求变化，及时调整映射表和扩展方法。
- 后续如有新轴类型或通信方式，可按本架构模式快速扩展。

### 4. 结论
本次串口与PLC轴解耦重构已基本完成，核心目标达成，系统架构显著优化。建议进入全面测试和验收阶段，确保所有功能在新架构下稳定运行。 