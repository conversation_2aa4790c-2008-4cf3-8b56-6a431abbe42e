using Sunny.UI;

namespace WaferAligner.Forms.Pages
{
    partial class FLoginPage
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            uiPanel1 = new UIPanel();
            lblTitle = new UILabel();
            pictureBox1 = new PictureBox();
            uiPanel2 = new UIPanel();
            uiAvatar1 = new UIAvatar();
            btnCancel = new UIButton();
            btnLogin = new UIButton();
            txtPassword = new UITextBox();
            txtUsername = new UITextBox();
            lblLoginTitle = new UILabel();
            uiPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).BeginInit();
            uiPanel2.SuspendLayout();
            SuspendLayout();
            // 
            // uiPanel1
            // 
            uiPanel1.Controls.Add(lblTitle);
            uiPanel1.Controls.Add(pictureBox1);
            uiPanel1.Dock = DockStyle.Top;
            uiPanel1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiPanel1.Location = new Point(0, 35);
            uiPanel1.Margin = new Padding(4, 5, 4, 5);
            uiPanel1.MinimumSize = new Size(1, 1);
            uiPanel1.Name = "uiPanel1";
            uiPanel1.Size = new Size(520, 80);
            uiPanel1.TabIndex = 0;
            uiPanel1.Text = null;
            uiPanel1.TextAlignment = ContentAlignment.MiddleCenter;
            // 
            // lblTitle
            // 
            lblTitle.BackColor = Color.Transparent;
            lblTitle.Font = new Font("微软雅黑", 18F, FontStyle.Bold, GraphicsUnit.Point);
            lblTitle.ForeColor = Color.FromArgb(48, 48, 48);
            lblTitle.Location = new Point(180, 10);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(290, 60);
            lblTitle.TabIndex = 1;
            lblTitle.Text = "晶圆对准设备";
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // pictureBox1
            // 
            pictureBox1.BackColor = Color.Transparent;
            pictureBox1.Image = WaferAligner.Properties.Resources.CETC透明图_彩色字体;
            pictureBox1.Location = new Point(50, 10);
            pictureBox1.Name = "pictureBox1";
            pictureBox1.Size = new Size(124, 60);
            pictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
            pictureBox1.TabIndex = 0;
            pictureBox1.TabStop = false;
            // 
            // uiPanel2
            // 
            uiPanel2.BackColor = Color.White;
            uiPanel2.Controls.Add(uiAvatar1);
            uiPanel2.Controls.Add(btnCancel);
            uiPanel2.Controls.Add(btnLogin);
            uiPanel2.Controls.Add(txtPassword);
            uiPanel2.Controls.Add(txtUsername);
            uiPanel2.Controls.Add(lblLoginTitle);
            uiPanel2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiPanel2.Location = new Point(50, 140);
            uiPanel2.Margin = new Padding(4, 5, 4, 5);
            uiPanel2.MinimumSize = new Size(1, 1);
            uiPanel2.Name = "uiPanel2";
            uiPanel2.RectColor = Color.LightGray;
            uiPanel2.Size = new Size(420, 400);
            uiPanel2.TabIndex = 1;
            uiPanel2.Text = null;
            uiPanel2.TextAlignment = ContentAlignment.MiddleCenter;
            // 
            // uiAvatar1
            // 
            uiAvatar1.BackColor = Color.Transparent;
            uiAvatar1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiAvatar1.ForeColor = Color.FromArgb(140, 140, 140);
            uiAvatar1.Location = new Point(176, 31);
            uiAvatar1.MinimumSize = new Size(1, 1);
            uiAvatar1.Name = "uiAvatar1";
            uiAvatar1.Size = new Size(60, 60);
            uiAvatar1.Style = UIStyle.Custom;
            uiAvatar1.TabIndex = 6;
            uiAvatar1.Text = "uiAvatar1";
            // 
            // btnCancel
            // 
            btnCancel.Cursor = Cursors.Hand;
            btnCancel.FillColor = Color.FromArgb(230, 80, 80);
            btnCancel.Font = new Font("微软雅黑", 12F, FontStyle.Regular, GraphicsUnit.Point);
            btnCancel.Location = new Point(230, 320);
            btnCancel.MinimumSize = new Size(1, 1);
            btnCancel.Name = "btnCancel";
            btnCancel.RectColor = Color.FromArgb(230, 80, 80);
            btnCancel.RectHoverColor = Color.FromArgb(235, 115, 115);
            btnCancel.RectPressColor = Color.FromArgb(202, 58, 58);
            btnCancel.RectSelectedColor = Color.FromArgb(202, 58, 58);
            btnCancel.Size = new Size(130, 45);
            btnCancel.TabIndex = 5;
            btnCancel.Text = "✗ 取消";
            btnCancel.Click += btnCancel_Click;
            // 
            // btnLogin
            // 
            btnLogin.Cursor = Cursors.Hand;
            btnLogin.Font = new Font("微软雅黑", 12F, FontStyle.Regular, GraphicsUnit.Point);
            btnLogin.Location = new Point(60, 320);
            btnLogin.MinimumSize = new Size(1, 1);
            btnLogin.Name = "btnLogin";
            btnLogin.Size = new Size(130, 45);
            btnLogin.TabIndex = 4;
            btnLogin.Text = "✓ 登录";
            btnLogin.Click += btnLogin_Click;
            // 
            // txtPassword
            // 
            txtPassword.Cursor = Cursors.IBeam;
            txtPassword.Font = new Font("微软雅黑", 12F, FontStyle.Regular, GraphicsUnit.Point);
            txtPassword.Location = new Point(60, 250);
            txtPassword.Margin = new Padding(4, 5, 4, 5);
            txtPassword.MinimumSize = new Size(1, 16);
            txtPassword.Name = "txtPassword";
            txtPassword.Padding = new Padding(5);
            txtPassword.PasswordChar = '*';
            txtPassword.ShowText = false;
            txtPassword.Size = new Size(300, 40);
            txtPassword.TabIndex = 3;
            txtPassword.TextAlignment = ContentAlignment.MiddleLeft;
            txtPassword.Watermark = "请输入密码";
            // 
            // txtUsername
            // 
            txtUsername.Cursor = Cursors.IBeam;
            txtUsername.Font = new Font("微软雅黑", 12F, FontStyle.Regular, GraphicsUnit.Point);
            txtUsername.Location = new Point(60, 190);
            txtUsername.Margin = new Padding(4, 5, 4, 5);
            txtUsername.MinimumSize = new Size(1, 16);
            txtUsername.Name = "txtUsername";
            txtUsername.Padding = new Padding(5);
            txtUsername.ShowText = false;
            txtUsername.Size = new Size(300, 40);
            txtUsername.TabIndex = 2;
            txtUsername.Text = "admin";
            txtUsername.TextAlignment = ContentAlignment.MiddleLeft;
            txtUsername.Watermark = "请输入用户名";
            // 
            // lblLoginTitle
            // 
            lblLoginTitle.BackColor = Color.Transparent;
            lblLoginTitle.Font = new Font("微软雅黑", 16F, FontStyle.Regular, GraphicsUnit.Point);
            lblLoginTitle.ForeColor = Color.FromArgb(48, 48, 48);
            lblLoginTitle.Location = new Point(60, 112);
            lblLoginTitle.Name = "lblLoginTitle";
            lblLoginTitle.Size = new Size(300, 40);
            lblLoginTitle.TabIndex = 1;
            lblLoginTitle.Text = "用户登录";
            lblLoginTitle.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // FLoginPage
            // 
            AutoScaleMode = AutoScaleMode.None;
            ClientSize = new Size(520, 580);
            Controls.Add(uiPanel2);
            Controls.Add(uiPanel1);
            Font = new Font("微软雅黑", 12F, FontStyle.Regular, GraphicsUnit.Point);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "FLoginPage";
            ShowIcon = false;
            ShowInTaskbar = false;
            Text = "V1.0";
            TitleColor = Color.Gray;
            TitleFont = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point);
            ZoomScaleRect = new Rectangle(22, 22, 520, 580);
            FormClosing += FLoginPage_FormClosing;
            Load += FLoginPage_Load;
            uiPanel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)pictureBox1).EndInit();
            uiPanel2.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private Sunny.UI.UIPanel uiPanel1;
        private System.Windows.Forms.PictureBox pictureBox1;
        private Sunny.UI.UILabel lblTitle;
        private Sunny.UI.UIPanel uiPanel2;
        private Sunny.UI.UILabel lblLoginTitle;
        private Sunny.UI.UITextBox txtUsername;
        private Sunny.UI.UITextBox txtPassword;
        private Sunny.UI.UIButton btnLogin;
        private Sunny.UI.UIButton btnCancel;
        private UIAvatar uiAvatar1;
    }
} 