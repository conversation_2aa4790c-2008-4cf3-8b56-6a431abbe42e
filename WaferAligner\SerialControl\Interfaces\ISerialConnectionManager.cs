﻿using System;
using System.Threading;
using System.Threading.Tasks;

namespace WaferAligner.SerialControl.Interfaces
{
    /// <summary>
    /// 串口连接管理接口
    /// </summary>
    public interface ISerialConnectionManager : IDisposable
    {
        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; }
        
        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event EventHandler<SerialConnectionEventArgs> ConnectionStateChanged;
        
        /// <summary>
        /// 初始化
        /// </summary>
        Task InitializeAsync();
        
        /// <summary>
        /// 连接串口
        /// </summary>
        /// <param name="comPort">串口号</param>
        /// <param name="baudRate">波特率</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> ConnectAsync(int comPort, int baudRate, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 断开连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> DisconnectAsync(CancellationToken cancellationToken = default);
    }
    
    /// <summary>
    /// 串口连接事件参数
    /// </summary>
    public class SerialConnectionEventArgs : EventArgs
    {
        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected { get; set; }
    }
}
