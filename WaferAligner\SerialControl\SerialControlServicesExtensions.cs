using JYJ001.App.Services.Common.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using System;
using WaferAligner.Common;
using WaferAligner.SerialControl.Implementation;
using WaferAligner.SerialControl.Interfaces;
using WaferAligner.SerialControl.Models;

namespace WaferAligner.SerialControl
{
    /// <summary>
    /// 串口控制服务扩展
    /// </summary>
    public static class SerialControlServicesExtensions
    {
        /// <summary>
        /// 注册串口控制相关服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddSerialControlServices(this IServiceCollection services)
        {
            if (services == null)
            {
                throw new ArgumentNullException(nameof(services));
            }
            
            // 注册SerialComWrapper为单例
            services.AddSingleton<ISerialComWrapper, SerialComWrapper>();
            
            // 注册串口连接管理器为单例
            services.AddSingleton<ISerialConnectionManager, SerialConnectionManager>();
            
            // 注册轴控制器工厂为单例
            services.AddSingleton<ISerialAxisControllerFactory, SerialAxisControllerFactory>();
            
            // 注册SerialAxisViewModel工厂方法
            services.AddTransient<SerialAxisViewModel>((provider) => {
                var factory = provider.GetRequiredService<ISerialAxisControllerFactory>();
                var loggingService = provider.GetRequiredService<ILoggingService>();
                
                // 这里默认创建X轴控制器，实际使用时应该通过参数指定
                var controller = factory.GetAxisController("X");
                return new SerialAxisViewModel(controller, loggingService);
            });
            
            // 注册ResourceManager（如果尚未注册）
            if (services.BuildServiceProvider().GetService<WaferAligner.Common.ResourceManager>() == null)
            {
                services.AddSingleton<WaferAligner.Common.ResourceManager>();
            }
            
            // 注册测试类
            services.AddTransient<SerialControlTest>();
            
            // 注册SerialAxisViewModel测试类
            services.AddTransient<SerialAxisViewModelTest>();
            
            return services;
        }
    }
} 