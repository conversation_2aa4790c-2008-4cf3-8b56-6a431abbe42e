# XyrAxisViewModel迁移说明文档

## 一、原始设计与用途

`XyrAxisViewModel`是WaferAligner项目中最初用于控制X、Y、R三轴运动的视图模型类。该类位于`WaferAligner/InovancePLC/Axis/XyrAxisViewModel.cs`，主要功能包括：

1. 通过串口与运动控制器通信，实现轴的位置控制
2. 提供轴使能、位置读取、位置设置、轴运动、回零等基本功能
3. 实现轴状态监控，包括连接状态、使能状态、报警状态等
4. 支持点动操作和速度设置

虽然该类命名中包含"PLC"，但实际上它是通过SerialCom.dll与串口通信，而非与PLC通信。这种命名和目录结构的混用导致了架构上的混乱，是串口整改的主要原因之一。

## 二、迁移到SerialAxisViewModel

在Phase 3重构计划中，我们将`XyrAxisViewModel`的功能迁移到了新的`SerialAxisViewModel`类，主要改进包括：

1. **架构清晰化**：
   - 将串口通信相关代码移至`WaferAligner/SerialControl`目录
   - 明确分离串口通信和PLC通信层

2. **接口抽象化**：
   - 创建`ISerialAxisController`接口抽象轴控制功能
   - 创建`ISerialComWrapper`接口封装SerialCom.dll调用
   - `SerialAxisViewModel`通过`ISerialAxisController`实现轴控制

3. **异步支持**：
   - 所有阻塞操作都实现为异步方法
   - 支持CancellationToken和超时控制

4. **资源管理规范化**：
   - 实现IDisposable接口，确保资源正确释放
   - 使用ResourceManager注册资源

5. **日志记录规范化**：
   - 使用ILoggingService进行日志记录
   - 使用WaferAligner.EventIds中定义的事件ID
   - 实现日志抑制机制，避免日志刷屏

6. **状态更新优化**：
   - 实现状态更新节流，减少频繁更新
   - 添加锁机制确保线程安全

## 三、依赖移除策略

为了完全移除对`XyrAxisViewModel`的依赖，同时保留原始代码以供参考，我们采取以下策略：

1. **保留原始文件**：
   - 保留`XyrAxisViewModel.cs`在原位置
   - 添加废弃标记和注释，说明该类已被`SerialAxisViewModel`替代

2. **移除工厂中的依赖**：
   - 修改`AxisViewModelFactory`，移除对`XyrAxisViewModel`的直接实例化
   - 在回退路径中使用`SerialAxisViewModel`的空实现或Mock对象

3. **移除适配器**：
   - 移除`XyrAxisViewModelAdapter`类，它是连接旧实现和新接口的桥梁
   - 确保所有使用适配器的代码都直接使用`SerialAxisViewModel`

4. **配置调整**：
   - 移除`UseSerialControl`配置开关，默认始终使用新实现
   - 保留配置读取代码但使其无效，添加注释说明

5. **静态引用清理**：
   - 清理`CommonVariable`中的静态引用
   - 添加废弃警告，引导使用依赖注入方式获取轴实例

## 四、原始API参考

为便于将来参考，以下是`XyrAxisViewModel`的主要方法和属性：

```csharp
// 构造函数
public XyrAxisViewModel(string axisName)

// 属性
public string axisIndex { get; }
public bool IsConnected { get; }
public bool IsReady { get; }
public bool IsEnabled { get; }
public bool HasError { get; }
public bool Arrive_position { get; }

// 连接管理
public int Connect(string address, int port)
public Task<bool> ConnectAsync(string address, int port)

// 轴操作
public int EnableAxis()
public int GetPosition()
public int SetPosition(int position)
public int GoPosition()
public int Stop()
public int Home()
public int ClearMotorError()

// 点动操作
public void JOF_F_Start()
public void JOG_B_Start()
public void JOG_Stop()

// 速度设置
public int SetJogSpeed(uint speed)
public int SetRunSpeed(uint speed)
public int GetJogSpeed()
public int GetSpeed()

// 状态查询
public int GetRunState()
public int GetEnableState()
public int GetAlarmState()
```

## 五、迁移后的依赖关系

迁移完成后，依赖关系如下：

1. UI页面 → IAxisViewModelFactory → SerialAxisViewModel → ISerialAxisController → SerialAxisController → ISerialComWrapper → SerialComWrapper → SerialCom.dll

这种依赖链更加清晰，每一层都有明确的职责，且通过接口实现了解耦。

## 六、注意事项

1. 原始`XyrAxisViewModel`类中的部分实现细节可能在新实现中有所调整，特别是异常处理和资源管理方面
2. 如需参考原始实现，请查看`XyrAxisViewModel.cs`文件，但不要在新代码中直接使用该类
3. 所有需要轴控制功能的代码应通过`IAxisViewModelFactory`获取实例，而不是直接实例化任何具体类

**文档创建日期**：2024年7月
**适用版本**：WaferAligner Phase 3 