﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace WaferAligner.Interfaces
{
    /// <summary>
    /// 串口轴专用视图模型接口，提供串口轴特有功能
    /// </summary>
    public interface ISerialAxisViewModel : IXyrAxisViewModel
    {
        /// <summary>
        /// 初始化串口轴
        /// </summary>
        Task<bool> InitAxisAsync();
        
        /// <summary>
        /// 获取轴的使能状态
        /// </summary>
        Task<int> GetEnableStateAsync();
        
        /// <summary>
        /// 获取轴的报警状态
        /// </summary>
        Task<int> GetAlarmStateAsync();
        
        /// <summary>
        /// 获取轴的运行状态
        /// </summary>
        Task<int> GetRunStateAsync();
        
        /// <summary>
        /// 获取轴的当前速度
        /// </summary>
        Task<int> GetCurrentSpeedAsync();
        
        /// <summary>
        /// 获取轴的点动速度
        /// </summary>
        Task<int> GetJogSpeedAsync();
    }
}
