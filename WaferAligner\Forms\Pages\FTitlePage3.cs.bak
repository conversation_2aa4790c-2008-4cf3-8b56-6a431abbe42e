using AlignerUI;
using Aya.PLC.Base;
using JYJ001.App.Services.Common.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PLC.Inovance;
using PLC.Inovance.Client;
using Sunny.UI.Win32;
using System.ComponentModel;
using System.Linq.Expressions;
using System.Security.Cryptography;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using WaferAligner;
using WaferAligner.EventIds;
using WaferAligner.CustomClass;
using JYJ001.App.Services.Common.Extension;
using WaferAligner.Common;
using WaferAligner.Services;
using WaferAligner.Interfaces;

namespace Sunny.UI.Demo
{
    public partial class FTitlePage3 : BasePage
    {
        private ILoggingService _loggingService;
        private IAxisEventService _axisEventService;
        private IPlcVariableService _plcVariableService;
        private IUIUpdateService _uiUpdateService;
        private IMainWindowViewModel _mainWindowViewModel;
        private CancellationTokenSource _cts = new CancellationTokenSource();  // 添加取消令牌源
        private volatile bool _isClosing = false;  // 添加关闭标志，使用volatile确保线程安全
        private IAxisViewModelFactory _axisFactory;
        private ICylinderService _cylinderService;
        private IPlcConnectionManager _plcConnectionManager;
        #region  Load
        public FTitlePage3()
        {
            try
            {
                InitializeComponent();

                _loggingService = CommonFun.host.Services.GetService<ILoggingService>();
                _axisEventService = GetService<IAxisEventService>();
                _plcVariableService = GetService<IPlcVariableService>();
                _uiUpdateService = GetService<IUIUpdateService>();
                _mainWindowViewModel = GetService<IMainWindowViewModel>();
                _axisFactory = GetService<IAxisViewModelFactory>();
                _cylinderService = GetService<ICylinderService>();
                _plcConnectionManager = GetService<IPlcConnectionManager>();

                LoadInitialConfigsAsync();

                _loggingService.LogInformation("服务和配置初始化完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"页面初始化失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 安全获取服务的辅助方法
        private T GetService<T>() where T : class
        {
            try
            {
                if (CommonFun.host?.Services != null)
                {
                    return CommonFun.host.Services.GetService<T>();
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"获取服务 {typeof(T).Name} 失败: {ex.Message}", EventIds.Service_Unavailable);
            }
            return null;
        }

        /// <summary>
        /// 异步加载初始化配置文件
        /// </summary>
        private async Task LoadInitialConfigsAsync()
        {
            try
            {
                // 加载标定文件
                string path = Application.StartupPath + "CalPara\\CalPara.json";
                bool fileExists = File.Exists(path);
                if (!fileExists)
                {
                    await SafeInvokeAsync(() => {
                        FHeaderMainFooter.frmM.LalSoftwareStateTest = "标定文件不存在！";
                        MessageBox.Show("标定文件不存在!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                        Application.Exit();
                    });
                    return;
                }

                bool res = await Task.Run(() => ReadFromCalJSON(path));
                if (res)
                {
                    await SafeInvokeAsync(() => {
                        FHeaderMainFooter.frmM.LalSoftwareStateTest = "标定文件打开成功";
                    });
                    _loggingService.LogInformation($"加载标定参数文件成功", EventIds.Load_Calibration_Config_Success);
                }
                else
                {
                    await SafeInvokeAsync(() => {
                        FHeaderMainFooter.frmM.LalSoftwareStateTest = "标定文件打开失败";
                        MessageBox.Show("标定文件打开失败!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    });
                    _loggingService.LogError($"加载标定参数文件发生错误", EventIds.Load_Calibration_Config_Failed);
                }

                // 加载系统文件
                path = Application.StartupPath + "SysPara\\ConfPara_Page3.json";
                fileExists = File.Exists(path);
                if (!fileExists)
                {
                    await SafeInvokeAsync(() => {
                        FHeaderMainFooter.frmM.LalSoftwareStateTest = "标定文件不存在！";
                        MessageBox.Show("运动界面的系统文件不存在!\r\n请退出软件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                        Application.Exit();
                    });
                    return;
                }

                res = await Task.Run(() => ReadFromSysJSON(path));
                if (res)
                {
                    await SafeInvokeAsync(() => {
                        FHeaderMainFooter.frmM.LalSoftwareStateTest = "运动界面的系统文件打开成功";
                    });
                    _loggingService.LogInformation($"加载运动界面的系统文件成功", EventIds.Load_Equip_Movement_Config_Success);
                }
                else
                {
                    await SafeInvokeAsync(() => {
                        FHeaderMainFooter.frmM.LalSoftwareStateTest = "运动界面的系统文件打开失败";
                        MessageBox.Show("加载运动界面的系统文件失败!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    });
                    _loggingService.LogError($"加载运动界面的系统文件失败", EventIds.Load_Equip_Movement_Config_Failed);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"加载配置文件时发生错误: {ex.Message}", EventIds.Configuration_Error);
            }
        }

        // 使用新的TimerWrapper替换原来的System.Timers.Timer
        private TimerWrapper _UpdateTimer = null;
        private TimerWrapper _CalibrateTimer = null;
        private TimerWrapper _InputTimer = null;  // 替换tmrInput
        private TimerWrapper _CalTimer = null;    // 替换timer1

        // 定义timer1字段以保持代码兼容性
        private System.Windows.Forms.Timer timer1 = null;

        // 定义tmrInput字段以保持代码兼容性
        private System.Windows.Forms.Timer tmrInput = null;

        protected override async Task OnInitializeAsync()
        {
            // 将timer1和tmrInput设置为null，使用_CalTimer和_InputTimer替代
            timer1 = null;
            tmrInput = null;

            try
            {
                // 检查用户是否有权限访问运动参数配置页面
                if (!CurrentUser.CanAccessMotionPage())
                {
                    _loggingService?.LogWarning($"用户 {CurrentUser.User?.Username ?? "未知"} ({CurrentUser.GetRoleDisplayName()}) 尝试访问运动参数页面但没有权限",
                        EventIds.UnhandledException);

                    // 显示权限不足的消息
                    ShowWarning($"您没有权限访问运动参数配置页面。\n\n当前角色：{CurrentUser.GetRoleDisplayName()}\n只有管理员和工程师可以配置参数。");

                    // 禁用当前页面
                    this.Enabled = false;
                    return;
                }

                _loggingService?.LogInformation($"用户 {CurrentUser.User?.Username ?? "未知"} 成功访问运动参数页面",
                    EventIds.Page_Initialize_Completed);

                uiTabControlMenu1.SelectedIndex = 0;

                // 修改：Z轴归零偏移设置改为后台执行，不阻塞UI线程
                if (_axisEventService == null)
                {
                    _loggingService?.LogError("轴事件服务未初始化，无法设置Z轴归零偏移。");
                }
                else if (TxtZOffset == null)
                {
                    _loggingService?.LogError("TxtZOffset控件未初始化，无法设置Z轴归零偏移。");
                }
                else
                {
                    double offset;
                    if (!double.TryParse(TxtZOffset.Text, out offset))
                    {
                        _loggingService?.LogError($"TxtZOffset.Text不是有效数字: {TxtZOffset.Text}");
                    }
                    else
                    {
                        _ = _axisEventService.SetZAxisHomeOffsetAsync(offset)
                            .ContinueWith(t => {
                                if (t.Exception != null)
                                    _loggingService?.LogError($"Z轴归零偏移设置失败: {t.Exception.Message}");
                                else
                                    _loggingService?.LogDebug("Z轴归零偏移设置成功");
                            }, TaskScheduler.Default);
                    }
                }

                // 修改：系统参数配置也在后台执行
                if (_mainWindowViewModel == null)
                {
                    _loggingService?.LogError("主窗口视图模型(_mainWindowViewModel)未初始化，无法设置系统参数。");
                }
                else if (TxtCameraOffset == null)
                {
                    _loggingService?.LogError("TxtCameraOffset控件未初始化，无法设置系统参数。");
                }
                else if (TxtBottomPhotoOffset == null)
                {
                    _loggingService?.LogError("TxtBottomPhotoOffset控件未初始化，无法设置系统参数。");
                }
                else if (TxtTopGap == null)
                {
                    _loggingService?.LogError("TxtTopGap控件未初始化，无法设置系统参数。");
                }
                else if (TxtBottomGap == null)
                {
                    _loggingService?.LogError("TxtBottomGap控件未初始化，无法设置系统参数。");
                }
                else
                {
                    double cameraOffset, bottomPhotoOffset, topGap, bottomGap;
                    if (!double.TryParse(TxtCameraOffset.Text, out cameraOffset))
                    {
                        _loggingService?.LogError($"TxtCameraOffset.Text不是有效数字: {TxtCameraOffset.Text}");
                    }
                    else if (!double.TryParse(TxtBottomPhotoOffset.Text, out bottomPhotoOffset))
                    {
                        _loggingService?.LogError($"TxtBottomPhotoOffset.Text不是有效数字: {TxtBottomPhotoOffset.Text}");
                    }
                    else if (!double.TryParse(TxtTopGap.Text, out topGap))
                    {
                        _loggingService?.LogError($"TxtTopGap.Text不是有效数字: {TxtTopGap.Text}");
                    }
                    else if (!double.TryParse(TxtBottomGap.Text, out bottomGap))
                    {
                        _loggingService?.LogError($"TxtBottomGap.Text不是有效数字: {TxtBottomGap.Text}");
                    }
                    else
                    {
                        _ = _mainWindowViewModel.SystemParameterExecuteAsync(
                            cameraOffset,
                            bottomPhotoOffset,
                            topGap,
                            bottomGap
                        ).ContinueWith(t => {
                            if (t.Exception != null)
                                _loggingService?.LogError($"系统参数配置失败: {t.Exception.Message}");
                            else
                                _loggingService?.LogDebug("系统参数配置成功");
                        }, TaskScheduler.Default);
                    }
                }

                // ? 检查PLC连接状态（使用兼容性服务）
                object plcInstance = null;
                if (_axisEventService == null)
                {
                    _loggingService?.LogError("_axisEventService未初始化，无法获取PLC实例。", EventIds.Plc_Connection_Failed);
                }
                else
                {
                    plcInstance = _axisEventService.GetMainPLCInstance();
                }
                if (plcInstance == null)
                {
                    _loggingService?.LogWarning("PLC实例为空，界面功能可能受限", EventIds.Plc_Connection_Failed);
                    FHeaderMainFooter.frmM.LalSoftwareStateTest = "PLC未初始化";
                }
                else
                {
                    _loggingService?.LogInformation("PLC连接正常，开始启动Timer", EventIds.Plc_Connection_Succeeded);
                }

                // 创建并配置_InputTimer替代tmrInput
                _InputTimer = new TimerWrapper("FTitlePage3_InputTimer", 200, _loggingService);
                _InputTimer.AddElapsedHandler((s, e) => {
                    SafeInvoke(() => TmrInput_Tick(s, e));
                });
                RegisterResource("InputTimer", _InputTimer);

                // 创建并配置_CalTimer替代timer1
                _CalTimer = new TimerWrapper("FTitlePage3_CalTimer", 100, _loggingService);
                _CalTimer.AddElapsedHandler((s, e) => {
                    SafeInvoke(() => Timer1_Tick(s, e));
                });
                RegisterResource("CalTimer", _CalTimer);

                // 使用新的TimerWrapper创建定时器
                _UpdateTimer = new TimerWrapper("FTitlePage3_UpdateTimer", 200, _Update_Tick, _loggingService);
                RegisterResource("UpdateTimer", _UpdateTimer);

                // 初始化_CalibrateTimer
                _CalibrateTimer = new TimerWrapper("FTitlePage3_CalibrateTimer", 2000, Calibrate_Tick, _loggingService);
                RegisterResource("CalibrateTimer", _CalibrateTimer);

                try
                {
                    if (!_isClosing)
                    {
                        // 启动更新定时器
                        if (_UpdateTimer != null && !_UpdateTimer.Enabled)
                            _UpdateTimer.Start();

                        // 启动输入定时器
                        if (_InputTimer != null && !_InputTimer.Enabled)
                            _InputTimer.Start();
                    }
                }
                catch (Exception ex)
                {
                    // 记录异常但继续执行
                    _loggingService?.LogError(ex, $"定时器启动失败: {ex.Message}", EventIds.Update_Timer_Start_Failed);
                }

                // 注册其他Timer到资源管理器
                if (_CalTimer != null)
                    RegisterResource("CalTimer", _CalTimer); // 使用_CalTimer替代timer1

                if (_InputTimer != null)
                    RegisterResource("InputTimer", _InputTimer); // 使用_InputTimer替代tmrInput

                // 已迁移到Task-based异步模式，不再使用BackgroundWorker
                // 注册取消令牌源清理操作
                ResourceManager?.RegisterCustomCleanup(() =>
                {
                    _calPosCts?.Cancel();
                    _calPosCts?.Dispose();
                    return Task.CompletedTask;
                });

                // 添加界面可见性变化事件处理
                this.VisibleChanged += FTitlePage3_VisibleChanged;

                await base.OnInitializeAsync();

                // 修改：将SysPareExcute移到这里，在所有UI初始化后执行
                // 使用Task.Delay确保UI完全加载后再执行
                _ = Task.Delay(100).ContinueWith(_ =>
                {
                    try
                    {
                        SysPareExcute();
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError(ex, "延迟执行SysPareExcute失败", EventIds.Configuration_Error);
                    }
                }, TaskScheduler.Default);

                // 服务注入：为每个AxisControl实例调用InitServices
                步进电机调试X.InitServices(_loggingService, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                步进电机调试Y.InitServices(_loggingService, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                步进电机调试Z.InitServices(_loggingService, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                步进电机调试LX.InitServices(_loggingService, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                步进电机调试LY.InitServices(_loggingService, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                步进电机调试LZ.InitServices(_loggingService, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                步进电机调试RX.InitServices(_loggingService, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                步进电机调试RY.InitServices(_loggingService, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                步进电机调试RZ.InitServices(_loggingService, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "FTitlePage3初始化时发生错误", EventIds.Configuration_Error);
            }
        }

        private void FTitlePage3_VisibleChanged(object sender, EventArgs e)
        {
            try
            {
                // 检查是否正在关闭
                if (_isClosing)
                {
                    _loggingService?.LogDebug("窗体正在关闭，跳过可见性处理", EventIds.Form_Closing_Skip_Visibility);
                    return;
                }

                if (this.Visible)
                {
                    // 界面变为可见时，恢复Timer
                    if (_InputTimer != null && !_InputTimer.Enabled && !_isClosing)
                    {
                        _InputTimer.Start();
                        _loggingService?.LogDebug("界面变为可见，恢复Timer", EventIds.Timer_Resumed);
                    }
                    if (_UpdateTimer != null && !_UpdateTimer.Enabled && !_isClosing)
                    {
                        try
                        {
                            _UpdateTimer.Start();
                            _loggingService?.LogDebug("界面变为可见，恢复XYR更新Timer", EventIds.Xyr_Timer_Resumed);
                        }
                        catch (Exception ex)
                        {
                            // 记录异常但继续执行
                            _loggingService?.LogDebug($"恢复XYR更新Timer失败: {ex.Message}", EventIds.Xyr_Timer_Resume_Error);
                        }
                    }
                }
                else
                {
                    // 界面变为不可见时，暂停Timer以节省资源
                    if (_InputTimer != null && _InputTimer.Enabled)
                    {
                        _InputTimer.Stop();
                        _loggingService?.LogDebug("界面变为不可见，暂停Timer", EventIds.Timer_Paused);
                    }
                    if (_UpdateTimer != null && _UpdateTimer.Enabled)
                    {
                        _UpdateTimer.Stop();
                        _loggingService?.LogDebug("界面变为不可见，暂停XYR更新Timer", EventIds.Xyr_Timer_Paused);
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"处理界面可见性变化时发生错误: {ex.Message}", EventIds.Visible_Changed_Error);
            }
        }
        // 添加日志频率控制
        private static int _debugLogCounter = 0;
        private static bool _isDevelopmentMode = System.Configuration.ConfigurationManager.AppSettings["DevelopmentMode"] == "true" ||
                                               Environment.GetEnvironmentVariable("WAFER_ALIGNER_DEV_MODE") == "true" ||
                                               System.Diagnostics.Debugger.IsAttached;

        private async void _Update_Tick(object sender, System.Timers.ElapsedEventArgs e)
        {
            // 检查关闭标志
            if (_isClosing)
            {
                // ?? 开发模式下静默跳过
                if (!_isDevelopmentMode)
                    _loggingService?.LogDebug("窗体正在关闭，跳过Update Timer执行", EventIds.Form_Closing_In_Update_Timer);
                return;
            }

            try
            {
                // 直接执行异步操作，不需要Task.Run包装
                // 检查窗体状态和取消令牌
                if (this.IsDisposed || !this.IsHandleCreated || _cts == null || _cts.Token.IsCancellationRequested)
                {
                    return;
                }

                try
                {
                    // 视觉软件一直发送XYR的当前位置
                    // ? 使用迁移助手批量获取XYR位置并设置
                    var (x, y, r) = await _axisEventService.GetXYRPositionsAsync();
                    await _axisEventService.SetXYRPositionAsync(
                        Convert.ToSingle(x / AxisConstants.AXIS_XY_MULTIPLE_CONVERTION),
                        Convert.ToSingle(y / AxisConstants.AXIS_XY_MULTIPLE_CONVERTION),
                        Convert.ToSingle(r / AxisConstants.AXIS_R_MULTIPLE_CONVERTION)
                    );
                }
                catch (Exception ex)
                {
                    // ?? 保持重要的错误日志
                    _loggingService.LogError($"更新XYR位置时发生错误: {ex.Message}", EventIds.Update_Xyr_Position_Error);
                }
            }
            catch (OperationCanceledException)
            {
                // 任务被取消，静默退出，不记录日志
                return;
            }
            catch (Exception ex)
            {
                // ?? 保持重要的错误日志
                _loggingService.LogError($"_Update_Tick执行发生错误: {ex.Message}", EventIds.Update_Tick_Error);
            }
        }

        public Dictionary<String, Action<object>> VariableChangeActions = new();
        private void Notify(object? sender, InvoanceVariableChangedEventArgs e)
        {

        }
        private async void TmrInput_Tick(object sender, EventArgs e)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // 检查关闭标志
            if (_isClosing)
            {
                // ?? 开发模式下静默跳过
                if (!_isDevelopmentMode)
                    _loggingService?.LogDebug("窗体正在关闭，跳过Timer执行", EventIds.Form_Closing_In_Timer);
                return;
            }

            // 添加null检查，防止空引用异常
            if (_InputTimer == null)
            {
                // ?? 保持重要的警告日志
                _loggingService?.LogWarning("_InputTimer为null，跳过本次执行", EventIds.Input_Timer_Null);
                return;
            }

            // 检查窗体状态
            if (this.IsDisposed || this.Disposing || !this.IsHandleCreated)
            {
                // ?? 开发模式下静默跳过
                if (!_isDevelopmentMode)
                    _loggingService?.LogDebug("窗体已释放或正在释放，跳过Timer执行", EventIds.Form_Disposed_In_Timer);
                return;
            }

            try
            {
                _InputTimer.Stop();
            }
            catch (ObjectDisposedException)
            {
                // ?? 开发模式下静默处理
                if (!_isDevelopmentMode)
                    _loggingService?.LogDebug("Timer在Stop时已被释放", EventIds.Timer_Disposed_On_Stop);
                return;
            }
            try
            {
                // 直接执行PLC读取操作，不需要Task.Run包装
                // 检查窗体状态，避免在窗体销毁后执行操作
                if (this.IsDisposed || !this.IsHandleCreated)
                {
                    return;
                }

                var results = new object[8];

                // ? 并行读取所有PLC变量，使用迁移助手
                var readTasks = new[]
                {
                    _plcVariableService.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.UpperWaferAdSorptionSensor", false),
                    _plcVariableService.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.LowerChuckAdSorptionSensor", false),
                    _plcVariableService.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.LowerChuckAdSorptionSensor1", false),
                    _plcVariableService.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.LowerWaferAdSorptionSensor", false),
                    _plcVariableService.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.UpperChuckLeftLockSensor", false),
                    _plcVariableService.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.UpperChuckRightLockSensor", false),
                    _plcVariableService.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.UpperChuckLeftUnLockSensor", false),
                    _plcVariableService.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.UpperChuckRightUnLockSensor", false)
                };

                // ? 等待所有任务完成，并转换为object[]类型以兼容现有UI更新逻辑
                var boolResults = await Task.WhenAll(readTasks);
                for (int i = 0; i < boolResults.Length; i++)
                {
                    results[i] = boolResults[i];
                }

                // 在UI线程上更新界面
                SafeInvoke(() =>
                {
                    UpdateUIWithPLCResults(results);
                });
            }
            catch (Exception ex)
            {
                // ?? 保持重要的错误日志
                _loggingService.LogError($"PLC读取操作发生错误: {ex.Message}", EventIds.Plc_Variable_Read_Failed);
            }
            finally
            {
                try
                {
                    // 只有在性能问题时才记录警告
                    if (_InputTimer != null && !_InputTimer.IsDisposed)
                    {
                        if (stopwatch.ElapsedMilliseconds > _InputTimer.Interval * 0.8)
                        {
                            // ?? 保持性能警告日志
                            _loggingService.LogWarning($"PLC读取Timer执行时间过长: {stopwatch.ElapsedMilliseconds}ms，间隔: {_InputTimer.Interval}ms", EventIds.Timer_Performance_Warning);
                        }
                    }

                    // 安全的Timer重启
                    SafeRestartTimer();
                }
                catch (ObjectDisposedException)
                {
                    // Timer已被释放，静默处理，不记录日志
                }
                catch (Exception ex)
                {
                    // ?? 保持重要的错误日志
                    _loggingService?.LogError(ex, $"PLC Timer重启时发生错误: {ex.Message}", EventIds.Plc_Timer_Restart_Error);
                }
            }
        }

        private void SafeRestartTimer()
        {
            try
            {
                // 增加Timer空检查和关闭检查
                if (_isClosing || this.IsDisposed || this.Disposing)
                    return;

                if (_UpdateTimer != null && !_UpdateTimer.Enabled)
                {
                    _UpdateTimer.Start();
                }

                if (_InputTimer != null && !_InputTimer.Enabled)
                {
                    _InputTimer.Start();
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"重启Timer时发生错误: {ex.Message}", EventIds.Timer_Restart_Error);
            }
        }

        private async Task<object> ReadPLCVariableSafely(string variableName, Type expectedType)
        {
            try
            {
                // 使用迁移助手安全读取PLC变量，而非直接访问PLC实例
                var result = await _plcVariableService.ReadVariableSafelyAsync<object>(variableName, null);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"读取PLC变量 {variableName} 失败: {ex.Message}",
                                  EventIds.Plc_Variable_Read_Failed);
                return null;
            }
        }

        private void UpdateUIWithPLCResults(object[] results)
        {
            try
            {
                // 安全的类型转换方法
                bool SafeBoolConvert(object value, bool defaultValue = false)
                {
                    if (value == null) return defaultValue;
                    if (value is bool boolValue) return boolValue;
                    if (value is int intValue) return intValue != 0;
                    if (value is string stringValue) return !string.IsNullOrEmpty(stringValue) && stringValue.ToLower() != "false" && stringValue != "0";
                    return defaultValue;
                }

                // 检查results数组是否有效
                if (results == null || results.Length < 8)
                {
                    _loggingService.LogWarning($"PLC结果数组无效: results={results?.Length ?? 0}", EventIds.Plc_Invalid_Results_Array);
                    return;
                }

                // 上晶圆吸附状态
                bool retInPutTopWafer = SafeBoolConvert(results[0]);
                if (_axisEventService.GetCylinderState("TOPWAFER") == (int)CylinderStatus.Open)
                {
                    LightTopWafer.State = retInPutTopWafer ? UILightState.On : UILightState.Off;
                    LalTopWaferState.Text = retInPutTopWafer ? "上晶圆吸附成功" : "上晶圆吸附失败";
                }
                else
                {
                    LightTopWafer.State = UILightState.Off;
                    LalTopWaferState.Text = "上晶圆释放";
                }

                // 托盘晶圆外吸附状态
                bool retTrayWaferOuter = SafeBoolConvert(results[1]);
                if (_axisEventService.GetCylinderState("TRAYWAFERUTER") == (int)CylinderStatus.Open)
                {
                    BtnTrayWaferOuter.Text = "托盘晶圆外吸附";
                    LightTrayWaferOuter.State = retTrayWaferOuter ? UILightState.On : UILightState.Off;
                    LalTrayWaferOuterState.Text = retTrayWaferOuter ? "托盘晶圆外吸附成功" : "托盘晶圆外吸附失败";
                }
                else
                {
                    BtnTrayWaferOuter.Text = "托盘晶圆外释放";
                    LightTrayWaferOuter.State = UILightState.Off;
                    LalTrayWaferOuterState.Text = "托盘晶圆外释放";
                }

                // 托盘晶圆内吸附状态
                bool retTrayWaferInner = SafeBoolConvert(results[2]);
                if (_axisEventService.GetCylinderState("TRAYWAFERINNER") == (int)CylinderStatus.Open)
                {
                    BtnTrayWaferInner.Text = "托盘晶圆内吸附";
                    LightTrayWaferInner.State = retTrayWaferInner ? UILightState.On : UILightState.Off;
                    LalTrayWaferInnerState.Text = retTrayWaferInner ? "托盘晶圆内吸附成功" : "托盘晶圆内吸附失败";
                }
                else
                {
                    BtnTrayWaferInner.Text = "托盘晶圆内释放";
                    LightTrayWaferInner.State = UILightState.Off;
                    LalTrayWaferInnerState.Text = "托盘晶圆内释放";
                }

                // 托盘吸附状态
                bool retTray = SafeBoolConvert(results[3]);
                if (_axisEventService.GetCylinderState("TRAY") == (int)CylinderStatus.Open)
                {
                    BtnTray.Text = "托盘吸附";
                    LightTray.State = retTray ? UILightState.On : UILightState.Off;
                    LalTrayState.Text = retTray ? "托盘吸附成功" : "托盘吸附失败";
                }
                else
                {
                    BtnTray.Text = "托盘释放";
                    LightTray.State = UILightState.Off;
                    LalTrayState.Text = "托盘释放";
                }

                // 卡盘锁状态
                bool retChuckLeft = SafeBoolConvert(results[4]);
                bool retInPutChuckRight = SafeBoolConvert(results[5]);
                bool retChuckLeftUnLock = SafeBoolConvert(results[6]);
                bool retInPutChuckRightUnLock = SafeBoolConvert(results[7]);

                if (_axisEventService.GetCylinderState("CHUCKLOCK") == (int)CylinderStatus.Open)
                {
                    BtnChuckLock.Text = "卡盘锁紧";
                    LightChuckLock.State = (retChuckLeft && retInPutChuckRight) ? UILightState.On : UILightState.Off;
                    LalChuckLockState.Text = (retChuckLeft && retInPutChuckRight) ? "卡盘锁紧成功" : "卡盘锁紧失败";
                }
                else
                {
                    BtnChuckLock.Text = "卡盘锁开";
                    bool leftUnLock = retChuckLeftUnLock;
                    bool rightUnLock = retInPutChuckRightUnLock;
                    LightChuckLock.State = (leftUnLock && rightUnLock) ? UILightState.On : UILightState.Off;
                    LalChuckLockState.Text = (leftUnLock && rightUnLock) ? "卡盘锁开成功" : "卡盘锁开失败";
                }

                // 调平锁状态
                if (_axisEventService.GetCylinderState("HORIZONTALADJUST") == (int)CylinderStatus.Close)
                {
                    LalHorizontalAdjustLockState.Text = "调平锁关";
                    BtnHorizontalAdjustLock.Text = "调平锁关";
                    LightHorizontalAdjustLock.State = UILightState.Off;
                }
                else
                {
                    LalHorizontalAdjustLockState.Text = "调平锁开";
                    BtnHorizontalAdjustLock.Text = "调平锁开";
                    LightHorizontalAdjustLock.State = UILightState.On;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"更新UI时发生错误: {ex.Message}", EventIds.UI_Update_Failed);
            }
        }

        #endregion  Load       

        #region  IOButton
        private async void BtnTopWafer_Click(object sender, EventArgs e)
        {//当前状态为吸附open？，点击之后按钮文字变为释放
            BtnTopWafer.Enabled = false;
            try
            {
                // ? 使用迁移助手进行气缸控制
                await _axisEventService.CylinderControlAsync("TOPWAFER", (int)CylinderStatus.Close);
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"上晶圆气缸控制失败: {ex.Message}", EventIds.Top_Wafer_Control_Failed);
                ShowError("上晶圆气缸控制失败，请检查设备状态");
            }
            finally
            {
                BtnTopWafer.Enabled = true;
            }
        }
        private async void BtnTrayWaferOuter_Click(object sender, EventArgs e)
        {
            BtnTrayWaferOuter.Enabled = false;
            try
            {
                // ? 使用迁移助手进行气缸状态检查和控制
                var currentState = _axisEventService.GetCylinderState("TRAYWAFERUTER");
                int targetState = currentState == (int)CylinderStatus.Close ? (int)CylinderStatus.Open : (int)CylinderStatus.Close;

                await _axisEventService.CylinderControlAsync("TRAYWAFERUTER", targetState);

                // 更新UI显示
                _uiUpdateService.SafeUpdateUI(this, () =>
                {
                    BtnTrayWaferOuter.Text = targetState == (int)CylinderStatus.Open ? "托盘晶圆外吸附" : "托盘晶圆外释放";
                    LalTrayWaferOuterState.Text = targetState == (int)CylinderStatus.Open ? "托盘晶圆外吸附" : "托盘晶圆外释放";
                });
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"托盘晶圆外气缸控制失败: {ex.Message}", EventIds.Tray_Wafer_Outer_Control_Failed);
                ShowError("托盘晶圆外气缸控制失败，请检查设备状态");
            }
            finally
            {
                BtnTrayWaferOuter.Enabled = true;
            }
        }
        private async void BtnTrayWaferInner_Click(object sender, EventArgs e)
        {
            BtnTrayWaferInner.Enabled = false;
            try
            {
                // ? 使用迁移助手进行气缸状态检查和控制
                var currentState = _axisEventService.GetCylinderState("TRAYWAFERINNER");
                int targetState = currentState == (int)CylinderStatus.Close ? (int)CylinderStatus.Open : (int)CylinderStatus.Close;

                await _axisEventService.CylinderControlAsync("TRAYWAFERINNER", targetState);

                // 更新UI显示
                _uiUpdateService.SafeUpdateUI(this, () =>
                {
                    BtnTrayWaferInner.Text = targetState == (int)CylinderStatus.Open ? "托盘晶圆内吸附" : "托盘晶圆内释放";
                    LalTrayWaferInnerState.Text = targetState == (int)CylinderStatus.Open ? "托盘晶圆内吸附" : "托盘晶圆内释放";
                });
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"托盘晶圆内气缸控制失败: {ex.Message}", EventIds.Tray_Wafer_Inner_Control_Failed);
                ShowError("托盘晶圆内气缸控制失败，请检查设备状态");
            }
            finally
            {
                BtnTrayWaferInner.Enabled = true;
            }
        }
        private async void BtnTray_Click(object sender, EventArgs e)
        {
            BtnTray.Enabled = false;
            try
            {
                // ? 使用迁移助手进行气缸状态检查和控制
                var currentState = _axisEventService.GetCylinderState("TRAY");
                int targetState = currentState == (int)CylinderStatus.Close ? (int)CylinderStatus.Open : (int)CylinderStatus.Close;

                await _axisEventService.CylinderControlAsync("TRAY", targetState);

                // 更新UI显示
                _uiUpdateService.SafeUpdateUI(this, () =>
                {
                    BtnTray.Text = targetState == (int)CylinderStatus.Open ? "托盘吸附" : "托盘吸附释放";
                    LalTrayState.Text = targetState == (int)CylinderStatus.Open ? "托盘吸附" : "托盘吸附释放";
                });
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"托盘气缸控制失败: {ex.Message}", EventIds.Tray_Control_Failed);
                ShowError("托盘气缸控制失败，请检查设备状态");
            }
            finally
            {
                BtnTray.Enabled = true;
            }
        }
        private async void BtnChuckLock_Click(object sender, EventArgs e)
        {
            BtnChuckLock.Enabled = false;
            try
            {
                // ? 使用迁移助手进行气缸状态检查和控制
                var currentState = _axisEventService.GetCylinderState("CHUCKLOCK");
                int targetState = currentState == (int)CylinderStatus.Close ? (int)CylinderStatus.Open : (int)CylinderStatus.Close;

                await _axisEventService.CylinderControlAsync("CHUCKLOCK", targetState);

                // 更新UI显示
                _uiUpdateService.SafeUpdateUI(this, () =>
                {
                    if (targetState == (int)CylinderStatus.Open)
                    {
                        LalChuckLockState.Text = "卡盘锁紧";
                        BtnChuckLock.Text = "卡盘锁紧";
                        LightChuckLock.State = UILightState.On;
                    }
                    else
                    {
                        LalChuckLockState.Text = "卡盘锁开";
                        BtnChuckLock.Text = "卡盘锁开";
                        LightChuckLock.State = UILightState.Off;
                    }
                });
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"卡盘锁气缸控制失败: {ex.Message}", EventIds.Chuck_Lock_Control_Failed);
                ShowError("卡盘锁气缸控制失败，请检查设备状态");
            }
            finally
            {
                BtnChuckLock.Enabled = true;
            }
        }
        private async void BtnHorizontalAdjustLock_Click(object sender, EventArgs e)
        {
            BtnHorizontalAdjustLock.Enabled = false;
            try
            {
                // ? 使用迁移助手进行气缸状态检查和控制
                var currentState = _axisEventService.GetCylinderState("HORIZONTALADJUST");
                int targetState = currentState == (int)CylinderStatus.Close ? (int)CylinderStatus.Open : (int)CylinderStatus.Close;

                await _axisEventService.CylinderControlAsync("HORIZONTALADJUST", targetState);

                // 更新UI显示
                _uiUpdateService.SafeUpdateUI(this, () =>
                {
                    if (targetState == (int)CylinderStatus.Open)
                    {
                        LalHorizontalAdjustLockState.Text = "调平锁开";
                        BtnHorizontalAdjustLock.Text = "调平锁开";
                        LightHorizontalAdjustLock.State = UILightState.On;
                    }
                    else
                    {
                        LalHorizontalAdjustLockState.Text = "调平锁关";
                        BtnHorizontalAdjustLock.Text = "调平锁关";
                        LightHorizontalAdjustLock.State = UILightState.Off;
                    }
                });
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"调平锁气缸控制失败: {ex.Message}", EventIds.Horizontal_Adjust_Control_Failed);
                ShowError("调平锁气缸控制失败，请检查设备状态");
            }
            finally
            {
                BtnHorizontalAdjustLock.Enabled = true;
            }
        }

        #endregion  IOButton

        #region  TEXT
        private async void Txt_KeyUp(object sender, KeyEventArgs e)
        {
            UITextBox temp = (UITextBox)sender;
            string TempText = temp.Text.Trim();
            double value;
            if (TempText == "+" || TempText == "-" || TempText == "")
            {
                value = 0;
            }
            else if (TempText.EndsWith("."))
            {
                return;
            }
            else
            {
                value = Convert.ToDouble(TempText);
            }
            switch (temp.Name)
            {
                case "TxtZOffset":
                    {
                        // ? 使用迁移助手进行Z轴归零偏移设置
                        await _axisEventService.SetZAxisHomeOffsetAsync(value);
                    }
                    break;
                case "TxtTopGap":
                    {
                        // ? 使用迁移助手设置上间隙参数
                        await _axisEventService.SetTopGapParameterAsync(value);
                    }
                    break;
                case "TxtBottomGap":
                    {
                        // ? 使用迁移助手设置下间隙参数
                        await _axisEventService.SetBottomGapParameterAsync(value);
                    }
                    break;
                case "TxtBottomPhotoOffset":
                    {
                        // ? 使用迁移助手设置下拍照参数
                        await _axisEventService.SetBottomPhotoParameterAsync(value);
                    }
                    break;
                case "TxtCameraOffset":
                    {
                        // ? 使用迁移助手设置相机偏移参数
                        await _axisEventService.SetCameraOffsetParameterAsync(value);
                    }
                    break;
            }


        }
        #endregion  TEXT

        #region 平台标定
        int Calibrate_state = -1;
        private async void BtnCal_Click(object sender, EventArgs e)
        {
            BtnManage(false);
            FHeaderMainFooter.frmM.LalSoftwareStateTest = "平台标定中...";
            // ? 使用迁移助手进行XYR标定
            await _axisEventService.CalibrateXYRAsync();

            // 使用_CalTimer替代timer1
            _CalTimer.Interval = 100;
            Calibrate_state = 0;
            _CalTimer.Start();
        }

        private async void Timer1_Tick(object sender, EventArgs e)
        {
            _CalTimer.Stop();
            try
            {
                switch (Calibrate_state)
                {
                    case 0://获取20 21 22的状态直到111
                        {
                            // ? 使用迁移助手接收消息
                            string State = await _axisEventService.ReceiveMsgAsync();
                            string[] States = State.Split(',');
                            if (Convert.ToInt32(States[0]) == 1 && Convert.ToInt32(States[1]) == 9 && Convert.ToInt32(States[2]) == 1)
                            {
                                Calibrate_state = 1;
                                if (!this.IsDisposed && this.IsHandleCreated)
                                    _CalTimer?.Start();
                            }
                            else if (Convert.ToInt32(States[0]) == 1 && Convert.ToInt32(States[1]) == 4 && Convert.ToInt32(States[2]) == 1)
                            {
                                //标定完成
                                FHeaderMainFooter.frmM.LalSoftwareStateTest = "平台标定完成！";
                                if (this.IsHandleCreated && !this.IsDisposed)
                                {
                                    this.Invoke((MethodInvoker)(() => { BtnManage(true); }));
                                }
                                return;
                            }
                            else if (Convert.ToInt32(States[0]) == 1 && Convert.ToInt32(States[1]) == 4 && Convert.ToInt32(States[2]) == 2)
                            {
                                FHeaderMainFooter.frmM.LalSoftwareStateTest = "平台标定失败！";
                                _loggingService.LogError($"平台标定失败", EventIds.Platform_Calibrate_Failed);
                                if (this.IsHandleCreated && !this.IsDisposed)
                                {
                                    this.Invoke((MethodInvoker)(() => { BtnManage(true); }));
                                }
                                return;
                            }
                            else
                            {
                                if (!this.IsDisposed && this.IsHandleCreated)
                                    _CalTimer?.Start();
                            }
                        }
                        break;
                    case 1:
                        {
                            Calibrate_state = -1;
                            //获取26、28、30的位置并发出指令
                            // ? 使用迁移助手获取XYR位置
                            string Pos = await _axisEventService.GetXYRPosAsync();
                            string[] Poses = Pos.Split(',');

                            // ? 使用迁移助手执行XYR轴批量定位
                            await _axisEventService.MoveXYRAxesToPositionAsync(
                                Convert.ToSingle(Poses[0]),
                                Convert.ToSingle(Poses[1]),
                                Convert.ToSingle(Poses[2])
                            );

                            Calibrate_state = 2;
                            if (!this.IsDisposed && this.IsHandleCreated)
                                _CalTimer?.Start();
                        }
                        break;
                    case 2://等待各轴运动完成 
                        {
                            Calibrate_state = -1;
                            // ? 使用迁移助手获取XYR轴运动状态
                            var (retX, retY, retR) = _axisEventService.GetXYRRunStates();
                            if (retX != 1 && retY != 1 && retR != 1)
                            {
                                //运动完成
                                // ? 使用迁移助手发送消息
                                await _axisEventService.SendMsgAsync(1, 9, 1, 0, 0);
                                Calibrate_state = 0;
                                if (!this.IsDisposed && this.IsHandleCreated)
                                    _CalTimer?.Start();
                            }
                            else
                            {
                                //运动中
                                Calibrate_state = 2;
                                if (!this.IsDisposed && this.IsHandleCreated)
                                    _CalTimer?.Start();
                            }
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"标定Timer处理发生错误: {ex.Message}", EventIds.Calibrate_Timer_Error);
                if (this.IsHandleCreated && !this.IsDisposed)
                {
                    this.Invoke((MethodInvoker)(() => { BtnManage(true); }));
                }
            }
        }

        private async void Calibrate_Tick(object sender, System.Timers.ElapsedEventArgs e)
        {
            // 使用TimerWrapper的方式停止_CalibrateTimer
            if (_CalibrateTimer != null)
                _CalibrateTimer.Stop();

            try
            {
                switch (Calibrate_state)
                {
                    case 0://获取20 21 22的状态直到111
                        {
                            Calibrate_state = -1;
                            // ? 使用迁移助手接收消息
                            string State = await _axisEventService.ReceiveMsgAsync();
                            string[] States = State.Split(',');
                            if (Convert.ToInt32(States[0]) == 1 && Convert.ToInt32(States[1]) == 9 && Convert.ToInt32(States[2]) == 1)
                            {
                                Calibrate_state = 1;
                                if (!this.IsDisposed && this.IsHandleCreated && _CalibrateTimer != null)
                                    _CalibrateTimer.Start();
                            }
                            else if (Convert.ToInt32(States[0]) == 1 && Convert.ToInt32(States[1]) == 4 && Convert.ToInt32(States[2]) == 1)
                            {
                                //标定完成
                                FHeaderMainFooter.frmM.LalSoftwareStateTest = "平台标定完成！";
                                if (this.IsHandleCreated && !this.IsDisposed)
                                {
                                    this.Invoke((MethodInvoker)(() => { BtnManage(true); }));
                                }
                                return;
                            }
                            else if (Convert.ToInt32(States[0]) == 1 && Convert.ToInt32(States[1]) == 4 && Convert.ToInt32(States[2]) == 2)
                            {
                                FHeaderMainFooter.frmM.LalSoftwareStateTest = "平台标定失败！";
                                _loggingService.LogError($"平台标定失败", EventIds.Platform_Calibrate_Failed);
                                if (this.IsHandleCreated && !this.IsDisposed)
                                {
                                    this.Invoke((MethodInvoker)(() => { BtnManage(true); }));
                                }
                                return;
                            }
                            else
                            {
                                if (!this.IsDisposed && this.IsHandleCreated && _CalibrateTimer != null)
                                    _CalibrateTimer.Start();
                            }
                        }
                        break;
                    case 1:
                        {
                            Calibrate_state = -1;
                            //获取26、28、30的位置并发出指令
                            // ? 使用迁移助手获取XYR位置
                            string Pos = await _axisEventService.GetXYRPosAsync();
                            string[] Poses = Pos.Split(',');

                            // ? 使用迁移助手执行XYR轴批量定位
                            await _axisEventService.MoveXYRAxesToPositionAsync(
                                Convert.ToSingle(Poses[0]),
                                Convert.ToSingle(Poses[1]),
                                Convert.ToSingle(Poses[2])
                            );

                            Calibrate_state = 2;
                            if (!this.IsDisposed && this.IsHandleCreated && _CalibrateTimer != null)
                                _CalibrateTimer.Start();
                        }
                        break;
                    case 2://等待各轴运动完成 
                        {
                            Calibrate_state = -1;
                            // ? 使用迁移助手获取XYR轴运动状态
                            var (retX, retY, retR) = _axisEventService.GetXYRRunStates();
                            if (retX != 1 && retY != 1 && retR != 1)
                            {
                                //运动完成
                                // ? 使用迁移助手发送消息
                                await _axisEventService.SendMsgAsync(1, 9, 1, 0, 0);
                                Calibrate_state = 0;
                                if (!this.IsDisposed && this.IsHandleCreated && _CalibrateTimer != null)
                                    _CalibrateTimer.Start();
                            }
                            else
                            {
                                //运动中
                                Calibrate_state = 2;
                                if (!this.IsDisposed && this.IsHandleCreated && _CalibrateTimer != null)
                                    _CalibrateTimer.Start();
                            }
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"标定Timer处理发生错误: {ex.Message}", EventIds.Calibrate_Timer_Error);
                if (this.IsHandleCreated && !this.IsDisposed)
                {
                    this.Invoke((MethodInvoker)(() => { BtnManage(true); }));
                }
            }
        }



        // 替换BackgroundWorker为CancellationTokenSource
        private CancellationTokenSource _calPosCts = null;

        private async void BtnCalPos_Click(object sender, EventArgs e)
        {
            try
            {
                BtnManage(false);
                BtnSave.Enabled = false;
                BtnOpen.Enabled = false;

                if (TxtLX.Text == "" || TxtLY.Text == "" || TxtLZ.Text == "" || TxtRX.Text == "" || TxtRY.Text == "" || TxtRZ.Text == "")
                {
                    MessageBox.Show("标定运动前,请输入全部轴位置！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    BtnManage(true);
                    return;
                }

                // 创建新的取消令牌源
                _calPosCts = new CancellationTokenSource();

                FHeaderMainFooter.frmM.LalSoftwareStateTest = "标定位运动中...";

                // 使用迁移助手执行相机轴批量定位
                await _axisEventService.MoveCameraAxesToPositionAsync(
                    Convert.ToDouble(TxtLX.Text),  // LX
                    Convert.ToDouble(TxtLY.Text),  // LY
                    Convert.ToDouble(TxtLZ.Text),  // LZ
                    Convert.ToDouble(TxtRX.Text),  // RX
                    Convert.ToDouble(TxtRY.Text),  // RY
                    Convert.ToDouble(TxtRZ.Text)   // RZ
                );

                // 异步等待轴到达目标位置
                try
                {
                    // 使用异步任务替代BackgroundWorker
                    await Task.Run(async () =>
                    {
                        // 检查是否取消，如果取消则抛出异常
                        while (!_axisEventService.CheckCameraAxesArrived())
                        {
                            _calPosCts.Token.ThrowIfCancellationRequested();
                            // 短暂休眠避免占用过多CPU
                            await Task.Delay(50, _calPosCts.Token);
                        }
                    }, _calPosCts.Token);

                    // 任务完成，显示成功消息
                    FHeaderMainFooter.frmM.LalSoftwareStateTest = "标定运动完成！";
                }
                catch (OperationCanceledException)
                {
                    // 任务被取消
                    MessageBox.Show("您取消了操作!");
                    FHeaderMainFooter.frmM.LalSoftwareStateTest = "标定位运动取消！";
                }
                catch (Exception ex)
                {
                    // 处理错误
                    FHeaderMainFooter.frmM.LalSoftwareStateTest = "标定位运动中产生错误:" + ex.Message;
                    _loggingService.LogError(ex, "标定位运动中产生错误", EventIds.Calibrated_Moving_Failed);
                    MessageBox.Show("标定位运动中产生错误:" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                finally
                {
                    // 清理取消令牌源
                    _calPosCts?.Dispose();
                    _calPosCts = null;

                    // 恢复按钮状态
                    BtnManage(true);
                    BtnSave.Enabled = true;
                    BtnOpen.Enabled = true;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "标定位运动执行失败", EventIds.Calibrated_Moving_Failed);
                MessageBox.Show("标定位运动执行失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 确保UI恢复正常状态
                BtnManage(true);
                BtnSave.Enabled = true;
                BtnOpen.Enabled = true;
            }
        }

        private async void BtnFiveStop_Click(object sender, EventArgs e)
        {
            try
            {
                BtnManage(false);

                // 取消当前运行的标定任务
                _calPosCts?.Cancel();

                // 使用迁移助手停止所有轴运动
                await _axisEventService.StopAllAxesAsync();
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "停止轴运动失败", EventIds.Axis_Stop_Error);
                MessageBox.Show("停止轴运动失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                BtnManage(true);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            BtnSave.Enabled = false;
            string path;
            SaveFileDialog savefile = new()
            {
                Filter = "标定文件(*.json)|*.json", //格式写对才能显示对应的后缀名的文件
                InitialDirectory = Environment.CurrentDirectory + @"\CalPara\"
            };
            if (savefile.ShowDialog() == DialogResult.OK)//选择了打开文件 
            {
                Application.DoEvents();
                path = savefile.FileName;
                bool Res = WriteToCalJSON(path);
                if (Res)
                {
                    FHeaderMainFooter.frmM.LalSoftwareStateTest = "标定文件保存成功!";
                    _loggingService.LogInformation($"保存标定参数文件成功", EventIds.Save_Calibration_Config_Success);

                }
                else
                {
                    FHeaderMainFooter.frmM.LalSoftwareStateTest = "标定文件保存失败!";
                    _loggingService.LogError($"保存标定参数文件失败", EventIds.Save_Calibration_Config_Failed);

                    MessageBox.Show("标定文件保存失败!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                }
            }
            else
            {
                FHeaderMainFooter.frmM.LalSoftwareStateTest = "没有保存标定文件!";
                MessageBox.Show("没有保存标定文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            }
            BtnSave.Enabled = true;
        }

        private void BtnOpen_Click(object sender, EventArgs e)
        {
            BtnOpen.Enabled = false;
            string path;
            OpenFileDialog openfile = new()
            {
                Filter = "标定文件(*.json)|*.json", //格式写对才能显示对应的后缀名的文件
                InitialDirectory = Environment.CurrentDirectory + @"\CalPara\"
            };
            if (openfile.ShowDialog() == DialogResult.OK)//选择了打开文件 
            {
                Application.DoEvents();

                path = openfile.FileName;
                bool Res = ReadFromCalJSON(path);
                if (Res)
                {
                    FHeaderMainFooter.frmM.LalSoftwareStateTest = "打开标定文件成功!";
                    _loggingService.LogInformation($"加载标定参数文件成功", EventIds.Load_Calibration_Config_Success);

                }
                else
                {
                    FHeaderMainFooter.frmM.LalSoftwareStateTest = "打开标定文件失败!";
                    _loggingService.LogError($"加载标定参数文件发生错误", EventIds.Load_Calibration_Config_Failed);

                    MessageBox.Show("打开标定文件失败!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                }
            }
            else
            { //选择取消  
                FHeaderMainFooter.frmM.LalSoftwareStateTest = "没有选择标定文件";
                MessageBox.Show("没有选择标定文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            }
            BtnOpen.Enabled = true;
        }
        private bool ReadFromCalJSON(string FileName)
        {
            try
            {
                JsonOperator oper = new(FileName);
                this.SafeInvoke(() => TxtLX.Text = oper.Select("LX轴标定位置"));
                this.SafeInvoke(() => TxtLY.Text = oper.Select("LY轴标定位置"));
                this.SafeInvoke(() => TxtLZ.Text = oper.Select("LZ轴标定位置"));
                this.SafeInvoke(() => TxtRX.Text = oper.Select("RX轴标定位置"));
                this.SafeInvoke(() => TxtRY.Text = oper.Select("RY轴标定位置"));
                this.SafeInvoke(() => TxtRZ.Text = oper.Select("RZ轴标定位置"));
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message.ToString());
                Application.Exit();
                return false;
            }
        }

        public bool WriteToCalJSON(string FileName)
        {
            try
            {
                if (File.Exists(FileName))
                {
                    File.Delete(FileName);
                }
                JsonOperator oper = new(FileName);
                oper.Create("LX轴标定位置", TxtLX.Text);
                oper.Create("LY轴标定位置", TxtLY.Text);
                oper.Create("LZ轴标定位置", TxtLZ.Text);
                oper.Create("RX轴标定位置", TxtRX.Text);
                oper.Create("RY轴标定位置", TxtRY.Text);
                oper.Create("RZ轴标定位置", TxtRZ.Text);
                oper.Save();
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message.ToString());
                return false;
            }
        }

        private bool ReadFromSysJSON(string FileName)
        {
            try
            {
                JsonOperator oper = new(FileName);
                //运动参数
                步进电机调试X.PosMax = Convert.ToDouble(oper.Select("X轴限位max"));
                步进电机调试X.PosMin = Convert.ToDouble(oper.Select("X轴限位min"));
                步进电机调试X.SpeedMax = Convert.ToDouble(oper.Select("X轴速度max"));
                步进电机调试X.SpeedMin = Convert.ToDouble(oper.Select("X轴速度min"));
                步进电机调试X.JogSpeedMax = Convert.ToDouble(oper.Select("X轴Jog速度max"));
                步进电机调试X.JogSpeedMin = Convert.ToDouble(oper.Select("X轴Jog速度min"));
                //步进电机调试X.Pos = Convert.ToDouble(oper.Select(FileName, "X轴目标位置"));
                步进电机调试X.Speed = Convert.ToDouble(oper.Select("X轴目标速度"));
                步进电机调试X.JogSpeed = Convert.ToDouble(oper.Select("X轴Jog速度"));


                步进电机调试Y.PosMax = Convert.ToDouble(oper.Select("Y轴限位max"));
                步进电机调试Y.PosMin = Convert.ToDouble(oper.Select("Y轴限位min"));
                步进电机调试Y.SpeedMax = Convert.ToDouble(oper.Select("Y轴速度max"));
                步进电机调试Y.SpeedMin = Convert.ToDouble(oper.Select("Y轴速度min"));
                步进电机调试Y.JogSpeedMax = Convert.ToDouble(oper.Select("Y轴Jog速度max"));
                步进电机调试Y.JogSpeedMin = Convert.ToDouble(oper.Select("Y轴Jog速度min"));
                //步进电机调试Y.Pos = Convert.ToDouble(oper.Select(FileName, "Y轴目标位置"));
                步进电机调试Y.Speed = Convert.ToDouble(oper.Select("Y轴目标速度"));
                步进电机调试Y.JogSpeed = Convert.ToDouble(oper.Select("Y轴Jog速度"));


                步进电机调试R.PosMax = Convert.ToDouble(oper.Select("R轴限位max"));
                步进电机调试R.PosMin = Convert.ToDouble(oper.Select("R轴限位min"));
                步进电机调试R.SpeedMax = Convert.ToDouble(oper.Select("R轴速度max"));
                步进电机调试R.SpeedMin = Convert.ToDouble(oper.Select("R轴速度min"));
                步进电机调试R.JogSpeedMax = Convert.ToDouble(oper.Select("R轴Jog速度max"));
                步进电机调试R.JogSpeedMin = Convert.ToDouble(oper.Select("R轴Jog速度min"));
                //步进电机调试R.Pos = Convert.ToDouble(oper.Select(FileName, "R轴目标位置"));
                步进电机调试R.Speed = Convert.ToDouble(oper.Select("R轴目标速度"));
                步进电机调试R.JogSpeed = Convert.ToDouble(oper.Select("R轴Jog速度"));


                步进电机调试Z.PosMax = Convert.ToDouble(oper.Select("Z轴限位max"));
                步进电机调试Z.PosMin = Convert.ToDouble(oper.Select("Z轴限位min"));
                步进电机调试Z.SpeedMax = Convert.ToDouble(oper.Select("Z轴速度max"));
                步进电机调试Z.SpeedMin = Convert.ToDouble(oper.Select("Z轴速度min"));
                步进电机调试Z.JogSpeedMax = Convert.ToDouble(oper.Select("Z轴Jog速度max"));
                步进电机调试Z.JogSpeedMin = Convert.ToDouble(oper.Select("Z轴Jog速度min"));
                //步进电机调试Z.Pos = Convert.ToDouble(oper.Select(FileName, "Z轴目标位置"));
                步进电机调试Z.Speed = Convert.ToDouble(oper.Select("Z轴目标速度"));
                步进电机调试Z.JogSpeed = Convert.ToDouble(oper.Select("Z轴Jog速度"));


                步进电机调试LX.PosMax = Convert.ToDouble(oper.Select("LX轴限位max"));
                步进电机调试LX.PosMin = Convert.ToDouble(oper.Select("LX轴限位min"));
                步进电机调试LX.SpeedMax = Convert.ToDouble(oper.Select("LX轴速度max"));
                步进电机调试LX.SpeedMin = Convert.ToDouble(oper.Select("LX轴速度min"));
                步进电机调试LX.JogSpeedMax = Convert.ToDouble(oper.Select("LX轴Jog速度max"));
                步进电机调试LX.JogSpeedMin = Convert.ToDouble(oper.Select("LX轴Jog速度min"));
                //步进电机调试LX.Pos = Convert.ToDouble(oper.Select(FileName, "LX轴目标位置"));
                步进电机调试LX.Speed = Convert.ToDouble(oper.Select("LX轴目标速度"));
                步进电机调试LX.JogSpeed = Convert.ToDouble(oper.Select("LX轴Jog速度"));


                步进电机调试LY.PosMax = Convert.ToDouble(oper.Select("LY轴限位max"));
                步进电机调试LY.PosMin = Convert.ToDouble(oper.Select("LY轴限位min"));
                步进电机调试LY.SpeedMax = Convert.ToDouble(oper.Select("LY轴速度max"));
                步进电机调试LY.SpeedMin = Convert.ToDouble(oper.Select("LY轴速度min"));
                步进电机调试LY.JogSpeedMax = Convert.ToDouble(oper.Select("LY轴Jog速度max"));
                步进电机调试LY.JogSpeedMin = Convert.ToDouble(oper.Select("LY轴Jog速度min"));
                //步进电机调试LY.Pos = Convert.ToDouble(oper.Select(FileName, "LY轴目标位置"));
                步进电机调试LY.Speed = Convert.ToDouble(oper.Select("LY轴目标速度"));
                步进电机调试LY.JogSpeed = Convert.ToDouble(oper.Select("LY轴Jog速度"));


                步进电机调试LZ.PosMax = Convert.ToDouble(oper.Select("LZ轴限位max"));
                步进电机调试LZ.PosMin = Convert.ToDouble(oper.Select("LZ轴限位min"));
                步进电机调试LZ.SpeedMax = Convert.ToDouble(oper.Select("LZ轴速度max"));
                步进电机调试LZ.SpeedMin = Convert.ToDouble(oper.Select("LZ轴速度min"));
                步进电机调试LZ.JogSpeedMax = Convert.ToDouble(oper.Select("LZ轴Jog速度max"));
                步进电机调试LZ.JogSpeedMin = Convert.ToDouble(oper.Select("LZ轴Jog速度min"));
                //步进电机调试LZ.Pos = Convert.ToDouble(oper.Select(FileName, "LZ轴目标位置"));
                步进电机调试LZ.Speed = Convert.ToDouble(oper.Select("LZ轴目标速度"));
                步进电机调试LZ.JogSpeed = Convert.ToDouble(oper.Select("LZ轴Jog速度"));


                步进电机调试RX.PosMax = Convert.ToDouble(oper.Select("RX轴限位max"));
                步进电机调试RX.PosMin = Convert.ToDouble(oper.Select("RX轴限位min"));
                步进电机调试RX.SpeedMax = Convert.ToDouble(oper.Select("RX轴速度max"));
                步进电机调试RX.SpeedMin = Convert.ToDouble(oper.Select("RX轴速度min"));
                步进电机调试RX.JogSpeedMax = Convert.ToDouble(oper.Select("RX轴Jog速度max"));
                步进电机调试RX.JogSpeedMin = Convert.ToDouble(oper.Select("RX轴Jog速度min"));
                //步进电机调试RX.Pos = Convert.ToDouble(oper.Select(FileName, "RX轴目标位置"));
                步进电机调试RX.Speed = Convert.ToDouble(oper.Select("RX轴目标速度"));
                步进电机调试RX.JogSpeed = Convert.ToDouble(oper.Select("RX轴Jog速度"));


                步进电机调试RY.PosMax = Convert.ToDouble(oper.Select("RY轴限位max"));
                步进电机调试RY.PosMin = Convert.ToDouble(oper.Select("RY轴限位min"));
                步进电机调试RY.SpeedMax = Convert.ToDouble(oper.Select("RY轴速度max"));
                步进电机调试RY.SpeedMin = Convert.ToDouble(oper.Select("RY轴速度min"));
                步进电机调试RY.JogSpeedMax = Convert.ToDouble(oper.Select("RY轴Jog速度max"));
                步进电机调试RY.JogSpeedMin = Convert.ToDouble(oper.Select("RY轴Jog速度min"));
                //步进电机调试RY.Pos = Convert.ToDouble(oper.Select(FileName, "RY轴目标位置"));
                步进电机调试RY.Speed = Convert.ToDouble(oper.Select("RY轴目标速度"));
                步进电机调试RY.JogSpeed = Convert.ToDouble(oper.Select("RY轴Jog速度"));

                步进电机调试RZ.PosMax = Convert.ToDouble(oper.Select("RZ轴限位max"));
                步进电机调试RZ.PosMin = Convert.ToDouble(oper.Select("RZ轴限位min"));
                步进电机调试RZ.SpeedMax = Convert.ToDouble(oper.Select("RZ轴速度max"));
                步进电机调试RZ.SpeedMin = Convert.ToDouble(oper.Select("RZ轴速度min"));
                步进电机调试RZ.JogSpeedMax = Convert.ToDouble(oper.Select("RZ轴Jog速度max"));
                步进电机调试RZ.JogSpeedMin = Convert.ToDouble(oper.Select("RZ轴Jog速度min"));
                //步进电机调试RZ.Pos = Convert.ToDouble(oper.Select(FileName, "RZ轴目标位置"));
                步进电机调试RZ.Speed = Convert.ToDouble(oper.Select("RZ轴目标速度"));
                步进电机调试RZ.JogSpeed = Convert.ToDouble(oper.Select("RZ轴Jog速度"));

                //标定参数
                this.SafeInvoke(() => TxtLX.Text = oper.Select("LX轴标定位置"));
                this.SafeInvoke(() => TxtLY.Text = oper.Select("LY轴标定位置"));
                this.SafeInvoke(() => TxtLZ.Text = oper.Select("LZ轴标定位置"));
                this.SafeInvoke(() => TxtRX.Text = oper.Select("RX轴标定位置"));
                this.SafeInvoke(() => TxtRY.Text = oper.Select("RY轴标定位置"));
                this.SafeInvoke(() => TxtRZ.Text = oper.Select("RZ轴标定位置"));

                this.SafeInvoke(() => TxtZOffset.Text = oper.Select("Z轴原点偏移位置"));
                this.SafeInvoke(() => TxtCameraOffset.Text = oper.Select("相机偏移量"));
                this.SafeInvoke(() => TxtBottomPhotoOffset.Text = oper.Select("下透明片拍照偏移量"));
                this.SafeInvoke(() => TxtTopGap.Text = oper.Select("上晶圆吸附间隙"));
                this.SafeInvoke(() => TxtBottomGap.Text = oper.Select("下晶圆吸附间隙"));

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message.ToString());
                Application.Exit();
                return false;
            }
        }

        private async void SysPareExcute()
        {
            try
            {
                // 修改：使用后台任务执行批量设置所有轴速度，避免阻塞UI
                _loggingService?.LogInformation("开始设置轴速度参数", EventIds.Axis_Speed_Config_Started);

                // 准备所有要执行的任务
                var tasks = new List<Task>();

                // XYR轴速度配置 - 不使用Task.Run包装，直接使用异步操作
                var xyrTask = SetXYRAxisSpeedsAsync();
                tasks.Add(xyrTask);

                // 左侧相机轴速度配置 - 不使用Task.Run包装，直接使用异步操作
                var leftCameraTask = SetLeftCameraAxisSpeedsAsync();
                tasks.Add(leftCameraTask);

                // 右侧相机轴速度配置 - 不使用Task.Run包装，直接使用异步操作
                var rightCameraTask = SetRightCameraAxisSpeedsAsync();
                tasks.Add(rightCameraTask);

                // 等待所有任务组完成，但设置超时以防止长时间阻塞
                await Task.WhenAny(
                    Task.WhenAll(tasks),
                    Task.Delay(5000) // 5秒超时
                );

                // 检查是否所有任务都已完成
                if (tasks.All(t => t.IsCompleted))
                {
                    _loggingService?.LogInformation("所有轴速度参数设置已完成", EventIds.All_Axis_Speed_Config_Success);
                }
                else
                {
                    _loggingService?.LogWarning("部分轴速度参数设置超时，将在后台继续进行", EventIds.Some_Axis_Speed_Config_Timeout);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "创建轴速度设置任务失败", EventIds.Task_Creation_Failed);
            }
        }

        // 拆分为单独的异步方法，便于维护
        private async Task SetXYRAxisSpeedsAsync()
        {
            try
            {
                // XYR轴速度配置
                await _axisEventService.SetAxisSpeedsAsync(
                    (uint)(步进电机调试X.Speed * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION),
                    (uint)(步进电机调试X.JogSpeed * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION),

                    (uint)(步进电机调试Y.Speed * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION),
                    (uint)(步进电机调试Y.JogSpeed * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION),

                    (uint)(步进电机调试R.Speed * AxisConstants.AXIS_R_MULTIPLE_CONVERTION),
                    (uint)(步进电机调试R.JogSpeed * AxisConstants.AXIS_R_MULTIPLE_CONVERTION),

                    (uint)(步进电机调试Z.Speed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION),
                    (uint)(步进电机调试Z.JogSpeed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION)
                );
                _loggingService?.LogDebug("XYR轴速度参数设置成功", EventIds.Xyr_Axis_Speed_Config_Success);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "XYR轴速度参数设置失败", EventIds.Xyr_Axis_Speed_Config_Failed);
            }
        }

        // 拆分为单独的异步方法，便于维护
        private async Task SetLeftCameraAxisSpeedsAsync()
        {
            try
            {
                // 左侧相机相关轴处理
                var lxAxis = await _axisEventService.GetCameraAxisViewModelAsync("Left", "X");
                var lyAxis = await _axisEventService.GetCameraAxisViewModelAsync("Left", "Y");
                var lzAxis = await _axisEventService.GetCameraAxisViewModelAsync("Left", "Z");

                await lxAxis.SetRunSpeedAsync(步进电机调试LX.Speed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                await lxAxis.SetJogSpeedAsync(步进电机调试LX.JogSpeed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);

                await lyAxis.SetRunSpeedAsync(步进电机调试LY.Speed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                await lyAxis.SetJogSpeedAsync(步进电机调试LY.JogSpeed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);

                await lzAxis.SetRunSpeedAsync(步进电机调试LZ.Speed);
                await lzAxis.SetJogSpeedAsync(步进电机调试LZ.JogSpeed);

                _loggingService?.LogDebug("左侧相机轴速度参数设置成功", EventIds.Left_Camera_Axis_Speed_Config_Success);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "左侧相机轴速度参数设置失败", EventIds.Left_Camera_Axis_Speed_Config_Failed);
            }
        }

        // 拆分为单独的异步方法，便于维护
        private async Task SetRightCameraAxisSpeedsAsync()
        {
            try
            {
                // 右侧相机相关轴处理
                var rxAxis = await _axisEventService.GetCameraAxisViewModelAsync("Right", "X");
                var ryAxis = await _axisEventService.GetCameraAxisViewModelAsync("Right", "Y");
                var rzAxis = await _axisEventService.GetCameraAxisViewModelAsync("Right", "Z");

                await rxAxis.SetRunSpeedAsync(步进电机调试RX.Speed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                await rxAxis.SetJogSpeedAsync(步进电机调试RX.JogSpeed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);

                await ryAxis.SetRunSpeedAsync(步进电机调试RY.Speed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                await ryAxis.SetJogSpeedAsync(步进电机调试RY.JogSpeed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);

                await rzAxis.SetRunSpeedAsync(步进电机调试RZ.Speed);
                await rzAxis.SetJogSpeedAsync(步进电机调试RZ.JogSpeed);

                _loggingService?.LogDebug("右侧相机轴速度参数设置成功", EventIds.Right_Camera_Axis_Speed_Config_Success);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "右侧相机轴速度参数设置失败", EventIds.Right_Camera_Axis_Speed_Config_Failed);
            }
        }

        public bool WriteToSysJSON(string FileName)
        {
            try
            {
                if (File.Exists(FileName))
                {
                    File.Delete(FileName);
                }
                JsonOperator oper = new(FileName);

                oper.Create("X轴限位max", Convert.ToString(步进电机调试X.PosMax));
                oper.Create("X轴限位min", Convert.ToString(步进电机调试X.PosMin));
                oper.Create("X轴速度max", Convert.ToString(步进电机调试X.SpeedMax));
                oper.Create("X轴速度min", Convert.ToString(步进电机调试X.SpeedMin));
                oper.Create("X轴Jog速度max", Convert.ToString(步进电机调试X.JogSpeedMax));
                oper.Create("X轴Jog速度min", Convert.ToString(步进电机调试X.JogSpeedMin));
                //oper.Create(FileName, "X轴目标位置", Convert.ToString(步进电机调试X.Pos));
                oper.Create("X轴目标速度", Convert.ToString(步进电机调试X.Speed));
                oper.Create("X轴Jog速度", Convert.ToString(步进电机调试X.JogSpeed));

                oper.Create("Y轴限位max", Convert.ToString(步进电机调试Y.PosMax));
                oper.Create("Y轴限位min", Convert.ToString(步进电机调试Y.PosMin));
                oper.Create("Y轴速度max", Convert.ToString(步进电机调试Y.SpeedMax));
                oper.Create("Y轴速度min", Convert.ToString(步进电机调试Y.SpeedMin));
                oper.Create("Y轴Jog速度max", Convert.ToString(步进电机调试Y.JogSpeedMax));
                oper.Create("Y轴Jog速度min", Convert.ToString(步进电机调试Y.JogSpeedMin));
                //oper.Create(FileName, "Y轴目标位置", Convert.ToString(步进电机调试Y.Pos));
                oper.Create("Y轴目标速度", Convert.ToString(步进电机调试Y.Speed));
                oper.Create("Y轴Jog速度", Convert.ToString(步进电机调试Y.JogSpeed));


                oper.Create("R轴限位max", Convert.ToString(步进电机调试R.PosMax));
                oper.Create("R轴限位min", Convert.ToString(步进电机调试R.PosMin));
                oper.Create("R轴速度max", Convert.ToString(步进电机调试R.SpeedMax));
                oper.Create("R轴速度min", Convert.ToString(步进电机调试R.SpeedMin));
                oper.Create("R轴Jog速度max", Convert.ToString(步进电机调试R.JogSpeedMax));
                oper.Create("R轴Jog速度min", Convert.ToString(步进电机调试R.JogSpeedMin));
                //oper.Create(FileName, "R轴目标位置", Convert.ToString(步进电机调试R.Pos));
                oper.Create("R轴目标速度", Convert.ToString(步进电机调试R.Speed));
                oper.Create("R轴Jog速度", Convert.ToString(步进电机调试R.JogSpeed));

                oper.Create("Z轴限位max", Convert.ToString(步进电机调试Z.PosMax));
                oper.Create("Z轴限位min", Convert.ToString(步进电机调试Z.PosMin));
                oper.Create("Z轴速度max", Convert.ToString(步进电机调试Z.SpeedMax));
                oper.Create("Z轴速度min", Convert.ToString(步进电机调试Z.SpeedMin));
                oper.Create("Z轴Jog速度max", Convert.ToString(步进电机调试Z.JogSpeedMax));
                oper.Create("Z轴Jog速度min", Convert.ToString(步进电机调试Z.JogSpeedMin));
                //oper.Create(FileName, "Z轴目标位置", Convert.ToString(步进电机调试Z.Pos));
                oper.Create("Z轴目标速度", Convert.ToString(步进电机调试Z.Speed));
                oper.Create("Z轴Jog速度", Convert.ToString(步进电机调试Z.JogSpeed));

                oper.Create("LX轴限位max", Convert.ToString(步进电机调试LX.PosMax));
                oper.Create("LX轴限位min", Convert.ToString(步进电机调试LX.PosMin));
                oper.Create("LX轴速度max", Convert.ToString(步进电机调试LX.SpeedMax));
                oper.Create("LX轴速度min", Convert.ToString(步进电机调试LX.SpeedMin));
                oper.Create("LX轴Jog速度max", Convert.ToString(步进电机调试LX.JogSpeedMax));
                oper.Create("LX轴Jog速度min", Convert.ToString(步进电机调试LX.JogSpeedMin));
                //oper.Create(FileName, "LX轴目标位置", Convert.ToString(步进电机调试LX.Pos));
                oper.Create("LX轴目标速度", Convert.ToString(步进电机调试LX.Speed));
                oper.Create("LX轴Jog速度", Convert.ToString(步进电机调试LX.JogSpeed));

                oper.Create("LY轴限位max", Convert.ToString(步进电机调试LY.PosMax));
                oper.Create("LY轴限位min", Convert.ToString(步进电机调试LY.PosMin));
                oper.Create("LY轴速度max", Convert.ToString(步进电机调试LY.SpeedMax));
                oper.Create("LY轴速度min", Convert.ToString(步进电机调试LY.SpeedMin));
                oper.Create("LY轴Jog速度max", Convert.ToString(步进电机调试LY.JogSpeedMax));
                oper.Create("LY轴Jog速度min", Convert.ToString(步进电机调试LY.JogSpeedMin));
                //oper.Create(FileName, "LY轴目标位置", Convert.ToString(步进电机调试LY.Pos));
                oper.Create("LY轴目标速度", Convert.ToString(步进电机调试LY.Speed));
                oper.Create("LY轴Jog速度", Convert.ToString(步进电机调试LY.JogSpeed));


                oper.Create("LZ轴限位max", Convert.ToString(步进电机调试LZ.PosMax));
                oper.Create("LZ轴限位min", Convert.ToString(步进电机调试LZ.PosMin));
                oper.Create("LZ轴速度max", Convert.ToString(步进电机调试LZ.SpeedMax));
                oper.Create("LZ轴速度min", Convert.ToString(步进电机调试LZ.SpeedMin));
                oper.Create("LZ轴Jog速度max", Convert.ToString(步进电机调试LZ.JogSpeedMax));
                oper.Create("LZ轴Jog速度min", Convert.ToString(步进电机调试LZ.JogSpeedMin));
                //oper.Create(FileName, "LZ轴目标位置", Convert.ToString(步进电机调试LZ.Pos));
                oper.Create("LZ轴目标速度", Convert.ToString(步进电机调试LZ.Speed));
                oper.Create("LZ轴Jog速度", Convert.ToString(步进电机调试LZ.JogSpeed));

                oper.Create("RX轴限位max", Convert.ToString(步进电机调试RX.PosMax));
                oper.Create("RX轴限位min", Convert.ToString(步进电机调试RX.PosMin));
                oper.Create("RX轴速度max", Convert.ToString(步进电机调试RX.SpeedMax));
                oper.Create("RX轴速度min", Convert.ToString(步进电机调试RX.SpeedMin));
                oper.Create("RX轴Jog速度max", Convert.ToString(步进电机调试RX.JogSpeedMax));
                oper.Create("RX轴Jog速度min", Convert.ToString(步进电机调试RX.JogSpeedMin));
                //oper.Create(FileName, "RX轴目标位置", Convert.ToString(步进电机调试RX.Pos));
                oper.Create("RX轴目标速度", Convert.ToString(步进电机调试RX.Speed));
                oper.Create("RX轴Jog速度", Convert.ToString(步进电机调试RX.JogSpeed));

                oper.Create("RY轴限位max", Convert.ToString(步进电机调试RY.PosMax));
                oper.Create("RY轴限位min", Convert.ToString(步进电机调试RY.PosMin));
                oper.Create("RY轴速度max", Convert.ToString(步进电机调试RY.SpeedMax));
                oper.Create("RY轴速度min", Convert.ToString(步进电机调试RY.SpeedMin));
                oper.Create("RY轴Jog速度max", Convert.ToString(步进电机调试RY.JogSpeedMax));
                oper.Create("RY轴Jog速度min", Convert.ToString(步进电机调试RY.JogSpeedMin));
                //oper.Create(FileName, "RY轴目标位置", Convert.ToString(步进电机调试RY.Pos));
                oper.Create("RY轴目标速度", Convert.ToString(步进电机调试RY.Speed));
                oper.Create("RY轴Jog速度", Convert.ToString(步进电机调试RY.JogSpeed));

                oper.Create("RZ轴限位max", Convert.ToString(步进电机调试RZ.PosMax));
                oper.Create("RZ轴限位min", Convert.ToString(步进电机调试RZ.PosMin));
                oper.Create("RZ轴速度max", Convert.ToString(步进电机调试RZ.SpeedMax));
                oper.Create("RZ轴速度min", Convert.ToString(步进电机调试RZ.SpeedMin));
                oper.Create("RZ轴Jog速度max", Convert.ToString(步进电机调试RZ.JogSpeedMax));
                oper.Create("RZ轴Jog速度min", Convert.ToString(步进电机调试RZ.JogSpeedMin));
                //oper.Create(FileName, "RZ轴目标位置", Convert.ToString(步进电机调试RZ.Pos));
                oper.Create("RZ轴目标速度", Convert.ToString(步进电机调试RZ.Speed));
                oper.Create("RZ轴Jog速度", Convert.ToString(步进电机调试RZ.JogSpeed));

                oper.Create("LX轴标定位置", TxtLX.Text);
                oper.Create("LY轴标定位置", TxtLY.Text);
                oper.Create("LZ轴标定位置", TxtLZ.Text);
                oper.Create("RX轴标定位置", TxtRX.Text);
                oper.Create("RY轴标定位置", TxtRY.Text);
                oper.Create("RZ轴标定位置", TxtRZ.Text);

                oper.Create("Z轴原点偏移位置", TxtZOffset.Text);
                oper.Create("相机偏移量", TxtCameraOffset.Text);
                oper.Create("下透明片拍照偏移量", TxtBottomPhotoOffset.Text);
                oper.Create("上晶圆吸附间隙", TxtTopGap.Text);
                oper.Create("下晶圆吸附间隙", TxtBottomGap.Text);




                oper.Save();
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message.ToString());
                return false;
            }
        }

        private void SaveConfigurations()
        {
            try
            {
                // 保存标定文件
                string path = Application.StartupPath + "CalPara\\CalPara.json";
                bool Res = WriteToCalJSON(path);
                if (Res)
                {
                    FHeaderMainFooter.frmM.LalSoftwareStateTest = "标定文件保存成功";
                    _loggingService.LogInformation($"保存标定参数文件成功", EventIds.Save_Calibration_Config_Success);
                }
                else
                {
                    FHeaderMainFooter.frmM.LalSoftwareStateTest = "标定文件保存失败";
                    _loggingService.LogError($"保存标定参数文件失败", EventIds.Save_Calibration_Config_Failed);
                }

                // 保存系统文件
                path = Application.StartupPath + "SysPara\\ConfPara_Page3.json";
                Res = WriteToSysJSON(path);
                if (Res)
                {
                    FHeaderMainFooter.frmM.LalSoftwareStateTest = "运动界面的系统文件保存成功";
                    _loggingService.LogInformation($"运动界面的系统文件保存成功", EventIds.Save_Equip_Movement_Config_Success);
                }
                else
                {
                    FHeaderMainFooter.frmM.LalSoftwareStateTest = "运动界面的系统文件保存失败";
                    _loggingService.LogError($"运动界面的系统文件保存失败", EventIds.Save_Equip_Movement_Config_Failed);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"保存配置文件时发生错误: {ex.Message}", EventIds.Save_Configuration_Error);
            }
        }

        private void AllAxisControlCleanUp()
        {
            try
            {
                步进电机调试X?.CleanUp();
                步进电机调试Y?.CleanUp();
                步进电机调试R?.CleanUp();

                // 取消标定位运动操作（已迁移到Task-based异步模式）
                _calPosCts?.Cancel();

                // TODO: 注销事件/回调（请补充具体方法和参数）
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"清理轴控制资源时发生错误: {ex.Message}", EventIds.Axis_Control_Cleanup_Error);
            }
        }

        protected override void OnDispose()
        {
            try
            {
                // ?? 关键改进：立即设置关闭标志，这是第一优先级
                _isClosing = true;

                // 立即将字段设为null，防止其他线程访问
                var updateTimer = _UpdateTimer;
                var calibrateTimer = _CalibrateTimer;
                var inputTimer = _InputTimer;
                var calTimer = _CalTimer;
                _UpdateTimer = null;
                _CalibrateTimer = null;
                _InputTimer = null;
                _CalTimer = null;

                // ?? 立即停止所有Timer，防止新的回调启动
                try
                {
                    if (updateTimer != null)
                    {
                        updateTimer.Stop();
                        updateTimer.Dispose();
                    }
                    if (calibrateTimer != null)
                    {
                        calibrateTimer.Stop();
                        calibrateTimer.Dispose();
                    }
                    if (inputTimer != null)
                    {
                        inputTimer.Stop();
                        inputTimer.Dispose();
                    }
                    if (calTimer != null)
                    {
                        calTimer.Stop();
                        calTimer.Dispose();
                    }
                    if (_CalTimer != null)
                    {
                        _CalTimer.Stop();
                        // 不能设置Enabled属性，它是只读的
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogWarning($"停止Timer时发生错误: {ex.Message}", EventIds.Timer_Stop_Error);
                }

                // ?? 等待稍长时间，确保正在执行的Timer回调能够检查标志并安全退出
                System.Threading.Thread.Sleep(150);

                // ? 所有Timer和BackgroundWorker已注册到BasePage，会自动清理
                // 只需要处理特殊的业务逻辑清理

                // 取消CancellationTokenSource
                try
                {
                    if (_cts != null)
                    {
                        _cts.Cancel();
                        _cts.Dispose();
                        _cts = null;
                    }

                    // 取消标定位运动的取消令牌源
                    if (_calPosCts != null)
                    {
                        _calPosCts.Cancel();
                        _calPosCts.Dispose();
                        _calPosCts = null;
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError(ex, "清理CancellationTokenSource时发生错误", EventIds.Resource_Released);
                }

                // 清理轴控制资源
                AllAxisControlCleanUp();

                // 清理PLC变量监听
                VariableChangeActions.Clear();

                _loggingService?.LogInformation("FTitlePage3资源清理完成", EventIds.CleanupComplete);

                base.OnDispose();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "FTitlePage3清理资源时发生错误", EventIds.Resource_Released);
            }
        }

        // ? 保持向后兼容
        public void CleanUp()
        {
            // 转发到新的资源管理机制
            this.Dispose();
        }

        #endregion

        #region  TabControl
        private void TabControl1_DrawItem(object sender, DrawItemEventArgs e)
        {
            TabControl temp = (TabControl)sender;

            Graphics g = e.Graphics;
            //修改tabcontrol的背景颜色
            //g.FillRectangle(new SolidBrush(SystemColors.Control), ClientRectangle);

            Font font = new("宋体", 12f, FontStyle.Regular);

            for (int i = 0; i < temp.TabCount; i++)
            {
                Rectangle rect = temp.GetTabRect(i);

                StringFormat sf = new()
                {
                    LineAlignment = StringAlignment.Center,
                    Alignment = StringAlignment.Near
                };//封装文本布局信息 
                  //写字                
                g.DrawString(temp.TabPages[i].Text, font, SystemBrushes.ControlText, rect, sf);
                //恢复原始变换
                //g.Transform = matxSave;                
            }
            g.Dispose();
        }

        #endregion  TabControl

        void BtnManage(bool EnableState)
        {
            BtnCalPos.Enabled = EnableState;
            //BtnFiveStop.Enabled = EnableState;
            BtnCal.Enabled = EnableState;
            uiTabControlMenu1.Enabled = EnableState;
        }

        private async void BtnTopWafer_Release_Click(object sender, EventArgs e)
        {
            BtnTopWafer_Release.Enabled = false;
            // 使用_axisEventService替代静态引用，控制上晶圆气缸
            await _axisEventService.CylinderControlAsync("TOPWAFER", (int)CylinderStatus.Open);
            BtnTopWafer_Release.Enabled = true;
        }

        private void FTitlePage3_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // ?? 关键改进：在FormClosing的第一时间就设置关闭标志
                _isClosing = true;

                // 立即将字段设为null，防止其他线程访问
                var updateTimer = _UpdateTimer;
                var calibrateTimer = _CalibrateTimer;
                var inputTimer = _InputTimer;
                var calTimer = _CalTimer;
                _UpdateTimer = null;
                _CalibrateTimer = null;
                _InputTimer = null;
                _CalTimer = null;

                // ?? 立即停止所有Timer，防止在保存配置期间有Timer回调执行
                try
                {
                    if (updateTimer != null)
                    {
                        updateTimer.Stop();
                        updateTimer.Dispose();
                    }
                    if (calibrateTimer != null)
                    {
                        calibrateTimer.Stop();
                        calibrateTimer.Dispose();
                    }
                    if (inputTimer != null)
                    {
                        inputTimer.Stop();
                        inputTimer.Dispose();
                    }
                    if (calTimer != null)
                    {
                        calTimer.Stop();
                        calTimer.Dispose();
                    }
                    if (_CalTimer != null)
                    {
                        _CalTimer.Stop();
                        // 不能设置Enabled属性，它是只读的
                    }

                    // 取消标定位运动的取消令牌源
                    if (_calPosCts != null)
                    {
                        _calPosCts.Cancel();
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogWarning($"停止Timer时发生错误: {ex.Message}", EventIds.Timer_Stop_Error_In_Form_Closing);
                }

                // 保存配置
                SaveConfigurations();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"窗体关闭时发生错误: {ex.Message}", EventIds.Form_Closing_Error);
            }
        }


    }
}
