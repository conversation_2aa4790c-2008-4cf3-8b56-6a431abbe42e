﻿namespace Sunny.UI.Demo
{
    partial class FTitlePage1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            
            
            components = new System.ComponentModel.Container();
            gbx相机微调 = new GroupBox();
            tableLayoutPanel3 = new TableLayoutPanel();
            panel37 = new Panel();
            Lal当前位置 = new Label();
            panel44 = new Panel();
            Lal目标位置 = new Label();
            panel46 = new Panel();
            LalPos = new Label();
            panel55 = new Panel();
            label1 = new Label();
            panel56 = new Panel();
            label2 = new Label();
            panel59 = new Panel();
            TxtCurLZ = new TextBox();
            panel60 = new Panel();
            TxtCurRZ = new TextBox();
            panel61 = new Panel();
            TxtTargetLZ = new UITextBox();
            panel62 = new Panel();
            TxtTargetRZ = new UITextBox();
            panel63 = new Panel();
            BtnPosLZ = new Button();
            panel64 = new Panel();
            BtnPosRZ = new Button();
            panel65 = new Panel();
            LalStop = new Label();
            panel66 = new Panel();
            BtnStopLZ = new Button();
            panel67 = new Panel();
            BtnStopRZ = new Button();
            panel71 = new Panel();
            LalAxisName = new Label();
            panel25 = new Panel();
            LalFront = new Label();
            panel26 = new Panel();
            LalBack = new Label();
            panel27 = new Panel();
            BtnFrontLZ = new Button();
            panel28 = new Panel();
            BtnBackLZ = new Button();
            panel29 = new Panel();
            BtnFrontRZ = new Button();
            panel30 = new Panel();
            BtnBackRZ = new Button();
            panel57 = new Panel();
            label3 = new Label();
            panel54 = new Panel();
            label7 = new Label();
            panel3 = new Panel();
            label4 = new Label();
            panel33 = new Panel();
            TxtCurX = new TextBox();
            panel48 = new Panel();
            TxtCurY = new TextBox();
            panel5 = new Panel();
            TxtCurR = new TextBox();
            panel45 = new Panel();
            TxtTargetX = new UITextBox();
            panel49 = new Panel();
            TxtTargetY = new UITextBox();
            panel17 = new Panel();
            TxtTargetR = new UITextBox();
            panel47 = new Panel();
            BtnPosX = new Button();
            panel68 = new Panel();
            BtnStopX = new Button();
            panel41 = new Panel();
            BtnBackX = new Button();
            panel50 = new Panel();
            BtnPosY = new Button();
            panel69 = new Panel();
            BtnStopY = new Button();
            panel42 = new Panel();
            BtnFrontX = new Button();
            panel31 = new Panel();
            BtnFrontY = new Button();
            panel72 = new Panel();
            BtnBackY = new Button();
            panel23 = new Panel();
            BtnPosR = new Button();
            panel24 = new Panel();
            BtnStopR = new Button();
            panel73 = new Panel();
            BtnFrontR = new Button();
            panel74 = new Panel();
            BtnBackR = new Button();
            panel58 = new Panel();
            label8 = new Label();
            panel51 = new Panel();
            TxtCurZ = new TextBox();
            panel52 = new Panel();
            TxtTargetZ = new UITextBox();
            panel53 = new Panel();
            BtnPosZ = new Button();
            panel70 = new Panel();
            BtnStopZ = new Button();
            panel75 = new Panel();
            BtnFrontZ = new Button();
            panel76 = new Panel();
            BtnBackZ = new Button();
            gbx键合对准 = new GroupBox();
            tableLayoutPanel1 = new TableLayoutPanel();
            panel87 = new Panel();
            panel84 = new Panel();
            label12 = new Label();
            button10 = new Button();
            panel79 = new Panel();
            label14 = new Label();
            TxtTopThick = new UITextBox();
            LalTopThick = new Label();
            panel2 = new Panel();
            BtnTopWaferPhotoPos = new Button();
            panel4 = new Panel();
            label6 = new Label();
            button6 = new Button();
            panel9 = new Panel();
            BtnTopTrayWaferOuter = new Button();
            panel8 = new Panel();
            label9 = new Label();
            button9 = new Button();
            panel1 = new Panel();
            lalTextName = new Label();
            button1 = new Button();
            panel32 = new Panel();
            label5 = new Label();
            button4 = new Button();
            panel34 = new Panel();
            btnOpen = new Button();
            panel35 = new Panel();
            txtFileName = new TextBox();
            panel22 = new Panel();
            BtnAllStop = new Button();
            panel39 = new Panel();
            TxtName = new UITextBox();
            lalName = new Label();
            panel80 = new Panel();
            TxtSpacerThick = new UITextBox();
            label10 = new Label();
            label16 = new Label();
            panel38 = new Panel();
            label15 = new Label();
            TxtBottomThick = new UITextBox();
            lalBottomThick = new Label();
            panel7 = new Panel();
            BtnTopWaferUp = new Button();
            panel82 = new Panel();
            BtnTopWaferPhoto = new Button();
            panel10 = new Panel();
            BtnBottomTrayWaferOuter = new Button();
            panel12 = new Panel();
            BtnTopWaferTakeUp = new Button();
            panel14 = new Panel();
            BtnBottomWaferDown = new Button();
            panel19 = new Panel();
            BtnAignMore = new Button();
            panel85 = new Panel();
            BtnBottomWaferTakeDown = new Button();
            panel13 = new Panel();
            BtnXYRZReset = new Button();
            panel88 = new Panel();
            BtnChuckLock = new Button();
            panel21 = new Panel();
            BtnChuckUnLock = new Button();
            tmrAlign = new System.Windows.Forms.Timer(components);
            uiTableLayoutPanel1 = new UITableLayoutPanel();
            uiTableLayoutPanel2 = new UITableLayoutPanel();
            uiTitlePanel1 = new UITitlePanel();
            RichTextBoxLog = new UIRichTextBox();
            gbx相机微调.SuspendLayout();
            tableLayoutPanel3.SuspendLayout();
            panel37.SuspendLayout();
            panel44.SuspendLayout();
            panel46.SuspendLayout();
            panel55.SuspendLayout();
            panel56.SuspendLayout();
            panel59.SuspendLayout();
            panel60.SuspendLayout();
            panel61.SuspendLayout();
            panel62.SuspendLayout();
            panel63.SuspendLayout();
            panel64.SuspendLayout();
            panel65.SuspendLayout();
            panel66.SuspendLayout();
            panel67.SuspendLayout();
            panel71.SuspendLayout();
            panel25.SuspendLayout();
            panel26.SuspendLayout();
            panel27.SuspendLayout();
            panel28.SuspendLayout();
            panel29.SuspendLayout();
            panel30.SuspendLayout();
            panel57.SuspendLayout();
            panel54.SuspendLayout();
            panel3.SuspendLayout();
            panel33.SuspendLayout();
            panel48.SuspendLayout();
            panel5.SuspendLayout();
            panel45.SuspendLayout();
            panel49.SuspendLayout();
            panel17.SuspendLayout();
            panel47.SuspendLayout();
            panel68.SuspendLayout();
            panel41.SuspendLayout();
            panel50.SuspendLayout();
            panel69.SuspendLayout();
            panel42.SuspendLayout();
            panel31.SuspendLayout();
            panel72.SuspendLayout();
            panel23.SuspendLayout();
            panel24.SuspendLayout();
            panel73.SuspendLayout();
            panel74.SuspendLayout();
            panel58.SuspendLayout();
            panel51.SuspendLayout();
            panel52.SuspendLayout();
            panel53.SuspendLayout();
            panel70.SuspendLayout();
            panel75.SuspendLayout();
            panel76.SuspendLayout();
            gbx键合对准.SuspendLayout();
            tableLayoutPanel1.SuspendLayout();
            panel84.SuspendLayout();
            panel79.SuspendLayout();
            panel2.SuspendLayout();
            panel4.SuspendLayout();
            panel9.SuspendLayout();
            panel8.SuspendLayout();
            panel1.SuspendLayout();
            panel32.SuspendLayout();
            panel34.SuspendLayout();
            panel35.SuspendLayout();
            panel22.SuspendLayout();
            panel39.SuspendLayout();
            panel80.SuspendLayout();
            panel38.SuspendLayout();
            panel7.SuspendLayout();
            panel82.SuspendLayout();
            panel10.SuspendLayout();
            panel12.SuspendLayout();
            panel14.SuspendLayout();
            panel19.SuspendLayout();
            panel85.SuspendLayout();
            panel13.SuspendLayout();
            panel88.SuspendLayout();
            panel21.SuspendLayout();
            uiTableLayoutPanel1.SuspendLayout();
            uiTableLayoutPanel2.SuspendLayout();
            uiTitlePanel1.SuspendLayout();
            SuspendLayout();
            // 
            // gbx相机微调
            // 
            gbx相机微调.Controls.Add(tableLayoutPanel3);
            gbx相机微调.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            gbx相机微调.Location = new Point(953, 3);
            gbx相机微调.Name = "gbx相机微调";
            gbx相机微调.Size = new Size(871, 807);
            gbx相机微调.TabIndex = 81;
            gbx相机微调.TabStop = false;
            gbx相机微调.Text = "轴微调";
            // 
            // tableLayoutPanel3
            // 
            tableLayoutPanel3.ColumnCount = 7;
            tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 7.07159F));
            tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 13.7574539F));
            tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 13.7574577F));
            tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.3533745F));
            tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.3533745F));
            tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.3533745F));
            tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.3533745F));
            tableLayoutPanel3.Controls.Add(panel37, 1, 0);
            tableLayoutPanel3.Controls.Add(panel44, 2, 0);
            tableLayoutPanel3.Controls.Add(panel46, 3, 0);
            tableLayoutPanel3.Controls.Add(panel55, 0, 5);
            tableLayoutPanel3.Controls.Add(panel56, 0, 6);
            tableLayoutPanel3.Controls.Add(panel59, 1, 5);
            tableLayoutPanel3.Controls.Add(panel60, 1, 6);
            tableLayoutPanel3.Controls.Add(panel61, 2, 5);
            tableLayoutPanel3.Controls.Add(panel62, 2, 6);
            tableLayoutPanel3.Controls.Add(panel63, 3, 5);
            tableLayoutPanel3.Controls.Add(panel64, 3, 6);
            tableLayoutPanel3.Controls.Add(panel65, 4, 0);
            tableLayoutPanel3.Controls.Add(panel66, 4, 5);
            tableLayoutPanel3.Controls.Add(panel67, 4, 6);
            tableLayoutPanel3.Controls.Add(panel71, 0, 0);
            tableLayoutPanel3.Controls.Add(panel25, 5, 0);
            tableLayoutPanel3.Controls.Add(panel26, 6, 0);
            tableLayoutPanel3.Controls.Add(panel27, 5, 5);
            tableLayoutPanel3.Controls.Add(panel28, 6, 5);
            tableLayoutPanel3.Controls.Add(panel29, 5, 6);
            tableLayoutPanel3.Controls.Add(panel30, 6, 6);
            tableLayoutPanel3.Controls.Add(panel57, 0, 1);
            tableLayoutPanel3.Controls.Add(panel54, 0, 2);
            tableLayoutPanel3.Controls.Add(panel3, 0, 3);
            tableLayoutPanel3.Controls.Add(panel33, 1, 1);
            tableLayoutPanel3.Controls.Add(panel48, 1, 2);
            tableLayoutPanel3.Controls.Add(panel5, 1, 3);
            tableLayoutPanel3.Controls.Add(panel45, 2, 1);
            tableLayoutPanel3.Controls.Add(panel49, 2, 2);
            tableLayoutPanel3.Controls.Add(panel17, 2, 3);
            tableLayoutPanel3.Controls.Add(panel47, 3, 1);
            tableLayoutPanel3.Controls.Add(panel68, 4, 1);
            tableLayoutPanel3.Controls.Add(panel41, 6, 1);
            tableLayoutPanel3.Controls.Add(panel50, 3, 2);
            tableLayoutPanel3.Controls.Add(panel69, 4, 2);
            tableLayoutPanel3.Controls.Add(panel42, 5, 1);
            tableLayoutPanel3.Controls.Add(panel31, 5, 2);
            tableLayoutPanel3.Controls.Add(panel72, 6, 2);
            tableLayoutPanel3.Controls.Add(panel23, 3, 3);
            tableLayoutPanel3.Controls.Add(panel24, 4, 3);
            tableLayoutPanel3.Controls.Add(panel73, 5, 3);
            tableLayoutPanel3.Controls.Add(panel74, 6, 3);
            tableLayoutPanel3.Controls.Add(panel58, 0, 4);
            tableLayoutPanel3.Controls.Add(panel51, 1, 4);
            tableLayoutPanel3.Controls.Add(panel52, 2, 4);
            tableLayoutPanel3.Controls.Add(panel53, 3, 4);
            tableLayoutPanel3.Controls.Add(panel70, 4, 4);
            tableLayoutPanel3.Controls.Add(panel75, 5, 4);
            tableLayoutPanel3.Controls.Add(panel76, 6, 4);
            tableLayoutPanel3.Location = new Point(40, 34);
            tableLayoutPanel3.Name = "tableLayoutPanel3";
            tableLayoutPanel3.RowCount = 7;
            tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 14.2834558F));
            tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 14.2881556F));
            tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 14.2881527F));
            tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 14.2881527F));
            tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 14.2851553F));
            tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 14.2834625F));
            tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 14.2834673F));
            tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            tableLayoutPanel3.Size = new Size(790, 750);
            tableLayoutPanel3.TabIndex = 3;
            // 
            // panel37
            // 
            panel37.Controls.Add(Lal当前位置);
            panel37.Dock = DockStyle.Fill;
            panel37.Location = new Point(58, 3);
            panel37.Name = "panel37";
            panel37.Size = new Size(102, 101);
            panel37.TabIndex = 0;
            // 
            // Lal当前位置
            // 
            Lal当前位置.AutoSize = true;
            Lal当前位置.Location = new Point(16, 44);
            Lal当前位置.Name = "Lal当前位置";
            Lal当前位置.Size = new Size(106, 24);
            Lal当前位置.TabIndex = 0;
            Lal当前位置.Text = "当前位置";
            // 
            // panel44
            // 
            panel44.Controls.Add(Lal目标位置);
            panel44.Dock = DockStyle.Fill;
            panel44.Location = new Point(166, 3);
            panel44.Name = "panel44";
            panel44.Size = new Size(102, 101);
            panel44.TabIndex = 15;
            // 
            // Lal目标位置
            // 
            Lal目标位置.AutoSize = true;
            Lal目标位置.Location = new Point(16, 44);
            Lal目标位置.Name = "Lal目标位置";
            Lal目标位置.Size = new Size(106, 24);
            Lal目标位置.TabIndex = 0;
            Lal目标位置.Text = "目标位置";
            // 
            // panel46
            // 
            panel46.Controls.Add(LalPos);
            panel46.Dock = DockStyle.Fill;
            panel46.Location = new Point(274, 3);
            panel46.Name = "panel46";
            panel46.Size = new Size(123, 101);
            panel46.TabIndex = 17;
            // 
            // LalPos
            // 
            LalPos.AutoSize = true;
            LalPos.Location = new Point(42, 44);
            LalPos.Name = "LalPos";
            LalPos.Size = new Size(58, 24);
            LalPos.TabIndex = 2;
            LalPos.Text = "定位";
            // 
            // panel55
            // 
            panel55.Controls.Add(label1);
            panel55.Dock = DockStyle.Fill;
            panel55.Location = new Point(3, 538);
            panel55.Name = "panel55";
            panel55.Size = new Size(49, 101);
            panel55.TabIndex = 26;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(16, 42);
            label1.Name = "label1";
            label1.Size = new Size(34, 24);
            label1.TabIndex = 2;
            label1.Text = "LZ";
            // 
            // panel56
            // 
            panel56.Controls.Add(label2);
            panel56.Dock = DockStyle.Fill;
            panel56.Location = new Point(3, 645);
            panel56.Name = "panel56";
            panel56.Size = new Size(49, 102);
            panel56.TabIndex = 27;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(16, 43);
            label2.Name = "label2";
            label2.Size = new Size(34, 24);
            label2.TabIndex = 2;
            label2.Text = "RZ";
            // 
            // panel59
            // 
            panel59.Controls.Add(TxtCurLZ);
            panel59.Dock = DockStyle.Fill;
            panel59.Location = new Point(58, 538);
            panel59.Name = "panel59";
            panel59.Size = new Size(102, 101);
            panel59.TabIndex = 31;
            // 
            // TxtCurLZ
            // 
            TxtCurLZ.Enabled = false;
            TxtCurLZ.Location = new Point(4, 37);
            TxtCurLZ.Name = "TxtCurLZ";
            TxtCurLZ.ReadOnly = true;
            TxtCurLZ.Size = new Size(94, 35);
            TxtCurLZ.TabIndex = 1;
            TxtCurLZ.TextAlign = HorizontalAlignment.Center;
            // 
            // panel60
            // 
            panel60.Controls.Add(TxtCurRZ);
            panel60.Dock = DockStyle.Fill;
            panel60.Location = new Point(58, 645);
            panel60.Name = "panel60";
            panel60.Size = new Size(102, 102);
            panel60.TabIndex = 32;
            // 
            // TxtCurRZ
            // 
            TxtCurRZ.Enabled = false;
            TxtCurRZ.Location = new Point(4, 38);
            TxtCurRZ.Name = "TxtCurRZ";
            TxtCurRZ.ReadOnly = true;
            TxtCurRZ.Size = new Size(94, 35);
            TxtCurRZ.TabIndex = 1;
            TxtCurRZ.TextAlign = HorizontalAlignment.Center;
            // 
            // panel61
            // 
            panel61.Controls.Add(TxtTargetLZ);
            panel61.Dock = DockStyle.Fill;
            panel61.Location = new Point(166, 538);
            panel61.Name = "panel61";
            panel61.Size = new Size(102, 101);
            panel61.TabIndex = 33;
            // 
            // TxtTargetLZ
            // 
            TxtTargetLZ.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtTargetLZ.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetLZ.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetLZ.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtTargetLZ.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetLZ.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetLZ.ButtonStyleInherited = false;
            TxtTargetLZ.ButtonSymbolOffset = new Point(0, 0);
            TxtTargetLZ.DecimalPlaces = 3;
            TxtTargetLZ.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtTargetLZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTargetLZ.Location = new Point(4, 37);
            TxtTargetLZ.Margin = new Padding(4, 5, 4, 5);
            TxtTargetLZ.MinimumSize = new Size(1, 16);
            TxtTargetLZ.Name = "TxtTargetLZ";
            TxtTargetLZ.Padding = new Padding(5);
            TxtTargetLZ.RectColor = Color.FromArgb(140, 140, 140);
            TxtTargetLZ.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtTargetLZ.ScrollBarStyleInherited = false;
            TxtTargetLZ.ShowText = false;
            TxtTargetLZ.Size = new Size(95, 26);
            TxtTargetLZ.Style = UIStyle.Custom;
            TxtTargetLZ.TabIndex = 7;
            TxtTargetLZ.Text = "0.000";
            TxtTargetLZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTargetLZ.Type = UITextBox.UIEditType.Double;
            TxtTargetLZ.Watermark = "";
            TxtTargetLZ.KeyUp += Target_KeyUp;
            // 
            // panel62
            // 
            panel62.Controls.Add(TxtTargetRZ);
            panel62.Dock = DockStyle.Fill;
            panel62.Location = new Point(166, 645);
            panel62.Name = "panel62";
            panel62.Size = new Size(102, 102);
            panel62.TabIndex = 34;
            // 
            // TxtTargetRZ
            // 
            TxtTargetRZ.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtTargetRZ.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetRZ.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetRZ.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtTargetRZ.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetRZ.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetRZ.ButtonStyleInherited = false;
            TxtTargetRZ.ButtonSymbolOffset = new Point(0, 0);
            TxtTargetRZ.DecimalPlaces = 3;
            TxtTargetRZ.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtTargetRZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTargetRZ.Location = new Point(4, 38);
            TxtTargetRZ.Margin = new Padding(4, 5, 4, 5);
            TxtTargetRZ.MinimumSize = new Size(1, 16);
            TxtTargetRZ.Name = "TxtTargetRZ";
            TxtTargetRZ.Padding = new Padding(5);
            TxtTargetRZ.RectColor = Color.FromArgb(140, 140, 140);
            TxtTargetRZ.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtTargetRZ.ScrollBarStyleInherited = false;
            TxtTargetRZ.ShowText = false;
            TxtTargetRZ.Size = new Size(95, 26);
            TxtTargetRZ.Style = UIStyle.Custom;
            TxtTargetRZ.TabIndex = 7;
            TxtTargetRZ.Text = "0.000";
            TxtTargetRZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTargetRZ.Type = UITextBox.UIEditType.Double;
            TxtTargetRZ.Watermark = "";
            TxtTargetRZ.KeyUp += Target_KeyUp;
            // 
            // panel63
            // 
            panel63.Controls.Add(BtnPosLZ);
            panel63.Dock = DockStyle.Fill;
            panel63.Location = new Point(274, 538);
            panel63.Name = "panel63";
            panel63.Size = new Size(123, 101);
            panel63.TabIndex = 35;
            // 
            // BtnPosLZ
            // 
            BtnPosLZ.BackColor = Color.FromArgb(224, 224, 224);
            BtnPosLZ.Location = new Point(3, 31);
            BtnPosLZ.Name = "BtnPosLZ";
            BtnPosLZ.Size = new Size(117, 39);
            BtnPosLZ.TabIndex = 88;
            BtnPosLZ.Text = "定位";
            BtnPosLZ.UseVisualStyleBackColor = true;
            BtnPosLZ.Click += BtnPos_Click;
            // 
            // panel64
            // 
            panel64.Controls.Add(BtnPosRZ);
            panel64.Dock = DockStyle.Fill;
            panel64.Location = new Point(274, 645);
            panel64.Name = "panel64";
            panel64.Size = new Size(123, 102);
            panel64.TabIndex = 36;
            // 
            // BtnPosRZ
            // 
            BtnPosRZ.BackColor = Color.FromArgb(224, 224, 224);
            BtnPosRZ.Location = new Point(3, 32);
            BtnPosRZ.Name = "BtnPosRZ";
            BtnPosRZ.Size = new Size(117, 39);
            BtnPosRZ.TabIndex = 88;
            BtnPosRZ.Text = "定位";
            BtnPosRZ.UseVisualStyleBackColor = true;
            BtnPosRZ.Click += BtnPos_Click;
            // 
            // panel65
            // 
            panel65.Controls.Add(LalStop);
            panel65.Dock = DockStyle.Fill;
            panel65.Location = new Point(403, 3);
            panel65.Name = "panel65";
            panel65.Size = new Size(123, 101);
            panel65.TabIndex = 37;
            // 
            // LalStop
            // 
            LalStop.AutoSize = true;
            LalStop.Location = new Point(42, 44);
            LalStop.Name = "LalStop";
            LalStop.Size = new Size(58, 24);
            LalStop.TabIndex = 2;
            LalStop.Text = "停止";
            // 
            // panel66
            // 
            panel66.Controls.Add(BtnStopLZ);
            panel66.Dock = DockStyle.Fill;
            panel66.Location = new Point(403, 538);
            panel66.Name = "panel66";
            panel66.Size = new Size(123, 101);
            panel66.TabIndex = 38;
            // 
            // BtnStopLZ
            // 
            BtnStopLZ.BackColor = Color.FromArgb(224, 224, 224);
            BtnStopLZ.Location = new Point(3, 31);
            BtnStopLZ.Name = "BtnStopLZ";
            BtnStopLZ.Size = new Size(117, 39);
            BtnStopLZ.TabIndex = 88;
            BtnStopLZ.Text = "停止";
            BtnStopLZ.UseVisualStyleBackColor = true;
            BtnStopLZ.Click += BtnStop_Click;
            // 
            // panel67
            // 
            panel67.Controls.Add(BtnStopRZ);
            panel67.Dock = DockStyle.Fill;
            panel67.Location = new Point(403, 645);
            panel67.Name = "panel67";
            panel67.Size = new Size(123, 102);
            panel67.TabIndex = 39;
            // 
            // BtnStopRZ
            // 
            BtnStopRZ.BackColor = Color.FromArgb(224, 224, 224);
            BtnStopRZ.Location = new Point(3, 32);
            BtnStopRZ.Name = "BtnStopRZ";
            BtnStopRZ.Size = new Size(117, 39);
            BtnStopRZ.TabIndex = 88;
            BtnStopRZ.Text = "停止";
            BtnStopRZ.UseVisualStyleBackColor = true;
            BtnStopRZ.Click += BtnStop_Click;
            // 
            // panel71
            // 
            panel71.Controls.Add(LalAxisName);
            panel71.Dock = DockStyle.Fill;
            panel71.Location = new Point(3, 3);
            panel71.Name = "panel71";
            panel71.Size = new Size(49, 101);
            panel71.TabIndex = 43;
            // 
            // LalAxisName
            // 
            LalAxisName.AutoSize = true;
            LalAxisName.Location = new Point(0, 44);
            LalAxisName.Name = "LalAxisName";
            LalAxisName.Size = new Size(82, 24);
            LalAxisName.TabIndex = 1;
            LalAxisName.Text = "轴名称";
            // 
            // panel25
            // 
            panel25.Controls.Add(LalFront);
            panel25.Dock = DockStyle.Fill;
            panel25.Location = new Point(532, 3);
            panel25.Name = "panel25";
            panel25.Size = new Size(123, 101);
            panel25.TabIndex = 49;
            // 
            // LalFront
            // 
            LalFront.AutoSize = true;
            LalFront.Location = new Point(42, 44);
            LalFront.Name = "LalFront";
            LalFront.Size = new Size(58, 24);
            LalFront.TabIndex = 3;
            LalFront.Text = "正转";
            // 
            // panel26
            // 
            panel26.Controls.Add(LalBack);
            panel26.Dock = DockStyle.Fill;
            panel26.Location = new Point(661, 3);
            panel26.Name = "panel26";
            panel26.Size = new Size(126, 101);
            panel26.TabIndex = 50;
            // 
            // LalBack
            // 
            LalBack.AutoSize = true;
            LalBack.Location = new Point(44, 44);
            LalBack.Name = "LalBack";
            LalBack.Size = new Size(58, 24);
            LalBack.TabIndex = 3;
            LalBack.Text = "反转";
            // 
            // panel27
            // 
            panel27.Controls.Add(BtnFrontLZ);
            panel27.Dock = DockStyle.Fill;
            panel27.Location = new Point(532, 538);
            panel27.Name = "panel27";
            panel27.Size = new Size(123, 101);
            panel27.TabIndex = 51;
            // 
            // BtnFrontLZ
            // 
            BtnFrontLZ.BackColor = Color.FromArgb(224, 224, 224);
            BtnFrontLZ.Location = new Point(3, 31);
            BtnFrontLZ.Name = "BtnFrontLZ";
            BtnFrontLZ.Size = new Size(117, 39);
            BtnFrontLZ.TabIndex = 89;
            BtnFrontLZ.Text = "正转";
            BtnFrontLZ.UseVisualStyleBackColor = true;
            BtnFrontLZ.MouseDown += BtnFront_MouseDown;
            BtnFrontLZ.MouseUp += BtnFront_MouseUp;
            // 
            // panel28
            // 
            panel28.Controls.Add(BtnBackLZ);
            panel28.Dock = DockStyle.Fill;
            panel28.Location = new Point(661, 538);
            panel28.Name = "panel28";
            panel28.Size = new Size(126, 101);
            panel28.TabIndex = 52;
            // 
            // BtnBackLZ
            // 
            BtnBackLZ.BackColor = Color.FromArgb(224, 224, 224);
            BtnBackLZ.Location = new Point(5, 31);
            BtnBackLZ.Name = "BtnBackLZ";
            BtnBackLZ.Size = new Size(117, 39);
            BtnBackLZ.TabIndex = 90;
            BtnBackLZ.Text = "反转";
            BtnBackLZ.UseVisualStyleBackColor = true;
            BtnBackLZ.MouseDown += BtnBack_MouseDown;
            BtnBackLZ.MouseUp += BtnBack_MouseUp;
            // 
            // panel29
            // 
            panel29.Controls.Add(BtnFrontRZ);
            panel29.Dock = DockStyle.Fill;
            panel29.Location = new Point(532, 645);
            panel29.Name = "panel29";
            panel29.Size = new Size(123, 102);
            panel29.TabIndex = 53;
            // 
            // BtnFrontRZ
            // 
            BtnFrontRZ.BackColor = Color.FromArgb(224, 224, 224);
            BtnFrontRZ.Location = new Point(3, 32);
            BtnFrontRZ.Name = "BtnFrontRZ";
            BtnFrontRZ.Size = new Size(117, 39);
            BtnFrontRZ.TabIndex = 90;
            BtnFrontRZ.Text = "正转";
            BtnFrontRZ.UseVisualStyleBackColor = true;
            BtnFrontRZ.MouseDown += BtnFront_MouseDown;
            BtnFrontRZ.MouseUp += BtnFront_MouseUp;
            // 
            // panel30
            // 
            panel30.Controls.Add(BtnBackRZ);
            panel30.Dock = DockStyle.Fill;
            panel30.Location = new Point(661, 645);
            panel30.Name = "panel30";
            panel30.Size = new Size(126, 102);
            panel30.TabIndex = 54;
            // 
            // BtnBackRZ
            // 
            BtnBackRZ.BackColor = Color.FromArgb(224, 224, 224);
            BtnBackRZ.Location = new Point(5, 32);
            BtnBackRZ.Name = "BtnBackRZ";
            BtnBackRZ.Size = new Size(117, 39);
            BtnBackRZ.TabIndex = 91;
            BtnBackRZ.Text = "反转";
            BtnBackRZ.UseVisualStyleBackColor = true;
            BtnBackRZ.MouseDown += BtnBack_MouseDown;
            BtnBackRZ.MouseUp += BtnBack_MouseUp;
            // 
            // panel57
            // 
            panel57.Controls.Add(label3);
            panel57.Dock = DockStyle.Fill;
            panel57.Location = new Point(3, 110);
            panel57.Name = "panel57";
            panel57.Size = new Size(49, 101);
            panel57.TabIndex = 28;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(20, 44);
            label3.Name = "label3";
            label3.Size = new Size(22, 24);
            label3.TabIndex = 2;
            label3.Text = "X";
            // 
            // panel54
            // 
            panel54.Controls.Add(label7);
            panel54.Dock = DockStyle.Fill;
            panel54.Location = new Point(3, 217);
            panel54.Name = "panel54";
            panel54.Size = new Size(49, 101);
            panel54.TabIndex = 29;
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new Point(20, 44);
            label7.Name = "label7";
            label7.Size = new Size(22, 24);
            label7.TabIndex = 2;
            label7.Text = "Y";
            // 
            // panel3
            // 
            panel3.Controls.Add(label4);
            panel3.Dock = DockStyle.Fill;
            panel3.Location = new Point(3, 324);
            panel3.Name = "panel3";
            panel3.Size = new Size(49, 101);
            panel3.TabIndex = 44;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new Point(20, 44);
            label4.Name = "label4";
            label4.Size = new Size(22, 24);
            label4.TabIndex = 3;
            label4.Text = "R";
            // 
            // panel33
            // 
            panel33.Controls.Add(TxtCurX);
            panel33.Dock = DockStyle.Fill;
            panel33.Location = new Point(58, 110);
            panel33.Name = "panel33";
            panel33.Size = new Size(102, 101);
            panel33.TabIndex = 14;
            // 
            // TxtCurX
            // 
            TxtCurX.Enabled = false;
            TxtCurX.Location = new Point(4, 39);
            TxtCurX.Name = "TxtCurX";
            TxtCurX.ReadOnly = true;
            TxtCurX.Size = new Size(94, 35);
            TxtCurX.TabIndex = 1;
            TxtCurX.TextAlign = HorizontalAlignment.Center;
            // 
            // panel48
            // 
            panel48.Controls.Add(TxtCurY);
            panel48.Dock = DockStyle.Fill;
            panel48.Location = new Point(58, 217);
            panel48.Name = "panel48";
            panel48.Size = new Size(102, 101);
            panel48.TabIndex = 19;
            // 
            // TxtCurY
            // 
            TxtCurY.Enabled = false;
            TxtCurY.Location = new Point(4, 39);
            TxtCurY.Name = "TxtCurY";
            TxtCurY.ReadOnly = true;
            TxtCurY.Size = new Size(94, 35);
            TxtCurY.TabIndex = 2;
            TxtCurY.TextAlign = HorizontalAlignment.Center;
            // 
            // panel5
            // 
            panel5.Controls.Add(TxtCurR);
            panel5.Dock = DockStyle.Fill;
            panel5.Location = new Point(58, 324);
            panel5.Name = "panel5";
            panel5.Size = new Size(102, 101);
            panel5.TabIndex = 45;
            // 
            // TxtCurR
            // 
            TxtCurR.Enabled = false;
            TxtCurR.Location = new Point(4, 39);
            TxtCurR.Name = "TxtCurR";
            TxtCurR.ReadOnly = true;
            TxtCurR.Size = new Size(94, 35);
            TxtCurR.TabIndex = 3;
            TxtCurR.TextAlign = HorizontalAlignment.Center;
            // 
            // panel45
            // 
            panel45.Controls.Add(TxtTargetX);
            panel45.Dock = DockStyle.Fill;
            panel45.Location = new Point(166, 110);
            panel45.Name = "panel45";
            panel45.Size = new Size(102, 101);
            panel45.TabIndex = 16;
            // 
            // TxtTargetX
            // 
            TxtTargetX.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtTargetX.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetX.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetX.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtTargetX.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetX.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetX.ButtonStyleInherited = false;
            TxtTargetX.ButtonSymbolOffset = new Point(0, 0);
            TxtTargetX.DecimalPlaces = 3;
            TxtTargetX.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtTargetX.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTargetX.Location = new Point(4, 39);
            TxtTargetX.Margin = new Padding(4, 5, 4, 5);
            TxtTargetX.MinimumSize = new Size(1, 16);
            TxtTargetX.Name = "TxtTargetX";
            TxtTargetX.Padding = new Padding(5);
            TxtTargetX.RectColor = Color.FromArgb(140, 140, 140);
            TxtTargetX.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtTargetX.ScrollBarStyleInherited = false;
            TxtTargetX.ShowText = false;
            TxtTargetX.Size = new Size(95, 26);
            TxtTargetX.Style = UIStyle.Custom;
            TxtTargetX.TabIndex = 6;
            TxtTargetX.Text = "0.000";
            TxtTargetX.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTargetX.Type = UITextBox.UIEditType.Double;
            TxtTargetX.Watermark = "";
            TxtTargetX.KeyUp += Target_KeyUp;
            // 
            // panel49
            // 
            panel49.Controls.Add(TxtTargetY);
            panel49.Dock = DockStyle.Fill;
            panel49.Location = new Point(166, 217);
            panel49.Name = "panel49";
            panel49.Size = new Size(102, 101);
            panel49.TabIndex = 20;
            // 
            // TxtTargetY
            // 
            TxtTargetY.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtTargetY.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetY.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetY.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtTargetY.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetY.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetY.ButtonStyleInherited = false;
            TxtTargetY.ButtonSymbolOffset = new Point(0, 0);
            TxtTargetY.DecimalPlaces = 3;
            TxtTargetY.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtTargetY.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTargetY.Location = new Point(4, 39);
            TxtTargetY.Margin = new Padding(4, 5, 4, 5);
            TxtTargetY.MinimumSize = new Size(1, 16);
            TxtTargetY.Name = "TxtTargetY";
            TxtTargetY.Padding = new Padding(5);
            TxtTargetY.RectColor = Color.FromArgb(140, 140, 140);
            TxtTargetY.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtTargetY.ScrollBarStyleInherited = false;
            TxtTargetY.ShowText = false;
            TxtTargetY.Size = new Size(95, 26);
            TxtTargetY.Style = UIStyle.Custom;
            TxtTargetY.TabIndex = 7;
            TxtTargetY.Text = "0.000";
            TxtTargetY.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTargetY.Type = UITextBox.UIEditType.Double;
            TxtTargetY.Watermark = "";
            TxtTargetY.KeyUp += Target_KeyUp;
            // 
            // panel17
            // 
            panel17.Controls.Add(TxtTargetR);
            panel17.Dock = DockStyle.Fill;
            panel17.Location = new Point(166, 324);
            panel17.Name = "panel17";
            panel17.Size = new Size(102, 101);
            panel17.TabIndex = 46;
            // 
            // TxtTargetR
            // 
            TxtTargetR.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtTargetR.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetR.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetR.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtTargetR.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetR.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetR.ButtonStyleInherited = false;
            TxtTargetR.ButtonSymbolOffset = new Point(0, 0);
            TxtTargetR.DecimalPlaces = 3;
            TxtTargetR.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtTargetR.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTargetR.Location = new Point(4, 39);
            TxtTargetR.Margin = new Padding(4, 5, 4, 5);
            TxtTargetR.MinimumSize = new Size(1, 16);
            TxtTargetR.Name = "TxtTargetR";
            TxtTargetR.Padding = new Padding(5);
            TxtTargetR.RectColor = Color.FromArgb(140, 140, 140);
            TxtTargetR.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtTargetR.ScrollBarStyleInherited = false;
            TxtTargetR.ShowText = false;
            TxtTargetR.Size = new Size(95, 26);
            TxtTargetR.Style = UIStyle.Custom;
            TxtTargetR.TabIndex = 7;
            TxtTargetR.Text = "0.000";
            TxtTargetR.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTargetR.Type = UITextBox.UIEditType.Double;
            TxtTargetR.Watermark = "";
            TxtTargetR.KeyUp += Target_KeyUp;
            // 
            // panel47
            // 
            panel47.Controls.Add(BtnPosX);
            panel47.Dock = DockStyle.Fill;
            panel47.Location = new Point(274, 110);
            panel47.Name = "panel47";
            panel47.Size = new Size(123, 101);
            panel47.TabIndex = 18;
            // 
            // BtnPosX
            // 
            BtnPosX.BackColor = Color.FromArgb(224, 224, 224);
            BtnPosX.Location = new Point(3, 33);
            BtnPosX.Name = "BtnPosX";
            BtnPosX.Size = new Size(117, 39);
            BtnPosX.TabIndex = 89;
            BtnPosX.Text = "定位";
            BtnPosX.UseVisualStyleBackColor = true;
            BtnPosX.Click += BtnPos_Click;
            // 
            // panel68
            // 
            panel68.Controls.Add(BtnStopX);
            panel68.Dock = DockStyle.Fill;
            panel68.Location = new Point(403, 110);
            panel68.Name = "panel68";
            panel68.Size = new Size(123, 101);
            panel68.TabIndex = 40;
            // 
            // BtnStopX
            // 
            BtnStopX.BackColor = Color.FromArgb(224, 224, 224);
            BtnStopX.Location = new Point(3, 33);
            BtnStopX.Name = "BtnStopX";
            BtnStopX.Size = new Size(117, 39);
            BtnStopX.TabIndex = 89;
            BtnStopX.Text = "停止";
            BtnStopX.UseVisualStyleBackColor = true;
            BtnStopX.Click += BtnStop_Click;
            // 
            // panel41
            // 
            panel41.Controls.Add(BtnBackX);
            panel41.Dock = DockStyle.Fill;
            panel41.Location = new Point(661, 110);
            panel41.Name = "panel41";
            panel41.Size = new Size(126, 101);
            panel41.TabIndex = 56;
            // 
            // BtnBackX
            // 
            BtnBackX.BackColor = Color.FromArgb(224, 224, 224);
            BtnBackX.Location = new Point(5, 33);
            BtnBackX.Name = "BtnBackX";
            BtnBackX.Size = new Size(117, 39);
            BtnBackX.TabIndex = 91;
            BtnBackX.Text = "反转";
            BtnBackX.UseVisualStyleBackColor = true;
            BtnBackX.MouseDown += BtnBack_MouseDown;
            BtnBackX.MouseUp += BtnBack_MouseUp;
            // 
            // panel50
            // 
            panel50.Controls.Add(BtnPosY);
            panel50.Dock = DockStyle.Fill;
            panel50.Location = new Point(274, 217);
            panel50.Name = "panel50";
            panel50.Size = new Size(123, 101);
            panel50.TabIndex = 21;
            // 
            // BtnPosY
            // 
            BtnPosY.BackColor = Color.FromArgb(224, 224, 224);
            BtnPosY.Location = new Point(3, 33);
            BtnPosY.Name = "BtnPosY";
            BtnPosY.Size = new Size(117, 39);
            BtnPosY.TabIndex = 89;
            BtnPosY.Text = "定位";
            BtnPosY.UseVisualStyleBackColor = true;
            BtnPosY.Click += BtnPos_Click;
            // 
            // panel69
            // 
            panel69.Controls.Add(BtnStopY);
            panel69.Dock = DockStyle.Fill;
            panel69.Location = new Point(403, 217);
            panel69.Name = "panel69";
            panel69.Size = new Size(123, 101);
            panel69.TabIndex = 41;
            // 
            // BtnStopY
            // 
            BtnStopY.BackColor = Color.FromArgb(224, 224, 224);
            BtnStopY.Location = new Point(3, 33);
            BtnStopY.Name = "BtnStopY";
            BtnStopY.Size = new Size(117, 39);
            BtnStopY.TabIndex = 89;
            BtnStopY.Text = "停止";
            BtnStopY.UseVisualStyleBackColor = true;
            BtnStopY.Click += BtnStop_Click;
            // 
            // panel42
            // 
            panel42.Controls.Add(BtnFrontX);
            panel42.Dock = DockStyle.Fill;
            panel42.Location = new Point(532, 110);
            panel42.Name = "panel42";
            panel42.Size = new Size(123, 101);
            panel42.TabIndex = 57;
            // 
            // BtnFrontX
            // 
            BtnFrontX.BackColor = Color.FromArgb(224, 224, 224);
            BtnFrontX.Location = new Point(3, 33);
            BtnFrontX.Name = "BtnFrontX";
            BtnFrontX.Size = new Size(117, 39);
            BtnFrontX.TabIndex = 91;
            BtnFrontX.Text = "正转";
            BtnFrontX.UseVisualStyleBackColor = true;
            BtnFrontX.MouseDown += BtnFront_MouseDown;
            BtnFrontX.MouseUp += BtnFront_MouseUp;
            // 
            // panel31
            // 
            panel31.Controls.Add(BtnFrontY);
            panel31.Dock = DockStyle.Fill;
            panel31.Location = new Point(532, 217);
            panel31.Name = "panel31";
            panel31.Size = new Size(123, 101);
            panel31.TabIndex = 55;
            // 
            // BtnFrontY
            // 
            BtnFrontY.BackColor = Color.FromArgb(224, 224, 224);
            BtnFrontY.Location = new Point(3, 33);
            BtnFrontY.Name = "BtnFrontY";
            BtnFrontY.Size = new Size(117, 39);
            BtnFrontY.TabIndex = 91;
            BtnFrontY.Text = "正转";
            BtnFrontY.UseVisualStyleBackColor = true;
            BtnFrontY.MouseDown += BtnFront_MouseDown;
            BtnFrontY.MouseUp += BtnFront_MouseUp;
            // 
            // panel72
            // 
            panel72.Controls.Add(BtnBackY);
            panel72.Dock = DockStyle.Fill;
            panel72.Location = new Point(661, 217);
            panel72.Name = "panel72";
            panel72.Size = new Size(126, 101);
            panel72.TabIndex = 58;
            // 
            // BtnBackY
            // 
            BtnBackY.BackColor = Color.FromArgb(224, 224, 224);
            BtnBackY.Location = new Point(5, 33);
            BtnBackY.Name = "BtnBackY";
            BtnBackY.Size = new Size(117, 39);
            BtnBackY.TabIndex = 91;
            BtnBackY.Text = "反转";
            BtnBackY.UseVisualStyleBackColor = true;
            BtnBackY.MouseDown += BtnBack_MouseDown;
            BtnBackY.MouseUp += BtnBack_MouseUp;
            // 
            // panel23
            // 
            panel23.Controls.Add(BtnPosR);
            panel23.Dock = DockStyle.Fill;
            panel23.Location = new Point(274, 324);
            panel23.Name = "panel23";
            panel23.Size = new Size(123, 101);
            panel23.TabIndex = 47;
            // 
            // BtnPosR
            // 
            BtnPosR.BackColor = Color.FromArgb(224, 224, 224);
            BtnPosR.Location = new Point(3, 33);
            BtnPosR.Name = "BtnPosR";
            BtnPosR.Size = new Size(117, 39);
            BtnPosR.TabIndex = 90;
            BtnPosR.Text = "定位";
            BtnPosR.UseVisualStyleBackColor = true;
            BtnPosR.Click += BtnPos_Click;
            // 
            // panel24
            // 
            panel24.Controls.Add(BtnStopR);
            panel24.Dock = DockStyle.Fill;
            panel24.Location = new Point(403, 324);
            panel24.Name = "panel24";
            panel24.Size = new Size(123, 101);
            panel24.TabIndex = 48;
            // 
            // BtnStopR
            // 
            BtnStopR.BackColor = Color.FromArgb(224, 224, 224);
            BtnStopR.Location = new Point(3, 33);
            BtnStopR.Name = "BtnStopR";
            BtnStopR.Size = new Size(117, 39);
            BtnStopR.TabIndex = 90;
            BtnStopR.Text = "停止";
            BtnStopR.UseVisualStyleBackColor = true;
            BtnStopR.Click += BtnStop_Click;
            // 
            // panel73
            // 
            panel73.Controls.Add(BtnFrontR);
            panel73.Dock = DockStyle.Fill;
            panel73.Location = new Point(532, 324);
            panel73.Name = "panel73";
            panel73.Size = new Size(123, 101);
            panel73.TabIndex = 59;
            // 
            // BtnFrontR
            // 
            BtnFrontR.BackColor = Color.FromArgb(224, 224, 224);
            BtnFrontR.Location = new Point(3, 33);
            BtnFrontR.Name = "BtnFrontR";
            BtnFrontR.Size = new Size(117, 39);
            BtnFrontR.TabIndex = 91;
            BtnFrontR.Text = "正转";
            BtnFrontR.UseVisualStyleBackColor = true;
            BtnFrontR.MouseDown += BtnFront_MouseDown;
            BtnFrontR.MouseUp += BtnFront_MouseUp;
            // 
            // panel74
            // 
            panel74.Controls.Add(BtnBackR);
            panel74.Dock = DockStyle.Fill;
            panel74.Location = new Point(661, 324);
            panel74.Name = "panel74";
            panel74.Size = new Size(126, 101);
            panel74.TabIndex = 60;
            // 
            // BtnBackR
            // 
            BtnBackR.BackColor = Color.FromArgb(224, 224, 224);
            BtnBackR.Location = new Point(5, 33);
            BtnBackR.Name = "BtnBackR";
            BtnBackR.Size = new Size(117, 39);
            BtnBackR.TabIndex = 91;
            BtnBackR.Text = "反转";
            BtnBackR.UseVisualStyleBackColor = true;
            BtnBackR.MouseDown += BtnBack_MouseDown;
            BtnBackR.MouseUp += BtnBack_MouseUp;
            // 
            // panel58
            // 
            panel58.Controls.Add(label8);
            panel58.Dock = DockStyle.Fill;
            panel58.Location = new Point(3, 431);
            panel58.Name = "panel58";
            panel58.Size = new Size(49, 101);
            panel58.TabIndex = 30;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new Point(20, 44);
            label8.Name = "label8";
            label8.Size = new Size(22, 24);
            label8.TabIndex = 2;
            label8.Text = "Z";
            // 
            // panel51
            // 
            panel51.Controls.Add(TxtCurZ);
            panel51.Dock = DockStyle.Fill;
            panel51.Location = new Point(58, 431);
            panel51.Name = "panel51";
            panel51.Size = new Size(102, 101);
            panel51.TabIndex = 22;
            // 
            // TxtCurZ
            // 
            TxtCurZ.Enabled = false;
            TxtCurZ.Location = new Point(4, 39);
            TxtCurZ.Name = "TxtCurZ";
            TxtCurZ.ReadOnly = true;
            TxtCurZ.Size = new Size(94, 35);
            TxtCurZ.TabIndex = 2;
            TxtCurZ.TextAlign = HorizontalAlignment.Center;
            // 
            // panel52
            // 
            panel52.Controls.Add(TxtTargetZ);
            panel52.Dock = DockStyle.Fill;
            panel52.Location = new Point(166, 431);
            panel52.Name = "panel52";
            panel52.Size = new Size(102, 101);
            panel52.TabIndex = 23;
            // 
            // TxtTargetZ
            // 
            TxtTargetZ.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtTargetZ.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetZ.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetZ.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtTargetZ.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtTargetZ.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtTargetZ.ButtonStyleInherited = false;
            TxtTargetZ.ButtonSymbolOffset = new Point(0, 0);
            TxtTargetZ.DecimalPlaces = 3;
            TxtTargetZ.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtTargetZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTargetZ.Location = new Point(4, 39);
            TxtTargetZ.Margin = new Padding(4, 5, 4, 5);
            TxtTargetZ.MinimumSize = new Size(1, 16);
            TxtTargetZ.Name = "TxtTargetZ";
            TxtTargetZ.Padding = new Padding(5);
            TxtTargetZ.RectColor = Color.FromArgb(140, 140, 140);
            TxtTargetZ.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtTargetZ.ScrollBarStyleInherited = false;
            TxtTargetZ.ShowText = false;
            TxtTargetZ.Size = new Size(95, 26);
            TxtTargetZ.Style = UIStyle.Custom;
            TxtTargetZ.TabIndex = 7;
            TxtTargetZ.Text = "0.000";
            TxtTargetZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTargetZ.Type = UITextBox.UIEditType.Double;
            TxtTargetZ.Watermark = "";
            TxtTargetZ.KeyUp += Target_KeyUp;
            // 
            // panel53
            // 
            panel53.Controls.Add(BtnPosZ);
            panel53.Dock = DockStyle.Fill;
            panel53.Location = new Point(274, 431);
            panel53.Name = "panel53";
            panel53.Size = new Size(123, 101);
            panel53.TabIndex = 24;
            // 
            // BtnPosZ
            // 
            BtnPosZ.BackColor = Color.FromArgb(224, 224, 224);
            BtnPosZ.Location = new Point(3, 33);
            BtnPosZ.Name = "BtnPosZ";
            BtnPosZ.Size = new Size(117, 39);
            BtnPosZ.TabIndex = 89;
            BtnPosZ.Text = "定位";
            BtnPosZ.UseVisualStyleBackColor = true;
            BtnPosZ.Click += BtnPos_Click;
            // 
            // panel70
            // 
            panel70.Controls.Add(BtnStopZ);
            panel70.Dock = DockStyle.Fill;
            panel70.Location = new Point(403, 431);
            panel70.Name = "panel70";
            panel70.Size = new Size(123, 101);
            panel70.TabIndex = 42;
            // 
            // BtnStopZ
            // 
            BtnStopZ.BackColor = Color.FromArgb(224, 224, 224);
            BtnStopZ.Location = new Point(3, 33);
            BtnStopZ.Name = "BtnStopZ";
            BtnStopZ.Size = new Size(117, 39);
            BtnStopZ.TabIndex = 89;
            BtnStopZ.Text = "停止";
            BtnStopZ.UseVisualStyleBackColor = true;
            BtnStopZ.Click += BtnStop_Click;
            // 
            // panel75
            // 
            panel75.Controls.Add(BtnFrontZ);
            panel75.Dock = DockStyle.Fill;
            panel75.Location = new Point(532, 431);
            panel75.Name = "panel75";
            panel75.Size = new Size(123, 101);
            panel75.TabIndex = 61;
            // 
            // BtnFrontZ
            // 
            BtnFrontZ.BackColor = Color.FromArgb(224, 224, 224);
            BtnFrontZ.Location = new Point(3, 33);
            BtnFrontZ.Name = "BtnFrontZ";
            BtnFrontZ.Size = new Size(117, 39);
            BtnFrontZ.TabIndex = 91;
            BtnFrontZ.Text = "正转";
            BtnFrontZ.UseVisualStyleBackColor = true;
            BtnFrontZ.MouseDown += BtnFront_MouseDown;
            BtnFrontZ.MouseUp += BtnFront_MouseUp;
            // 
            // panel76
            // 
            panel76.Controls.Add(BtnBackZ);
            panel76.Dock = DockStyle.Fill;
            panel76.Location = new Point(661, 431);
            panel76.Name = "panel76";
            panel76.Size = new Size(126, 101);
            panel76.TabIndex = 62;
            // 
            // BtnBackZ
            // 
            BtnBackZ.BackColor = Color.FromArgb(224, 224, 224);
            BtnBackZ.Location = new Point(5, 33);
            BtnBackZ.Name = "BtnBackZ";
            BtnBackZ.Size = new Size(117, 39);
            BtnBackZ.TabIndex = 91;
            BtnBackZ.Text = "反转";
            BtnBackZ.UseVisualStyleBackColor = true;
            BtnBackZ.MouseDown += BtnBack_MouseDown;
            BtnBackZ.MouseUp += BtnBack_MouseUp;
            // 
            // gbx键合对准
            // 
            gbx键合对准.Controls.Add(tableLayoutPanel1);
            gbx键合对准.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            gbx键合对准.Location = new Point(3, 3);
            gbx键合对准.Name = "gbx键合对准";
            gbx键合对准.Size = new Size(891, 807);
            gbx键合对准.TabIndex = 82;
            gbx键合对准.TabStop = false;
            gbx键合对准.Text = "键合对准";
            // 
            // tableLayoutPanel1
            // 
            tableLayoutPanel1.ColumnCount = 4;
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            tableLayoutPanel1.Controls.Add(panel87, 3, 8);
            tableLayoutPanel1.Controls.Add(panel84, 0, 7);
            tableLayoutPanel1.Controls.Add(panel79, 3, 1);
            tableLayoutPanel1.Controls.Add(panel2, 0, 4);
            tableLayoutPanel1.Controls.Add(panel4, 0, 5);
            tableLayoutPanel1.Controls.Add(panel9, 1, 4);
            tableLayoutPanel1.Controls.Add(panel8, 0, 9);
            tableLayoutPanel1.Controls.Add(panel1, 0, 3);
            tableLayoutPanel1.Controls.Add(panel32, 0, 0);
            tableLayoutPanel1.Controls.Add(panel34, 0, 1);
            tableLayoutPanel1.Controls.Add(panel35, 1, 1);
            tableLayoutPanel1.Controls.Add(panel22, 0, 2);
            tableLayoutPanel1.Controls.Add(panel39, 2, 1);
            tableLayoutPanel1.Controls.Add(panel80, 2, 2);
            tableLayoutPanel1.Controls.Add(panel38, 3, 2);
            tableLayoutPanel1.Controls.Add(panel7, 2, 4);
            tableLayoutPanel1.Controls.Add(panel82, 0, 6);
            tableLayoutPanel1.Controls.Add(panel10, 2, 6);
            tableLayoutPanel1.Controls.Add(panel12, 1, 6);
            tableLayoutPanel1.Controls.Add(panel14, 0, 8);
            tableLayoutPanel1.Controls.Add(panel19, 1, 8);
            tableLayoutPanel1.Controls.Add(panel85, 2, 8);
            tableLayoutPanel1.Controls.Add(panel13, 0, 10);
            tableLayoutPanel1.Controls.Add(panel88, 1, 10);
            tableLayoutPanel1.Controls.Add(panel21, 2, 10);
            tableLayoutPanel1.Location = new Point(39, 34);
            tableLayoutPanel1.Name = "tableLayoutPanel1";
            tableLayoutPanel1.RowCount = 11;
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.089704F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.089454F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.095037F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.089358F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.089363F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.089363F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.089363F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.095357F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.094275F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.089363F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.089363F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            tableLayoutPanel1.Size = new Size(812, 750);
            tableLayoutPanel1.TabIndex = 84;
            // 
            // panel87
            // 
            panel87.Dock = DockStyle.Fill;
            panel87.Location = new Point(612, 547);
            panel87.Name = "panel87";
            panel87.Size = new Size(197, 62);
            panel87.TabIndex = 102;
            // 
            // panel84
            // 
            tableLayoutPanel1.SetColumnSpan(panel84, 4);
            panel84.Controls.Add(label12);
            panel84.Controls.Add(button10);
            panel84.Dock = DockStyle.Fill;
            panel84.Location = new Point(3, 479);
            panel84.Name = "panel84";
            panel84.Size = new Size(806, 62);
            panel84.TabIndex = 108;
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label12.Location = new Point(53, 23);
            label12.Name = "label12";
            label12.Size = new Size(22, 24);
            label12.TabIndex = 17;
            label12.Text = "3";
            // 
            // button10
            // 
            button10.BackColor = Color.Silver;
            button10.Location = new Point(2, 31);
            button10.Name = "button10";
            button10.Size = new Size(801, 1);
            button10.TabIndex = 15;
            button10.Text = "button10";
            button10.UseVisualStyleBackColor = false;
            // 
            // panel79
            // 
            panel79.Controls.Add(label14);
            panel79.Controls.Add(TxtTopThick);
            panel79.Controls.Add(LalTopThick);
            panel79.Dock = DockStyle.Fill;
            panel79.Location = new Point(612, 71);
            panel79.Name = "panel79";
            panel79.Size = new Size(197, 62);
            panel79.TabIndex = 102;
            // 
            // label14
            // 
            label14.AutoSize = true;
            label14.Location = new Point(173, 23);
            label14.Name = "label14";
            label14.Size = new Size(34, 24);
            label14.TabIndex = 112;
            label14.Text = "mm";
            // 
            // TxtTopThick
            // 
            TxtTopThick.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtTopThick.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtTopThick.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtTopThick.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtTopThick.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtTopThick.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtTopThick.ButtonStyleInherited = false;
            TxtTopThick.ButtonSymbolOffset = new Point(0, 0);
            TxtTopThick.DecimalPlaces = 3;
            TxtTopThick.Enabled = false;
            TxtTopThick.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtTopThick.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTopThick.Location = new Point(85, 18);
            TxtTopThick.Margin = new Padding(4, 5, 4, 5);
            TxtTopThick.MinimumSize = new Size(1, 16);
            TxtTopThick.Name = "TxtTopThick";
            TxtTopThick.Padding = new Padding(5);
            TxtTopThick.RectColor = Color.FromArgb(140, 140, 140);
            TxtTopThick.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtTopThick.ScrollBarStyleInherited = false;
            TxtTopThick.ShowText = false;
            TxtTopThick.Size = new Size(85, 26);
            TxtTopThick.Style = UIStyle.Custom;
            TxtTopThick.TabIndex = 83;
            TxtTopThick.Text = "0.000";
            TxtTopThick.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTopThick.Type = UITextBox.UIEditType.Double;
            TxtTopThick.Watermark = "";
            // 
            // LalTopThick
            // 
            LalTopThick.AutoSize = true;
            LalTopThick.Location = new Point(-2, 23);
            LalTopThick.Name = "LalTopThick";
            LalTopThick.Size = new Size(130, 24);
            LalTopThick.TabIndex = 0;
            LalTopThick.Text = "上晶圆厚度";
            // 
            // panel2
            // 
            panel2.Controls.Add(BtnTopWaferPhotoPos);
            panel2.Dock = DockStyle.Fill;
            panel2.Location = new Point(3, 275);
            panel2.Name = "panel2";
            panel2.Size = new Size(197, 62);
            panel2.TabIndex = 1;
            // 
            // BtnTopWaferPhotoPos
            // 
            BtnTopWaferPhotoPos.BackColor = Color.FromArgb(224, 224, 224);
            BtnTopWaferPhotoPos.Location = new Point(5, 12);
            BtnTopWaferPhotoPos.Name = "BtnTopWaferPhotoPos";
            BtnTopWaferPhotoPos.Size = new Size(186, 39);
            BtnTopWaferPhotoPos.TabIndex = 86;
            BtnTopWaferPhotoPos.Text = "1:相机至上晶圆拍照位";
            BtnTopWaferPhotoPos.UseVisualStyleBackColor = true;
            BtnTopWaferPhotoPos.Click += BtnTopWaferPhotoPos_Click;
            // 
            // panel4
            // 
            tableLayoutPanel1.SetColumnSpan(panel4, 4);
            panel4.Controls.Add(label6);
            panel4.Controls.Add(button6);
            panel4.Dock = DockStyle.Fill;
            panel4.Location = new Point(3, 343);
            panel4.Name = "panel4";
            panel4.Size = new Size(806, 62);
            panel4.TabIndex = 4;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label6.Location = new Point(53, 23);
            label6.Name = "label6";
            label6.Size = new Size(22, 24);
            label6.TabIndex = 11;
            label6.Text = "2";
            // 
            // button6
            // 
            button6.BackColor = Color.Silver;
            button6.Location = new Point(2, 31);
            button6.Name = "button6";
            button6.Size = new Size(801, 1);
            button6.TabIndex = 9;
            button6.Text = "button6";
            button6.UseVisualStyleBackColor = false;
            // 
            // panel9
            // 
            panel9.Controls.Add(BtnTopTrayWaferOuter);
            panel9.Dock = DockStyle.Fill;
            panel9.Location = new Point(206, 275);
            panel9.Name = "panel9";
            panel9.Size = new Size(197, 62);
            panel9.TabIndex = 9;
            // 
            // BtnTopTrayWaferOuter
            // 
            BtnTopTrayWaferOuter.BackColor = Color.FromArgb(224, 224, 224);
            BtnTopTrayWaferOuter.Location = new Point(5, 12);
            BtnTopTrayWaferOuter.Name = "BtnTopTrayWaferOuter";
            BtnTopTrayWaferOuter.Size = new Size(186, 39);
            BtnTopTrayWaferOuter.TabIndex = 85;
            BtnTopTrayWaferOuter.Text = "2:托盘上晶圆吸附";
            BtnTopTrayWaferOuter.UseVisualStyleBackColor = true;
            BtnTopTrayWaferOuter.Click += BtnTrayWaferOuter_Click;
            // 
            // panel8
            // 
            tableLayoutPanel1.SetColumnSpan(panel8, 4);
            panel8.Controls.Add(label9);
            panel8.Controls.Add(button9);
            panel8.Dock = DockStyle.Fill;
            panel8.Location = new Point(3, 615);
            panel8.Name = "panel8";
            panel8.Size = new Size(806, 62);
            panel8.TabIndex = 8;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label9.Location = new Point(53, 23);
            label9.Name = "label9";
            label9.Size = new Size(22, 24);
            label9.TabIndex = 14;
            label9.Text = "4";
            // 
            // button9
            // 
            button9.BackColor = Color.Silver;
            button9.Location = new Point(2, 31);
            button9.Name = "button9";
            button9.Size = new Size(801, 1);
            button9.TabIndex = 12;
            button9.Text = "button9";
            button9.UseVisualStyleBackColor = false;
            // 
            // panel1
            // 
            tableLayoutPanel1.SetColumnSpan(panel1, 4);
            panel1.Controls.Add(lalTextName);
            panel1.Controls.Add(button1);
            panel1.Dock = DockStyle.Fill;
            panel1.Location = new Point(3, 207);
            panel1.Name = "panel1";
            panel1.Size = new Size(806, 62);
            panel1.TabIndex = 0;
            // 
            // lalTextName
            // 
            lalTextName.AutoSize = true;
            lalTextName.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            lalTextName.Location = new Point(53, 23);
            lalTextName.Name = "lalTextName";
            lalTextName.Size = new Size(22, 24);
            lalTextName.TabIndex = 8;
            lalTextName.Text = "1";
            // 
            // button1
            // 
            button1.BackColor = Color.Silver;
            button1.Location = new Point(2, 32);
            button1.Name = "button1";
            button1.Size = new Size(801, 1);
            button1.TabIndex = 6;
            button1.Text = "button1";
            button1.UseVisualStyleBackColor = false;
            // 
            // panel32
            // 
            tableLayoutPanel1.SetColumnSpan(panel32, 4);
            panel32.Controls.Add(label5);
            panel32.Controls.Add(button4);
            panel32.Dock = DockStyle.Fill;
            panel32.Location = new Point(3, 3);
            panel32.Name = "panel32";
            panel32.Size = new Size(806, 62);
            panel32.TabIndex = 92;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label5.Location = new Point(53, 23);
            label5.Name = "label5";
            label5.Size = new Size(154, 24);
            label5.TabIndex = 11;
            label5.Text = "打开参数文件";
            // 
            // button4
            // 
            button4.BackColor = Color.Silver;
            button4.Location = new Point(2, 31);
            button4.Name = "button4";
            button4.Size = new Size(801, 1);
            button4.TabIndex = 9;
            button4.Text = "button4";
            button4.UseVisualStyleBackColor = false;
            // 
            // panel34
            // 
            panel34.Controls.Add(btnOpen);
            panel34.Dock = DockStyle.Fill;
            panel34.Location = new Point(3, 71);
            panel34.Name = "panel34";
            panel34.Size = new Size(197, 62);
            panel34.TabIndex = 93;
            // 
            // btnOpen
            // 
            btnOpen.BackColor = Color.FromArgb(224, 224, 224);
            btnOpen.Location = new Point(5, 12);
            btnOpen.Name = "btnOpen";
            btnOpen.Size = new Size(186, 36);
            btnOpen.TabIndex = 86;
            btnOpen.Text = "打开对准文件";
            btnOpen.UseVisualStyleBackColor = true;
            btnOpen.Click += BtnOpen_Click;
            // 
            // panel35
            // 
            panel35.Controls.Add(txtFileName);
            panel35.Dock = DockStyle.Fill;
            panel35.Location = new Point(206, 71);
            panel35.Name = "panel35";
            tableLayoutPanel1.SetRowSpan(panel35, 2);
            panel35.Size = new Size(197, 130);
            panel35.TabIndex = 94;
            // 
            // txtFileName
            // 
            txtFileName.Dock = DockStyle.Fill;
            txtFileName.Enabled = false;
            txtFileName.Location = new Point(0, 0);
            txtFileName.Multiline = true;
            txtFileName.Name = "txtFileName";
            txtFileName.ScrollBars = ScrollBars.Vertical;
            txtFileName.Size = new Size(197, 130);
            txtFileName.TabIndex = 3;
            // 
            // panel22
            // 
            panel22.Controls.Add(BtnAllStop);
            panel22.Dock = DockStyle.Fill;
            panel22.Location = new Point(3, 139);
            panel22.Name = "panel22";
            panel22.Size = new Size(197, 62);
            panel22.TabIndex = 91;
            // 
            // BtnAllStop
            // 
            BtnAllStop.BackColor = Color.FromArgb(224, 224, 224);
            BtnAllStop.Location = new Point(5, 12);
            BtnAllStop.Name = "BtnAllStop";
            BtnAllStop.Size = new Size(186, 39);
            BtnAllStop.TabIndex = 89;
            BtnAllStop.Text = "停止运动";
            BtnAllStop.UseVisualStyleBackColor = true;
            BtnAllStop.Click += BtnAll停止_Click;
            // 
            // panel39
            // 
            panel39.Controls.Add(TxtName);
            panel39.Controls.Add(lalName);
            panel39.Dock = DockStyle.Fill;
            panel39.Location = new Point(409, 71);
            panel39.Name = "panel39";
            panel39.Size = new Size(197, 62);
            panel39.TabIndex = 97;
            // 
            // TxtName
            // 
            TxtName.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtName.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtName.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtName.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtName.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtName.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtName.ButtonStyleInherited = false;
            TxtName.ButtonSymbolOffset = new Point(0, 0);
            TxtName.Enabled = false;
            TxtName.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtName.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtName.Location = new Point(85, 18);
            TxtName.Margin = new Padding(4, 5, 4, 5);
            TxtName.MinimumSize = new Size(1, 16);
            TxtName.Name = "TxtName";
            TxtName.Padding = new Padding(5);
            TxtName.RectColor = Color.FromArgb(140, 140, 140);
            TxtName.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtName.ScrollBarStyleInherited = false;
            TxtName.ShowText = false;
            TxtName.Size = new Size(85, 26);
            TxtName.Style = UIStyle.Custom;
            TxtName.TabIndex = 1;
            TxtName.TextAlignment = ContentAlignment.MiddleLeft;
            TxtName.Watermark = "";
            // 
            // lalName
            // 
            lalName.AutoSize = true;
            lalName.Location = new Point(11, 23);
            lalName.Name = "lalName";
            lalName.Size = new Size(106, 24);
            lalName.TabIndex = 0;
            lalName.Text = "产品名称";
            // 
            // panel80
            // 
            panel80.Controls.Add(TxtSpacerThick);
            panel80.Controls.Add(label10);
            panel80.Controls.Add(label16);
            panel80.Dock = DockStyle.Fill;
            panel80.Location = new Point(409, 139);
            panel80.Name = "panel80";
            panel80.Size = new Size(197, 62);
            panel80.TabIndex = 103;
            // 
            // TxtSpacerThick
            // 
            TxtSpacerThick.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtSpacerThick.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtSpacerThick.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtSpacerThick.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtSpacerThick.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtSpacerThick.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtSpacerThick.ButtonStyleInherited = false;
            TxtSpacerThick.ButtonSymbolOffset = new Point(0, 0);
            TxtSpacerThick.DecimalPlaces = 3;
            TxtSpacerThick.Enabled = false;
            TxtSpacerThick.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtSpacerThick.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtSpacerThick.Location = new Point(85, 18);
            TxtSpacerThick.Margin = new Padding(4, 5, 4, 5);
            TxtSpacerThick.MinimumSize = new Size(1, 16);
            TxtSpacerThick.Name = "TxtSpacerThick";
            TxtSpacerThick.Padding = new Padding(5);
            TxtSpacerThick.RectColor = Color.FromArgb(140, 140, 140);
            TxtSpacerThick.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtSpacerThick.ScrollBarStyleInherited = false;
            TxtSpacerThick.ShowText = false;
            TxtSpacerThick.Size = new Size(85, 26);
            TxtSpacerThick.Style = UIStyle.Custom;
            TxtSpacerThick.TabIndex = 8;
            TxtSpacerThick.Text = "0.000";
            TxtSpacerThick.TextAlignment = ContentAlignment.MiddleCenter;
            TxtSpacerThick.Type = UITextBox.UIEditType.Double;
            TxtSpacerThick.Watermark = "";
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Location = new Point(12, 23);
            label10.Name = "label10";
            label10.Size = new Size(106, 24);
            label10.TabIndex = 0;
            label10.Text = "隔片厚度";
            // 
            // label16
            // 
            label16.AutoSize = true;
            label16.Location = new Point(173, 23);
            label16.Name = "label16";
            label16.Size = new Size(34, 24);
            label16.TabIndex = 114;
            label16.Text = "mm";
            // 
            // panel38
            // 
            panel38.Controls.Add(label15);
            panel38.Controls.Add(TxtBottomThick);
            panel38.Controls.Add(lalBottomThick);
            panel38.Dock = DockStyle.Fill;
            panel38.Location = new Point(612, 139);
            panel38.Name = "panel38";
            panel38.Size = new Size(197, 62);
            panel38.TabIndex = 96;
            // 
            // label15
            // 
            label15.AutoSize = true;
            label15.Location = new Point(173, 23);
            label15.Name = "label15";
            label15.Size = new Size(34, 24);
            label15.TabIndex = 113;
            label15.Text = "mm";
            // 
            // TxtBottomThick
            // 
            TxtBottomThick.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtBottomThick.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtBottomThick.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtBottomThick.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtBottomThick.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtBottomThick.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtBottomThick.ButtonStyleInherited = false;
            TxtBottomThick.ButtonSymbolOffset = new Point(0, 0);
            TxtBottomThick.DecimalPlaces = 3;
            TxtBottomThick.Enabled = false;
            TxtBottomThick.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtBottomThick.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtBottomThick.Location = new Point(85, 18);
            TxtBottomThick.Margin = new Padding(4, 5, 4, 5);
            TxtBottomThick.MinimumSize = new Size(1, 16);
            TxtBottomThick.Name = "TxtBottomThick";
            TxtBottomThick.Padding = new Padding(5);
            TxtBottomThick.RectColor = Color.FromArgb(140, 140, 140);
            TxtBottomThick.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtBottomThick.ScrollBarStyleInherited = false;
            TxtBottomThick.ShowText = false;
            TxtBottomThick.Size = new Size(85, 26);
            TxtBottomThick.Style = UIStyle.Custom;
            TxtBottomThick.TabIndex = 2;
            TxtBottomThick.Text = "0.000";
            TxtBottomThick.TextAlignment = ContentAlignment.MiddleCenter;
            TxtBottomThick.Type = UITextBox.UIEditType.Double;
            TxtBottomThick.Watermark = "";
            // 
            // lalBottomThick
            // 
            lalBottomThick.AutoSize = true;
            lalBottomThick.Location = new Point(-3, 23);
            lalBottomThick.Name = "lalBottomThick";
            lalBottomThick.Size = new Size(130, 24);
            lalBottomThick.TabIndex = 0;
            lalBottomThick.Text = "下晶圆厚度";
            // 
            // panel7
            // 
            panel7.Controls.Add(BtnTopWaferUp);
            panel7.Dock = DockStyle.Fill;
            panel7.Location = new Point(409, 275);
            panel7.Name = "panel7";
            panel7.Size = new Size(197, 62);
            panel7.TabIndex = 7;
            // 
            // BtnTopWaferUp
            // 
            BtnTopWaferUp.BackColor = Color.FromArgb(224, 224, 224);
            BtnTopWaferUp.Location = new Point(5, 12);
            BtnTopWaferUp.Name = "BtnTopWaferUp";
            BtnTopWaferUp.Size = new Size(186, 39);
            BtnTopWaferUp.TabIndex = 87;
            BtnTopWaferUp.Text = "3:上晶圆至拍照位";
            BtnTopWaferUp.UseVisualStyleBackColor = true;
            BtnTopWaferUp.Click += BtnTopWaferFixed_Click;
            // 
            // panel82
            // 
            panel82.Controls.Add(BtnTopWaferPhoto);
            panel82.Dock = DockStyle.Fill;
            panel82.Location = new Point(3, 411);
            panel82.Name = "panel82";
            panel82.Size = new Size(197, 62);
            panel82.TabIndex = 106;
            // 
            // BtnTopWaferPhoto
            // 
            BtnTopWaferPhoto.BackColor = Color.FromArgb(224, 224, 224);
            BtnTopWaferPhoto.Location = new Point(5, 12);
            BtnTopWaferPhoto.Name = "BtnTopWaferPhoto";
            BtnTopWaferPhoto.Size = new Size(186, 39);
            BtnTopWaferPhoto.TabIndex = 86;
            BtnTopWaferPhoto.Text = "4:上晶圆Mark识别";
            BtnTopWaferPhoto.UseVisualStyleBackColor = true;
            BtnTopWaferPhoto.Click += BtnTopWaferPhoto_Click;
            // 
            // panel10
            // 
            panel10.Controls.Add(BtnBottomTrayWaferOuter);
            panel10.Dock = DockStyle.Fill;
            panel10.Location = new Point(409, 411);
            panel10.Name = "panel10";
            panel10.Size = new Size(197, 62);
            panel10.TabIndex = 10;
            // 
            // BtnBottomTrayWaferOuter
            // 
            BtnBottomTrayWaferOuter.BackColor = Color.FromArgb(224, 224, 224);
            BtnBottomTrayWaferOuter.Location = new Point(5, 12);
            BtnBottomTrayWaferOuter.Name = "BtnBottomTrayWaferOuter";
            BtnBottomTrayWaferOuter.Size = new Size(186, 39);
            BtnBottomTrayWaferOuter.TabIndex = 84;
            BtnBottomTrayWaferOuter.Text = "6:托盘下晶圆吸附";
            BtnBottomTrayWaferOuter.UseVisualStyleBackColor = true;
            BtnBottomTrayWaferOuter.Click += BtnTrayWaferOuter_Click;
            // 
            // panel12
            // 
            panel12.Controls.Add(BtnTopWaferTakeUp);
            panel12.Dock = DockStyle.Fill;
            panel12.Location = new Point(206, 411);
            panel12.Name = "panel12";
            panel12.Size = new Size(197, 62);
            panel12.TabIndex = 12;
            // 
            // BtnTopWaferTakeUp
            // 
            BtnTopWaferTakeUp.BackColor = Color.FromArgb(224, 224, 224);
            BtnTopWaferTakeUp.Location = new Point(5, 12);
            BtnTopWaferTakeUp.Name = "BtnTopWaferTakeUp";
            BtnTopWaferTakeUp.Size = new Size(186, 39);
            BtnTopWaferTakeUp.TabIndex = 87;
            BtnTopWaferTakeUp.Text = "5:上晶圆固定至卡盘";
            BtnTopWaferTakeUp.UseVisualStyleBackColor = true;
            BtnTopWaferTakeUp.Click += BtnTopWaferTakeUp_Click;
            // 
            // panel14
            // 
            panel14.Controls.Add(BtnBottomWaferDown);
            panel14.Dock = DockStyle.Fill;
            panel14.Location = new Point(3, 547);
            panel14.Name = "panel14";
            panel14.Size = new Size(197, 62);
            panel14.TabIndex = 13;
            // 
            // BtnBottomWaferDown
            // 
            BtnBottomWaferDown.BackColor = Color.FromArgb(224, 224, 224);
            BtnBottomWaferDown.Location = new Point(5, 12);
            BtnBottomWaferDown.Name = "BtnBottomWaferDown";
            BtnBottomWaferDown.Size = new Size(186, 39);
            BtnBottomWaferDown.TabIndex = 88;
            BtnBottomWaferDown.Text = "7:下晶圆至拍照位";
            BtnBottomWaferDown.UseVisualStyleBackColor = true;
            BtnBottomWaferDown.Click += BtnBottomWaferDown_Click;
            // 
            // panel19
            // 
            panel19.Controls.Add(BtnAignMore);
            panel19.Dock = DockStyle.Fill;
            panel19.Location = new Point(206, 547);
            panel19.Name = "panel19";
            panel19.Size = new Size(197, 62);
            panel19.TabIndex = 88;
            // 
            // BtnAignMore
            // 
            BtnAignMore.BackColor = Color.FromArgb(224, 224, 224);
            BtnAignMore.Location = new Point(5, 12);
            BtnAignMore.Name = "BtnAignMore";
            BtnAignMore.Size = new Size(186, 39);
            BtnAignMore.TabIndex = 89;
            BtnAignMore.Text = "8:键合对准";
            BtnAignMore.UseVisualStyleBackColor = true;
            BtnAignMore.Click += BtnAignMore_Click;
            // 
            // panel85
            // 
            panel85.Controls.Add(BtnBottomWaferTakeDown);
            panel85.Dock = DockStyle.Fill;
            panel85.Location = new Point(409, 547);
            panel85.Name = "panel85";
            panel85.Size = new Size(197, 62);
            panel85.TabIndex = 109;
            // 
            // BtnBottomWaferTakeDown
            // 
            BtnBottomWaferTakeDown.BackColor = Color.FromArgb(224, 224, 224);
            BtnBottomWaferTakeDown.Location = new Point(5, 12);
            BtnBottomWaferTakeDown.Name = "BtnBottomWaferTakeDown";
            BtnBottomWaferTakeDown.Size = new Size(186, 39);
            BtnBottomWaferTakeDown.TabIndex = 112;
            BtnBottomWaferTakeDown.Text = "9:下晶圆至贴合位";
            BtnBottomWaferTakeDown.UseVisualStyleBackColor = true;
            BtnBottomWaferTakeDown.Click += BtnBottomWaferTakeDown_Click;
            // 
            // panel13
            // 
            panel13.Controls.Add(BtnXYRZReset);
            panel13.Dock = DockStyle.Fill;
            panel13.Location = new Point(3, 683);
            panel13.Name = "panel13";
            panel13.Size = new Size(197, 64);
            panel13.TabIndex = 11;
            // 
            // BtnXYRZReset
            // 
            BtnXYRZReset.BackColor = Color.FromArgb(224, 224, 224);
            BtnXYRZReset.Location = new Point(5, 14);
            BtnXYRZReset.Name = "BtnXYRZReset";
            BtnXYRZReset.Size = new Size(186, 36);
            BtnXYRZReset.TabIndex = 106;
            BtnXYRZReset.Text = "平台复位";
            BtnXYRZReset.UseVisualStyleBackColor = true;
            BtnXYRZReset.Click += BtnReset_Click;
            // 
            // panel88
            // 
            panel88.Controls.Add(BtnChuckLock);
            panel88.Dock = DockStyle.Fill;
            panel88.Location = new Point(206, 683);
            panel88.Name = "panel88";
            panel88.Size = new Size(197, 64);
            panel88.TabIndex = 111;
            // 
            // BtnChuckLock
            // 
            BtnChuckLock.BackColor = Color.FromArgb(224, 224, 224);
            BtnChuckLock.Location = new Point(5, 14);
            BtnChuckLock.Name = "BtnChuckLock";
            BtnChuckLock.Size = new Size(186, 36);
            BtnChuckLock.TabIndex = 85;
            BtnChuckLock.Text = "卡盘锁紧";
            BtnChuckLock.UseVisualStyleBackColor = true;
            BtnChuckLock.Click += BtnChuckLock_Click;
            // 
            // panel21
            // 
            panel21.Controls.Add(BtnChuckUnLock);
            panel21.Dock = DockStyle.Fill;
            panel21.Location = new Point(409, 683);
            panel21.Name = "panel21";
            panel21.Size = new Size(197, 64);
            panel21.TabIndex = 90;
            // 
            // BtnChuckUnLock
            // 
            BtnChuckUnLock.BackColor = Color.FromArgb(224, 224, 224);
            BtnChuckUnLock.Location = new Point(5, 14);
            BtnChuckUnLock.Name = "BtnChuckUnLock";
            BtnChuckUnLock.Size = new Size(186, 36);
            BtnChuckUnLock.TabIndex = 86;
            BtnChuckUnLock.Text = "卡盘锁开";
            BtnChuckUnLock.UseVisualStyleBackColor = true;
            BtnChuckUnLock.Click += BtnChuckUnLock_Click;
            // 
            // tmrAlign
            // 
            tmrAlign.Interval = 1000;
            // Tick事件已移至TimerWrapper实现
            // 
            // uiTableLayoutPanel1
            // 
            uiTableLayoutPanel1.AutoSize = true;
            uiTableLayoutPanel1.ColumnCount = 2;
            uiTableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            uiTableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            uiTableLayoutPanel1.Controls.Add(gbx键合对准, 0, 0);
            uiTableLayoutPanel1.Controls.Add(gbx相机微调, 1, 0);
            uiTableLayoutPanel1.Dock = DockStyle.Top;
            uiTableLayoutPanel1.Location = new Point(0, 35);
            uiTableLayoutPanel1.Name = "uiTableLayoutPanel1";
            uiTableLayoutPanel1.RowCount = 1;
            uiTableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            uiTableLayoutPanel1.Size = new Size(1900, 813);
            uiTableLayoutPanel1.TabIndex = 83;
            uiTableLayoutPanel1.TagString = null;
            // 
            // uiTableLayoutPanel2
            // 
            uiTableLayoutPanel2.ColumnCount = 1;
            uiTableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            uiTableLayoutPanel2.Controls.Add(uiTitlePanel1, 0, 0);
            uiTableLayoutPanel2.Dock = DockStyle.Bottom;
            uiTableLayoutPanel2.Location = new Point(0, 854);
            uiTableLayoutPanel2.Name = "uiTableLayoutPanel2";
            uiTableLayoutPanel2.RowCount = 1;
            uiTableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            uiTableLayoutPanel2.Size = new Size(1900, 104);
            uiTableLayoutPanel2.TabIndex = 84;
            uiTableLayoutPanel2.TagString = null;
            // 
            // uiTitlePanel1
            // 
            uiTitlePanel1.Controls.Add(RichTextBoxLog);
            uiTitlePanel1.Dock = DockStyle.Fill;
            uiTitlePanel1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTitlePanel1.Location = new Point(4, 5);
            uiTitlePanel1.Margin = new Padding(4, 5, 4, 5);
            uiTitlePanel1.MinimumSize = new Size(1, 1);
            uiTitlePanel1.Name = "uiTitlePanel1";
            uiTitlePanel1.ShowText = false;
            uiTitlePanel1.Size = new Size(1892, 94);
            uiTitlePanel1.TabIndex = 0;
            uiTitlePanel1.Text = "状态信息";
            uiTitlePanel1.TextAlignment = ContentAlignment.MiddleCenter;
            uiTitlePanel1.TitleColor = Color.Gray;
            // 
            // RichTextBoxLog
            // 
            RichTextBoxLog.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            RichTextBoxLog.FillColor = Color.White;
            RichTextBoxLog.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point);
            RichTextBoxLog.Location = new Point(4, 36);
            RichTextBoxLog.Margin = new Padding(4, 5, 4, 5);
            RichTextBoxLog.MinimumSize = new Size(1, 1);
            RichTextBoxLog.Name = "RichTextBoxLog";
            RichTextBoxLog.Padding = new Padding(2);
            RichTextBoxLog.ShowText = false;
            RichTextBoxLog.Size = new Size(1888, 62);
            RichTextBoxLog.TabIndex = 0;
            RichTextBoxLog.TextAlignment = ContentAlignment.MiddleCenter;
            // 
            // FTitlePage1
            // 
            AllowShowTitle = true;
            AutoScaleMode = AutoScaleMode.None;
            BackColor = Color.FromArgb(248, 248, 248);
            ClientSize = new Size(1900, 958);
            ControlBoxFillHoverColor = Color.FromArgb(163, 163, 163);
            Controls.Add(uiTableLayoutPanel2);
            Controls.Add(uiTableLayoutPanel1);
            Name = "FTitlePage1";
            Padding = new Padding(0, 35, 0, 0);
            PageIndex = 1001;
            RectColor = Color.FromArgb(140, 140, 140);
            ShowTitle = true;
            Style = UIStyle.Custom;
            Text = "键合对准";
            // FormClosing 和 Load 事件由 BasePage 自动管理
            gbx相机微调.ResumeLayout(false);
            tableLayoutPanel3.ResumeLayout(false);
            panel37.ResumeLayout(false);
            panel37.PerformLayout();
            panel44.ResumeLayout(false);
            panel44.PerformLayout();
            panel46.ResumeLayout(false);
            panel46.PerformLayout();
            panel55.ResumeLayout(false);
            panel55.PerformLayout();
            panel56.ResumeLayout(false);
            panel56.PerformLayout();
            panel59.ResumeLayout(false);
            panel59.PerformLayout();
            panel60.ResumeLayout(false);
            panel60.PerformLayout();
            panel61.ResumeLayout(false);
            panel62.ResumeLayout(false);
            panel63.ResumeLayout(false);
            panel64.ResumeLayout(false);
            panel65.ResumeLayout(false);
            panel65.PerformLayout();
            panel66.ResumeLayout(false);
            panel67.ResumeLayout(false);
            panel71.ResumeLayout(false);
            panel71.PerformLayout();
            panel25.ResumeLayout(false);
            panel25.PerformLayout();
            panel26.ResumeLayout(false);
            panel26.PerformLayout();
            panel27.ResumeLayout(false);
            panel28.ResumeLayout(false);
            panel29.ResumeLayout(false);
            panel30.ResumeLayout(false);
            panel57.ResumeLayout(false);
            panel57.PerformLayout();
            panel54.ResumeLayout(false);
            panel54.PerformLayout();
            panel3.ResumeLayout(false);
            panel3.PerformLayout();
            panel33.ResumeLayout(false);
            panel33.PerformLayout();
            panel48.ResumeLayout(false);
            panel48.PerformLayout();
            panel5.ResumeLayout(false);
            panel5.PerformLayout();
            panel45.ResumeLayout(false);
            panel49.ResumeLayout(false);
            panel17.ResumeLayout(false);
            panel47.ResumeLayout(false);
            panel68.ResumeLayout(false);
            panel41.ResumeLayout(false);
            panel50.ResumeLayout(false);
            panel69.ResumeLayout(false);
            panel42.ResumeLayout(false);
            panel31.ResumeLayout(false);
            panel72.ResumeLayout(false);
            panel23.ResumeLayout(false);
            panel24.ResumeLayout(false);
            panel73.ResumeLayout(false);
            panel74.ResumeLayout(false);
            panel58.ResumeLayout(false);
            panel58.PerformLayout();
            panel51.ResumeLayout(false);
            panel51.PerformLayout();
            panel52.ResumeLayout(false);
            panel53.ResumeLayout(false);
            panel70.ResumeLayout(false);
            panel75.ResumeLayout(false);
            panel76.ResumeLayout(false);
            gbx键合对准.ResumeLayout(false);
            tableLayoutPanel1.ResumeLayout(false);
            panel84.ResumeLayout(false);
            panel84.PerformLayout();
            panel79.ResumeLayout(false);
            panel79.PerformLayout();
            panel2.ResumeLayout(false);
            panel4.ResumeLayout(false);
            panel4.PerformLayout();
            panel9.ResumeLayout(false);
            panel8.ResumeLayout(false);
            panel8.PerformLayout();
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            panel32.ResumeLayout(false);
            panel32.PerformLayout();
            panel34.ResumeLayout(false);
            panel35.ResumeLayout(false);
            panel35.PerformLayout();
            panel22.ResumeLayout(false);
            panel39.ResumeLayout(false);
            panel39.PerformLayout();
            panel80.ResumeLayout(false);
            panel80.PerformLayout();
            panel38.ResumeLayout(false);
            panel38.PerformLayout();
            panel7.ResumeLayout(false);
            panel82.ResumeLayout(false);
            panel10.ResumeLayout(false);
            panel12.ResumeLayout(false);
            panel14.ResumeLayout(false);
            panel19.ResumeLayout(false);
            panel85.ResumeLayout(false);
            panel13.ResumeLayout(false);
            panel88.ResumeLayout(false);
            panel21.ResumeLayout(false);
            uiTableLayoutPanel1.ResumeLayout(false);
            uiTableLayoutPanel2.ResumeLayout(false);
            uiTitlePanel1.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion



        private GroupBox gbx相机微调;


        private Label Lal当前位置;
        private Label Lal目标位置;
        private TextBox TxtCurLZ;
        private Button BtnStopLZ;
        private Button BtnPosLZ;
        private GroupBox groupBox1;
        private TextBox TxtCurRZ;
        private Button BtnStopRZ;
        private Button BtnPosRZ;
        private GroupBox gbx键合对准;
        private TableLayoutPanel tableLayoutPanel1;
        private Button BtnChuckLock;
        private Panel panel4;
        private Panel panel7;
        private Button BtnBottomTrayWaferOuter;
        private Panel panel9;
        private Panel panel12;
        private Panel panel10;
        private Button BtnTopWaferPhotoPos;
        private Panel panel14;
        private Button BtnTopTrayWaferOuter;
        private Panel panel8;
        private Panel panel13;
        private Panel panel19;
        private Panel panel21;
        private Panel panel22;
        private Panel panel1;
        private Panel panel32;

        private Panel panel34;
        private Button btnOpen;
        private Panel panel35;
        private Panel panel39;
        private Label lalName;
        private Panel panel38;
        private Label lalBottomThick;
        private Button BtnTopWaferPhoto;
        private System.Windows.Forms.Timer tmrAlign;
        private TableLayoutPanel tableLayoutPanel3;
        private Panel panel53;
        private Panel panel52;
        private Panel panel51;
        private TextBox TxtCurZ;
        private Panel panel50;
        private Panel panel33;
        private TextBox TxtCurX;
        private Panel panel37;
        private Panel panel44;
        private Panel panel45;
        private Panel panel46;
        private Panel panel47;
        private Panel panel48;
        private TextBox TxtCurY;
        private Panel panel49;
        private Panel panel55;
        private Panel panel56;
        private Panel panel57;
        private Panel panel54;
        private Panel panel58;
        private Panel panel59;
        private Panel panel60;
        private Panel panel61;
        private Panel panel62;
        private Panel panel63;
        private Button BtnPosZ;
        private Button BtnPosY;
        private Label LalPos;
        private Button BtnPosX;
        private Panel panel64;
        private Panel panel65;
        private Label LalStop;
        private Panel panel66;
        private Panel panel67;
        private Panel panel68;
        private Button BtnStopX;
        private Panel panel69;
        private Button BtnStopV;
        private Panel panel70;
        private Button BtnStopZ;
        private Panel panel71;
        private Label LalAxisName;
        private Label label1;
        private Label label2;
        private Label label3;
        private Label label7;
        private Label label8;
        private Panel panel3;
        private Label label4;
        private Panel panel5;
        private TextBox TxtCurR;
        private Panel panel17;
        private Panel panel23;
        private Button BtnPosR;
        private Panel panel24;
        private Button BtnStopR;

        private Panel panel25;
        private Panel panel26;
        private Panel panel27;
        private Panel panel28;
        private Panel panel29;
        private Panel panel30;
        private Panel panel31;
        private Panel panel41;
        private Panel panel42;
        private Panel panel72;
        private Panel panel73;
        private Panel panel74;
        private Panel panel75;
        private Panel panel76;
        private Button BtnFrontLZ;
        private Button BtnBackLZ;
        private Label LalFront;
        private Label LalBack;
        private Button BtnFrontRZ;
        private Button BtnBackRZ;
        private Button BtnFrontY;
        private Button BtnBackX;
        private Button BtnFrontX;
        private Button BtnBackY;
        private Button BtnFrontR;
        private Button BtnBackR;
        private Button BtnFrontZ;
        private Button BtnBackZ;
        private Button BtnAllStop;
        private WaferAligner.ULine uLine4;
        private WaferAligner.ULine uLine2;
        private WaferAligner.ULine uLine3;
        private WaferAligner.ULine uLine5;

        private WaferAligner.ULine uLine7;
        private WaferAligner.ULine uLine1;
        private Button BtnAignMore;
        private TextBox txtFileName;
        private WaferAligner.ULine uLine6;
        private Label lalTextName;
        private Button button1;
        private Label label5;
        private Button button4;
        private Label label6;
        private Button button6;
        private Button BtnTopWaferUp;
        private Panel panel80;
        private Label label10;
        private Panel panel79;
        private Label LalTopThick;
        private UITextBox TxtTopThick;
        private UITextBox TxtSpacerThick;
        private UITextBox TxtName;
        private UITextBox TxtBottomThick;
        private Label label9;
        private Button button9;
        private Button BtnBottomWaferDown;
        private Panel panel82;
        private Panel panel84;
        private Label label12;
        private Button button10;
        private Panel panel85;
        private Panel panel87;
        private Button BtnXYRZReset;
        private Panel panel88;
        private Button BtnChuckUnLock;
        private UITextBox TxtTargetLZ;
        private UITextBox TxtTargetRZ;
        private UITextBox TxtTargetX;
        private UITextBox TxtTargetY;
        private UITextBox TxtTargetR;
        private UITextBox TxtTargetZ;
        private Button BtnStopY;
        private Button BtnTopWaferTakeUp;
        private Button BtnBottomWaferTakeDown;
        private Label label14;
        private Label label15;
        private Label label16;
        private Panel panel2;
        private UITableLayoutPanel uiTableLayoutPanel1;
        private UITableLayoutPanel uiTableLayoutPanel2;
        private UITitlePanel uiTitlePanel1;
        private UIRichTextBox RichTextBoxLog;
    }
}