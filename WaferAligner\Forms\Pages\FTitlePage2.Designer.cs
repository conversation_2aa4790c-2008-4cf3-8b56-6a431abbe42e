﻿namespace Sunny.UI.Demo
{
    partial class FTitlePage2
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tableLayoutPanel1 = new TableLayoutPanel();
            panel9 = new Panel();
            TxtName = new UITextBox();
            panel2 = new Panel();
            LalName = new Label();
            panel1 = new Panel();
            label3 = new Label();
            button4 = new Button();
            panel47 = new Panel();
            label42 = new Label();
            TxtCalZ = new UITextBox();
            LalCalZ = new Label();
            panel29 = new Panel();
            label40 = new Label();
            TxtCalRZ = new UITextBox();
            LalCalRZ = new Label();
            panel28 = new Panel();
            label38 = new Label();
            TxtCalRY = new UITextBox();
            LalCalRY = new Label();
            panel26 = new Panel();
            label36 = new Label();
            TxtCalRX = new UITextBox();
            LalCalRX = new Label();
            panel21 = new Panel();
            label33 = new Label();
            TxtCalLZ = new UITextBox();
            LalCalLZ = new Label();
            panel20 = new Panel();
            label29 = new Label();
            TxtCalLY = new UITextBox();
            LalCalLY = new Label();
            panel18 = new Panel();
            TxtCalLX = new UITextBox();
            label17 = new Label();
            LalCalLX = new Label();
            panel13 = new Panel();
            label16 = new Label();
            button2 = new Button();
            panel67 = new Panel();
            label20 = new Label();
            TxtTopWaferPhotoLZ = new UITextBox();
            LalTopWaferPhotoLZ = new Label();
            panel15 = new Panel();
            label14 = new Label();
            button10 = new Button();
            label15 = new Label();
            button12 = new Button();
            panel4 = new Panel();
            label12 = new Label();
            button6 = new Button();
            panel8 = new Panel();
            label13 = new Label();
            button8 = new Button();
            panel16 = new Panel();
            ChkTopHorizontalAdjust = new UICheckBox();
            panel19 = new Panel();
            ChkTopHorizontalPhoto = new UICheckBox();
            panel33 = new Panel();
            label1 = new Label();
            TxtCurX = new UITextBox();
            LalCurX = new Label();
            panel48 = new Panel();
            lalTextName = new Label();
            button1 = new Button();
            panel34 = new Panel();
            label2 = new Label();
            TxtCurY = new UITextBox();
            LalCurY = new Label();
            panel49 = new Panel();
            label4 = new Label();
            TxtCurR = new UITextBox();
            LalCurR = new Label();
            panel41 = new Panel();
            label5 = new Label();
            TxtCurZ = new UITextBox();
            LalCurZ = new Label();
            panel42 = new Panel();
            label6 = new Label();
            TxtCurLX = new UITextBox();
            LalCurLX = new Label();
            panel43 = new Panel();
            label7 = new Label();
            TxtCurLY = new UITextBox();
            LalCurLY = new Label();
            panel44 = new Panel();
            label8 = new Label();
            TxtCurLZ = new UITextBox();
            LalCurLZ = new Label();
            panel45 = new Panel();
            label9 = new Label();
            TxtCurRX = new UITextBox();
            LalCurRX = new Label();
            panel46 = new Panel();
            label11 = new Label();
            TxtCurRZ = new UITextBox();
            LalCurRZ = new Label();
            panel64 = new Panel();
            label10 = new Label();
            TxtCurRY = new UITextBox();
            LalCurRY = new Label();
            panel10 = new Panel();
            TxtTopWaferPhotoLX = new UITextBox();
            label23 = new Label();
            LalTopWaferPhotoLX = new Label();
            panel14 = new Panel();
            label19 = new Label();
            TxtTopWaferPhotoLY = new UITextBox();
            LalTopWaferPhotoLY = new Label();
            panel51 = new Panel();
            label22 = new Label();
            TxtTopWaferPhotoRY = new UITextBox();
            LalTopWaferPhotoRY = new Label();
            panel52 = new Panel();
            label18 = new Label();
            TxtTopWaferPhotoRZ = new UITextBox();
            LalTopWaferPhotoRZ = new Label();
            panel25 = new Panel();
            label25 = new Label();
            TxtBottomWaferPhotoZ = new UITextBox();
            LalBottomWaferPhotoZ = new Label();
            panel70 = new Panel();
            label21 = new Label();
            TxtTopWaferPhotoRX = new UITextBox();
            LalTopWaferPhotoRX = new Label();
            panel6 = new Panel();
            TxtBottomWaferPhotoLX = new UITextBox();
            label24 = new Label();
            LalBottomWaferPhotoLX = new Label();
            panel7 = new Panel();
            label26 = new Label();
            TxtBottomWaferPhotoLY = new UITextBox();
            LalBottomWaferPhotoLY = new Label();
            panel71 = new Panel();
            label28 = new Label();
            TxtBottomWaferPhotoLZ = new UITextBox();
            LalBottomWaferPhotoLZ = new Label();
            panel12 = new Panel();
            label30 = new Label();
            TxtBottomWaferPhotoRX = new UITextBox();
            LalBottomWaferPhotoRX = new Label();
            panel17 = new Panel();
            label32 = new Label();
            TxtBottomWaferPhotoRY = new UITextBox();
            LalBottomWaferPhotoRY = new Label();
            panel23 = new Panel();
            label34 = new Label();
            TxtBottomWaferPhotoRZ = new UITextBox();
            LalBottomWaferPhotoRZ = new Label();
            panel39 = new Panel();
            panel24 = new Panel();
            label37 = new Label();
            TxtTopWaferPhotoZ = new UITextBox();
            label39 = new Label();
            panel56 = new Panel();
            label41 = new Label();
            TxtTopWaferPhotoX = new UITextBox();
            label43 = new Label();
            panel57 = new Panel();
            label44 = new Label();
            TxtBottomWaferPhotoX = new UITextBox();
            label45 = new Label();
            panel58 = new Panel();
            label46 = new Label();
            TxtTopWaferPhotoY = new UITextBox();
            label47 = new Label();
            panel59 = new Panel();
            label48 = new Label();
            TxtBottomWaferPhotoY = new UITextBox();
            label49 = new Label();
            panel60 = new Panel();
            label50 = new Label();
            TxtTopWaferPhotoR = new UITextBox();
            label51 = new Label();
            panel61 = new Panel();
            label52 = new Label();
            TxtBottomWaferPhotoR = new UITextBox();
            label53 = new Label();
            panel55 = new Panel();
            panel62 = new Panel();
            label58 = new Label();
            TxtTakeDownWaferPos = new UITextBox();
            panel36 = new Panel();
            lalTakeUpWaferPos = new Label();
            panel37 = new Panel();
            label57 = new Label();
            TxtTakeUpWaferPos = new UITextBox();
            panel63 = new Panel();
            lalZLevel = new Label();
            panel65 = new Panel();
            label54 = new Label();
            TxtZLevel = new UITextBox();
            panel35 = new Panel();
            label55 = new Label();
            TxtUpThick = new UITextBox();
            panel68 = new Panel();
            LalUpThick = new Label();
            panel53 = new Panel();
            lalTakeDownWaferPos = new Label();
            panel38 = new Panel();
            label56 = new Label();
            TxtBottomThick = new UITextBox();
            panel40 = new Panel();
            label27 = new Label();
            panel27 = new Panel();
            LalSpacer = new Label();
            panel66 = new Panel();
            label31 = new Label();
            TxtSpacerThick = new UITextBox();
            panel30 = new Panel();
            lalMaterial = new Label();
            panel50 = new Panel();
            cmbMaterial = new ComboBox();
            panel69 = new Panel();
            label59 = new Label();
            panel72 = new Panel();
            DUDVisual = new UIDoubleUpDown();
            panel31 = new Panel();
            LalMarkDistance = new Label();
            panel32 = new Panel();
            label35 = new Label();
            TxtMarkDistance = new UITextBox();
            panel3 = new Panel();
            cmbSize = new ComboBox();
            panel11 = new Panel();
            LalSize = new Label();
            panel54 = new Panel();
            BtnOpenCalFile = new Button();
            panel5 = new Panel();
            BtnSave = new Button();
            panel22 = new Panel();
            BtnOpen = new Button();
            AlignPara = new UIPanel();
            tableLayoutPanel1.SuspendLayout();
            panel9.SuspendLayout();
            panel2.SuspendLayout();
            panel1.SuspendLayout();
            panel47.SuspendLayout();
            panel29.SuspendLayout();
            panel28.SuspendLayout();
            panel26.SuspendLayout();
            panel21.SuspendLayout();
            panel20.SuspendLayout();
            panel18.SuspendLayout();
            panel13.SuspendLayout();
            panel67.SuspendLayout();
            panel15.SuspendLayout();
            panel4.SuspendLayout();
            panel8.SuspendLayout();
            panel16.SuspendLayout();
            panel19.SuspendLayout();
            panel33.SuspendLayout();
            panel48.SuspendLayout();
            panel34.SuspendLayout();
            panel49.SuspendLayout();
            panel41.SuspendLayout();
            panel42.SuspendLayout();
            panel43.SuspendLayout();
            panel44.SuspendLayout();
            panel45.SuspendLayout();
            panel46.SuspendLayout();
            panel64.SuspendLayout();
            panel10.SuspendLayout();
            panel14.SuspendLayout();
            panel51.SuspendLayout();
            panel52.SuspendLayout();
            panel25.SuspendLayout();
            panel70.SuspendLayout();
            panel6.SuspendLayout();
            panel7.SuspendLayout();
            panel71.SuspendLayout();
            panel12.SuspendLayout();
            panel17.SuspendLayout();
            panel23.SuspendLayout();
            panel24.SuspendLayout();
            panel56.SuspendLayout();
            panel57.SuspendLayout();
            panel58.SuspendLayout();
            panel59.SuspendLayout();
            panel60.SuspendLayout();
            panel61.SuspendLayout();
            panel55.SuspendLayout();
            panel62.SuspendLayout();
            panel36.SuspendLayout();
            panel37.SuspendLayout();
            panel63.SuspendLayout();
            panel65.SuspendLayout();
            panel35.SuspendLayout();
            panel68.SuspendLayout();
            panel53.SuspendLayout();
            panel38.SuspendLayout();
            panel40.SuspendLayout();
            panel27.SuspendLayout();
            panel66.SuspendLayout();
            panel30.SuspendLayout();
            panel50.SuspendLayout();
            panel69.SuspendLayout();
            panel72.SuspendLayout();
            panel31.SuspendLayout();
            panel32.SuspendLayout();
            panel3.SuspendLayout();
            panel11.SuspendLayout();
            panel54.SuspendLayout();
            panel5.SuspendLayout();
            panel22.SuspendLayout();
            AlignPara.SuspendLayout();
            SuspendLayout();
            // 
            // tableLayoutPanel1
            // 
            tableLayoutPanel1.ColumnCount = 10;
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 10.0001F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 10.0001F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 10.0001F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 10.0001F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 10.0001F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 10.0001F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 10.0001F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 10.0001F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 10.0001F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 9.9991F));
            tableLayoutPanel1.Controls.Add(panel9, 1, 3);
            tableLayoutPanel1.Controls.Add(panel2, 0, 3);
            tableLayoutPanel1.Controls.Add(panel1, 0, 2);
            tableLayoutPanel1.Controls.Add(panel47, 6, 5);
            tableLayoutPanel1.Controls.Add(panel29, 5, 5);
            tableLayoutPanel1.Controls.Add(panel28, 4, 5);
            tableLayoutPanel1.Controls.Add(panel26, 3, 5);
            tableLayoutPanel1.Controls.Add(panel21, 2, 5);
            tableLayoutPanel1.Controls.Add(panel20, 1, 5);
            tableLayoutPanel1.Controls.Add(panel18, 0, 5);
            tableLayoutPanel1.Controls.Add(panel13, 0, 4);
            tableLayoutPanel1.Controls.Add(panel67, 2, 7);
            tableLayoutPanel1.Controls.Add(panel15, 0, 10);
            tableLayoutPanel1.Controls.Add(panel4, 0, 6);
            tableLayoutPanel1.Controls.Add(panel8, 0, 8);
            tableLayoutPanel1.Controls.Add(panel16, 0, 11);
            tableLayoutPanel1.Controls.Add(panel19, 1, 11);
            tableLayoutPanel1.Controls.Add(panel33, 0, 1);
            tableLayoutPanel1.Controls.Add(panel48, 0, 0);
            tableLayoutPanel1.Controls.Add(panel34, 1, 1);
            tableLayoutPanel1.Controls.Add(panel49, 2, 1);
            tableLayoutPanel1.Controls.Add(panel41, 3, 1);
            tableLayoutPanel1.Controls.Add(panel42, 4, 1);
            tableLayoutPanel1.Controls.Add(panel43, 5, 1);
            tableLayoutPanel1.Controls.Add(panel44, 6, 1);
            tableLayoutPanel1.Controls.Add(panel45, 7, 1);
            tableLayoutPanel1.Controls.Add(panel46, 9, 1);
            tableLayoutPanel1.Controls.Add(panel64, 8, 1);
            tableLayoutPanel1.Controls.Add(panel10, 0, 7);
            tableLayoutPanel1.Controls.Add(panel14, 1, 7);
            tableLayoutPanel1.Controls.Add(panel51, 4, 7);
            tableLayoutPanel1.Controls.Add(panel52, 5, 7);
            tableLayoutPanel1.Controls.Add(panel25, 6, 9);
            tableLayoutPanel1.Controls.Add(panel70, 3, 7);
            tableLayoutPanel1.Controls.Add(panel6, 0, 9);
            tableLayoutPanel1.Controls.Add(panel7, 1, 9);
            tableLayoutPanel1.Controls.Add(panel71, 2, 9);
            tableLayoutPanel1.Controls.Add(panel12, 3, 9);
            tableLayoutPanel1.Controls.Add(panel17, 4, 9);
            tableLayoutPanel1.Controls.Add(panel23, 5, 9);
            tableLayoutPanel1.Controls.Add(panel39, 8, 5);
            tableLayoutPanel1.Controls.Add(panel24, 6, 7);
            tableLayoutPanel1.Controls.Add(panel56, 7, 7);
            tableLayoutPanel1.Controls.Add(panel57, 7, 9);
            tableLayoutPanel1.Controls.Add(panel58, 8, 7);
            tableLayoutPanel1.Controls.Add(panel59, 8, 9);
            tableLayoutPanel1.Controls.Add(panel60, 9, 7);
            tableLayoutPanel1.Controls.Add(panel61, 9, 9);
            tableLayoutPanel1.Controls.Add(panel55, 8, 11);
            tableLayoutPanel1.Controls.Add(panel36, 2, 12);
            tableLayoutPanel1.Controls.Add(panel37, 3, 12);
            tableLayoutPanel1.Controls.Add(panel63, 0, 12);
            tableLayoutPanel1.Controls.Add(panel65, 1, 12);
            tableLayoutPanel1.Controls.Add(panel35, 3, 11);
            tableLayoutPanel1.Controls.Add(panel68, 2, 11);
            tableLayoutPanel1.Controls.Add(panel53, 7, 11);
            tableLayoutPanel1.Controls.Add(panel38, 6, 11);
            tableLayoutPanel1.Controls.Add(panel40, 5, 11);
            tableLayoutPanel1.Controls.Add(panel27, 2, 3);
            tableLayoutPanel1.Controls.Add(panel66, 3, 3);
            tableLayoutPanel1.Controls.Add(panel30, 5, 12);
            tableLayoutPanel1.Controls.Add(panel50, 6, 12);
            tableLayoutPanel1.Controls.Add(panel69, 8, 3);
            tableLayoutPanel1.Controls.Add(panel72, 9, 3);
            tableLayoutPanel1.Controls.Add(panel31, 6, 3);
            tableLayoutPanel1.Controls.Add(panel32, 7, 3);
            tableLayoutPanel1.Controls.Add(panel3, 5, 3);
            tableLayoutPanel1.Controls.Add(panel11, 4, 3);
            tableLayoutPanel1.Controls.Add(panel54, 7, 13);
            tableLayoutPanel1.Controls.Add(panel5, 8, 13);
            tableLayoutPanel1.Controls.Add(panel22, 9, 13);
            tableLayoutPanel1.Location = new Point(41, 7);
            tableLayoutPanel1.Name = "tableLayoutPanel1";
            tableLayoutPanel1.RowCount = 14;
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 7.141637F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 7.14159775F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 7.14124441F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 7.14124441F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 7.142783F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 7.142783F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 7.143696F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 7.14326859F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 7.143695F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 7.143696F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 7.143696F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 7.145835F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 7.14396F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 7.140867F));
            tableLayoutPanel1.Size = new Size(1744, 806);
            tableLayoutPanel1.TabIndex = 85;
            // 
            // panel9
            // 
            panel9.Controls.Add(TxtName);
            panel9.Dock = DockStyle.Fill;
            panel9.Location = new Point(177, 174);
            panel9.Name = "panel9";
            panel9.Size = new Size(168, 51);
            panel9.TabIndex = 188;
            // 
            // TxtName
            // 
            TxtName.ButtonSymbolOffset = new Point(0, 0);
            TxtName.DecimalPlaces = 3;
            TxtName.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtName.Location = new Point(37, 12);
            TxtName.Margin = new Padding(4, 5, 4, 5);
            TxtName.MinimumSize = new Size(1, 16);
            TxtName.Name = "TxtName";
            TxtName.Padding = new Padding(5);
            TxtName.ShowText = false;
            TxtName.Size = new Size(95, 26);
            TxtName.TabIndex = 0;
            TxtName.TextAlignment = ContentAlignment.MiddleLeft;
            TxtName.Watermark = "";
            // 
            // panel2
            // 
            panel2.Controls.Add(LalName);
            panel2.Dock = DockStyle.Fill;
            panel2.Location = new Point(3, 174);
            panel2.Name = "panel2";
            panel2.Size = new Size(168, 51);
            panel2.TabIndex = 187;
            // 
            // LalName
            // 
            LalName.AutoSize = true;
            LalName.Location = new Point(49, 17);
            LalName.Name = "LalName";
            LalName.Size = new Size(106, 24);
            LalName.TabIndex = 0;
            LalName.Text = "产品名称";
            // 
            // panel1
            // 
            tableLayoutPanel1.SetColumnSpan(panel1, 10);
            panel1.Controls.Add(label3);
            panel1.Controls.Add(button4);
            panel1.Dock = DockStyle.Fill;
            panel1.Location = new Point(3, 117);
            panel1.Name = "panel1";
            panel1.Size = new Size(1738, 51);
            panel1.TabIndex = 186;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label3.Location = new Point(53, 17);
            label3.Name = "label3";
            label3.Size = new Size(142, 24);
            label3.TabIndex = 8;
            label3.Text = "1：产品参数";
            // 
            // button4
            // 
            button4.BackColor = Color.Silver;
            button4.Location = new Point(2, 25);
            button4.Name = "button4";
            button4.Size = new Size(1789, 1);
            button4.TabIndex = 6;
            button4.Text = "button4";
            button4.UseVisualStyleBackColor = false;
            // 
            // panel47
            // 
            panel47.Controls.Add(label42);
            panel47.Controls.Add(TxtCalZ);
            panel47.Controls.Add(LalCalZ);
            panel47.Dock = DockStyle.Fill;
            panel47.Location = new Point(1047, 288);
            panel47.Name = "panel47";
            panel47.Size = new Size(168, 51);
            panel47.TabIndex = 182;
            // 
            // label42
            // 
            label42.AutoSize = true;
            label42.Location = new Point(130, 17);
            label42.Name = "label42";
            label42.Size = new Size(34, 24);
            label42.TabIndex = 10;
            label42.Text = "mm";
            // 
            // TxtCalZ
            // 
            TxtCalZ.ButtonSymbolOffset = new Point(0, 0);
            TxtCalZ.DecimalPlaces = 3;
            TxtCalZ.Enabled = false;
            TxtCalZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCalZ.Location = new Point(33, 12);
            TxtCalZ.Margin = new Padding(4, 5, 4, 5);
            TxtCalZ.MinimumSize = new Size(1, 16);
            TxtCalZ.Name = "TxtCalZ";
            TxtCalZ.Padding = new Padding(5);
            TxtCalZ.ShowText = false;
            TxtCalZ.Size = new Size(95, 26);
            TxtCalZ.TabIndex = 8;
            TxtCalZ.Text = "0.000";
            TxtCalZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCalZ.Type = UITextBox.UIEditType.Double;
            TxtCalZ.Watermark = "";
            // 
            // LalCalZ
            // 
            LalCalZ.AutoSize = true;
            LalCalZ.Location = new Point(15, 17);
            LalCalZ.Name = "LalCalZ";
            LalCalZ.Size = new Size(22, 24);
            LalCalZ.TabIndex = 4;
            LalCalZ.Text = "Z";
            // 
            // panel29
            // 
            panel29.Controls.Add(label40);
            panel29.Controls.Add(TxtCalRZ);
            panel29.Controls.Add(LalCalRZ);
            panel29.Dock = DockStyle.Fill;
            panel29.Location = new Point(873, 288);
            panel29.Name = "panel29";
            panel29.Size = new Size(168, 51);
            panel29.TabIndex = 181;
            // 
            // label40
            // 
            label40.AutoSize = true;
            label40.Location = new Point(134, 17);
            label40.Name = "label40";
            label40.Size = new Size(34, 24);
            label40.TabIndex = 9;
            label40.Text = "mm";
            // 
            // TxtCalRZ
            // 
            TxtCalRZ.ButtonSymbolOffset = new Point(0, 0);
            TxtCalRZ.DecimalPlaces = 3;
            TxtCalRZ.Enabled = false;
            TxtCalRZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCalRZ.Location = new Point(37, 12);
            TxtCalRZ.Margin = new Padding(4, 5, 4, 5);
            TxtCalRZ.MinimumSize = new Size(1, 16);
            TxtCalRZ.Name = "TxtCalRZ";
            TxtCalRZ.Padding = new Padding(5);
            TxtCalRZ.ShowText = false;
            TxtCalRZ.Size = new Size(95, 26);
            TxtCalRZ.TabIndex = 7;
            TxtCalRZ.Text = "0.000";
            TxtCalRZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCalRZ.Type = UITextBox.UIEditType.Double;
            TxtCalRZ.Watermark = "";
            // 
            // LalCalRZ
            // 
            LalCalRZ.AutoSize = true;
            LalCalRZ.Location = new Point(11, 17);
            LalCalRZ.Name = "LalCalRZ";
            LalCalRZ.Size = new Size(34, 24);
            LalCalRZ.TabIndex = 8;
            LalCalRZ.Text = "RZ";
            // 
            // panel28
            // 
            panel28.Controls.Add(label38);
            panel28.Controls.Add(TxtCalRY);
            panel28.Controls.Add(LalCalRY);
            panel28.Dock = DockStyle.Fill;
            panel28.Location = new Point(699, 288);
            panel28.Name = "panel28";
            panel28.Size = new Size(168, 51);
            panel28.TabIndex = 180;
            // 
            // label38
            // 
            label38.AutoSize = true;
            label38.Location = new Point(134, 17);
            label38.Name = "label38";
            label38.Size = new Size(34, 24);
            label38.TabIndex = 13;
            label38.Text = "mm";
            // 
            // TxtCalRY
            // 
            TxtCalRY.ButtonSymbolOffset = new Point(0, 0);
            TxtCalRY.DecimalPlaces = 3;
            TxtCalRY.Enabled = false;
            TxtCalRY.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCalRY.Location = new Point(37, 12);
            TxtCalRY.Margin = new Padding(4, 5, 4, 5);
            TxtCalRY.MinimumSize = new Size(1, 16);
            TxtCalRY.Name = "TxtCalRY";
            TxtCalRY.Padding = new Padding(5);
            TxtCalRY.ShowText = false;
            TxtCalRY.Size = new Size(95, 26);
            TxtCalRY.TabIndex = 6;
            TxtCalRY.Text = "0.000";
            TxtCalRY.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCalRY.Type = UITextBox.UIEditType.Double;
            TxtCalRY.Watermark = "";
            // 
            // LalCalRY
            // 
            LalCalRY.AutoSize = true;
            LalCalRY.Location = new Point(11, 17);
            LalCalRY.Name = "LalCalRY";
            LalCalRY.Size = new Size(34, 24);
            LalCalRY.TabIndex = 7;
            LalCalRY.Text = "RY";
            // 
            // panel26
            // 
            panel26.Controls.Add(label36);
            panel26.Controls.Add(TxtCalRX);
            panel26.Controls.Add(LalCalRX);
            panel26.Dock = DockStyle.Fill;
            panel26.Location = new Point(525, 288);
            panel26.Name = "panel26";
            panel26.Size = new Size(168, 51);
            panel26.TabIndex = 179;
            // 
            // label36
            // 
            label36.AutoSize = true;
            label36.Location = new Point(134, 17);
            label36.Name = "label36";
            label36.Size = new Size(34, 24);
            label36.TabIndex = 12;
            label36.Text = "mm";
            // 
            // TxtCalRX
            // 
            TxtCalRX.ButtonSymbolOffset = new Point(0, 0);
            TxtCalRX.DecimalPlaces = 3;
            TxtCalRX.DoubleValue = 49.95D;
            TxtCalRX.Enabled = false;
            TxtCalRX.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCalRX.Location = new Point(37, 12);
            TxtCalRX.Margin = new Padding(4, 5, 4, 5);
            TxtCalRX.MinimumSize = new Size(1, 16);
            TxtCalRX.Name = "TxtCalRX";
            TxtCalRX.Padding = new Padding(5);
            TxtCalRX.ShowText = false;
            TxtCalRX.Size = new Size(95, 26);
            TxtCalRX.TabIndex = 8;
            TxtCalRX.Text = "49.950";
            TxtCalRX.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCalRX.Type = UITextBox.UIEditType.Double;
            TxtCalRX.Watermark = "";
            // 
            // LalCalRX
            // 
            LalCalRX.AutoSize = true;
            LalCalRX.Location = new Point(11, 17);
            LalCalRX.Name = "LalCalRX";
            LalCalRX.Size = new Size(34, 24);
            LalCalRX.TabIndex = 7;
            LalCalRX.Text = "RX";
            // 
            // panel21
            // 
            panel21.Controls.Add(label33);
            panel21.Controls.Add(TxtCalLZ);
            panel21.Controls.Add(LalCalLZ);
            panel21.Dock = DockStyle.Fill;
            panel21.Location = new Point(351, 288);
            panel21.Name = "panel21";
            panel21.Size = new Size(168, 51);
            panel21.TabIndex = 178;
            // 
            // label33
            // 
            label33.AutoSize = true;
            label33.Location = new Point(134, 17);
            label33.Name = "label33";
            label33.Size = new Size(34, 24);
            label33.TabIndex = 11;
            label33.Text = "mm";
            // 
            // TxtCalLZ
            // 
            TxtCalLZ.ButtonSymbolOffset = new Point(0, 0);
            TxtCalLZ.DecimalPlaces = 3;
            TxtCalLZ.Enabled = false;
            TxtCalLZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCalLZ.Location = new Point(37, 12);
            TxtCalLZ.Margin = new Padding(4, 5, 4, 5);
            TxtCalLZ.MinimumSize = new Size(1, 16);
            TxtCalLZ.Name = "TxtCalLZ";
            TxtCalLZ.Padding = new Padding(5);
            TxtCalLZ.ShowText = false;
            TxtCalLZ.Size = new Size(95, 26);
            TxtCalLZ.TabIndex = 8;
            TxtCalLZ.Text = "0.000";
            TxtCalLZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCalLZ.Type = UITextBox.UIEditType.Double;
            TxtCalLZ.Watermark = "";
            // 
            // LalCalLZ
            // 
            LalCalLZ.AutoSize = true;
            LalCalLZ.Location = new Point(11, 17);
            LalCalLZ.Name = "LalCalLZ";
            LalCalLZ.Size = new Size(34, 24);
            LalCalLZ.TabIndex = 7;
            LalCalLZ.Text = "LZ";
            // 
            // panel20
            // 
            panel20.Controls.Add(label29);
            panel20.Controls.Add(TxtCalLY);
            panel20.Controls.Add(LalCalLY);
            panel20.Dock = DockStyle.Fill;
            panel20.Location = new Point(177, 288);
            panel20.Name = "panel20";
            panel20.Size = new Size(168, 51);
            panel20.TabIndex = 177;
            // 
            // label29
            // 
            label29.AutoSize = true;
            label29.Location = new Point(134, 17);
            label29.Name = "label29";
            label29.Size = new Size(34, 24);
            label29.TabIndex = 10;
            label29.Text = "mm";
            // 
            // TxtCalLY
            // 
            TxtCalLY.ButtonSymbolOffset = new Point(0, 0);
            TxtCalLY.DecimalPlaces = 3;
            TxtCalLY.Enabled = false;
            TxtCalLY.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCalLY.Location = new Point(37, 12);
            TxtCalLY.Margin = new Padding(4, 5, 4, 5);
            TxtCalLY.MinimumSize = new Size(1, 16);
            TxtCalLY.Name = "TxtCalLY";
            TxtCalLY.Padding = new Padding(5);
            TxtCalLY.ShowText = false;
            TxtCalLY.Size = new Size(95, 26);
            TxtCalLY.TabIndex = 6;
            TxtCalLY.Text = "0.000";
            TxtCalLY.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCalLY.Type = UITextBox.UIEditType.Double;
            TxtCalLY.Watermark = "";
            // 
            // LalCalLY
            // 
            LalCalLY.AutoSize = true;
            LalCalLY.Location = new Point(11, 17);
            LalCalLY.Name = "LalCalLY";
            LalCalLY.Size = new Size(34, 24);
            LalCalLY.TabIndex = 7;
            LalCalLY.Text = "LY";
            // 
            // panel18
            // 
            panel18.Controls.Add(TxtCalLX);
            panel18.Controls.Add(label17);
            panel18.Controls.Add(LalCalLX);
            panel18.Dock = DockStyle.Fill;
            panel18.Location = new Point(3, 288);
            panel18.Name = "panel18";
            panel18.Size = new Size(168, 51);
            panel18.TabIndex = 176;
            // 
            // TxtCalLX
            // 
            TxtCalLX.ButtonSymbolOffset = new Point(0, 0);
            TxtCalLX.DecimalPlaces = 3;
            TxtCalLX.Enabled = false;
            TxtCalLX.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCalLX.Location = new Point(37, 12);
            TxtCalLX.Margin = new Padding(4, 5, 4, 5);
            TxtCalLX.MinimumSize = new Size(1, 16);
            TxtCalLX.Name = "TxtCalLX";
            TxtCalLX.Padding = new Padding(5);
            TxtCalLX.ShowText = false;
            TxtCalLX.Size = new Size(95, 26);
            TxtCalLX.TabIndex = 9;
            TxtCalLX.Text = "0.000";
            TxtCalLX.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCalLX.Type = UITextBox.UIEditType.Double;
            TxtCalLX.Watermark = "";
            // 
            // label17
            // 
            label17.AutoSize = true;
            label17.Location = new Point(134, 17);
            label17.Name = "label17";
            label17.Size = new Size(34, 24);
            label17.TabIndex = 8;
            label17.Text = "mm";
            // 
            // LalCalLX
            // 
            LalCalLX.AutoSize = true;
            LalCalLX.Location = new Point(11, 17);
            LalCalLX.Name = "LalCalLX";
            LalCalLX.Size = new Size(34, 24);
            LalCalLX.TabIndex = 6;
            LalCalLX.Text = "LX";
            // 
            // panel13
            // 
            tableLayoutPanel1.SetColumnSpan(panel13, 10);
            panel13.Controls.Add(label16);
            panel13.Controls.Add(button2);
            panel13.Dock = DockStyle.Fill;
            panel13.Location = new Point(3, 231);
            panel13.Name = "panel13";
            panel13.Size = new Size(1738, 51);
            panel13.TabIndex = 175;
            // 
            // label16
            // 
            label16.AutoSize = true;
            label16.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label16.Location = new Point(53, 17);
            label16.Name = "label16";
            label16.Size = new Size(190, 24);
            label16.TabIndex = 5;
            label16.Text = "2：相机标定位置";
            // 
            // button2
            // 
            button2.BackColor = Color.Silver;
            button2.Location = new Point(2, 25);
            button2.Name = "button2";
            button2.Size = new Size(1789, 1);
            button2.TabIndex = 3;
            button2.Text = "button2";
            button2.UseVisualStyleBackColor = false;
            // 
            // panel67
            // 
            panel67.Controls.Add(label20);
            panel67.Controls.Add(TxtTopWaferPhotoLZ);
            panel67.Controls.Add(LalTopWaferPhotoLZ);
            panel67.Dock = DockStyle.Fill;
            panel67.Location = new Point(351, 402);
            panel67.Name = "panel67";
            panel67.Size = new Size(168, 51);
            panel67.TabIndex = 160;
            // 
            // label20
            // 
            label20.AutoSize = true;
            label20.Location = new Point(134, 17);
            label20.Name = "label20";
            label20.Size = new Size(34, 24);
            label20.TabIndex = 11;
            label20.Text = "mm";
            // 
            // TxtTopWaferPhotoLZ
            // 
            TxtTopWaferPhotoLZ.ButtonSymbolOffset = new Point(0, 0);
            TxtTopWaferPhotoLZ.DecimalPlaces = 3;
            TxtTopWaferPhotoLZ.Enabled = false;
            TxtTopWaferPhotoLZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTopWaferPhotoLZ.Location = new Point(37, 12);
            TxtTopWaferPhotoLZ.Margin = new Padding(4, 5, 4, 5);
            TxtTopWaferPhotoLZ.MinimumSize = new Size(1, 16);
            TxtTopWaferPhotoLZ.Name = "TxtTopWaferPhotoLZ";
            TxtTopWaferPhotoLZ.Padding = new Padding(5);
            TxtTopWaferPhotoLZ.ShowText = false;
            TxtTopWaferPhotoLZ.Size = new Size(95, 26);
            TxtTopWaferPhotoLZ.TabIndex = 8;
            TxtTopWaferPhotoLZ.Text = "0.000";
            TxtTopWaferPhotoLZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTopWaferPhotoLZ.Type = UITextBox.UIEditType.Double;
            TxtTopWaferPhotoLZ.Watermark = "";
            // 
            // LalTopWaferPhotoLZ
            // 
            LalTopWaferPhotoLZ.AutoSize = true;
            LalTopWaferPhotoLZ.Location = new Point(11, 17);
            LalTopWaferPhotoLZ.Name = "LalTopWaferPhotoLZ";
            LalTopWaferPhotoLZ.Size = new Size(34, 24);
            LalTopWaferPhotoLZ.TabIndex = 7;
            LalTopWaferPhotoLZ.Text = "LZ";
            // 
            // panel15
            // 
            tableLayoutPanel1.SetColumnSpan(panel15, 10);
            panel15.Controls.Add(label14);
            panel15.Controls.Add(button10);
            panel15.Controls.Add(label15);
            panel15.Controls.Add(button12);
            panel15.Dock = DockStyle.Fill;
            panel15.Location = new Point(3, 573);
            panel15.Name = "panel15";
            panel15.Size = new Size(1738, 51);
            panel15.TabIndex = 14;
            // 
            // label14
            // 
            label14.AutoSize = true;
            label14.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label14.Location = new Point(920, 17);
            label14.Name = "label14";
            label14.Size = new Size(166, 24);
            label14.TabIndex = 11;
            label14.Text = "6：下晶圆参数";
            // 
            // button10
            // 
            button10.BackColor = Color.Silver;
            button10.Location = new Point(870, 25);
            button10.Name = "button10";
            button10.Size = new Size(867, 1);
            button10.TabIndex = 9;
            button10.Text = "button10";
            button10.UseVisualStyleBackColor = false;
            // 
            // label15
            // 
            label15.AutoSize = true;
            label15.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label15.Location = new Point(53, 17);
            label15.Name = "label15";
            label15.Size = new Size(166, 24);
            label15.TabIndex = 8;
            label15.Text = "5：上晶圆参数";
            // 
            // button12
            // 
            button12.BackColor = Color.Silver;
            button12.Location = new Point(2, 25);
            button12.Name = "button12";
            button12.Size = new Size(839, 1);
            button12.TabIndex = 6;
            button12.Text = "button12";
            button12.UseVisualStyleBackColor = false;
            // 
            // panel4
            // 
            tableLayoutPanel1.SetColumnSpan(panel4, 10);
            panel4.Controls.Add(label12);
            panel4.Controls.Add(button6);
            panel4.Dock = DockStyle.Fill;
            panel4.Location = new Point(3, 345);
            panel4.Name = "panel4";
            panel4.Size = new Size(1738, 51);
            panel4.TabIndex = 4;
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label12.Location = new Point(53, 17);
            label12.Name = "label12";
            label12.Size = new Size(190, 24);
            label12.TabIndex = 8;
            label12.Text = "3：上晶圆识别位";
            // 
            // button6
            // 
            button6.BackColor = Color.Silver;
            button6.Location = new Point(2, 25);
            button6.Name = "button6";
            button6.Size = new Size(1789, 1);
            button6.TabIndex = 6;
            button6.Text = "button6";
            button6.UseVisualStyleBackColor = false;
            // 
            // panel8
            // 
            tableLayoutPanel1.SetColumnSpan(panel8, 10);
            panel8.Controls.Add(label13);
            panel8.Controls.Add(button8);
            panel8.Dock = DockStyle.Fill;
            panel8.Location = new Point(3, 459);
            panel8.Name = "panel8";
            panel8.Size = new Size(1738, 51);
            panel8.TabIndex = 8;
            // 
            // label13
            // 
            label13.AutoSize = true;
            label13.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label13.Location = new Point(53, 17);
            label13.Name = "label13";
            label13.Size = new Size(190, 24);
            label13.TabIndex = 8;
            label13.Text = "4：下晶圆拍照位";
            // 
            // button8
            // 
            button8.BackColor = Color.Silver;
            button8.Location = new Point(2, 25);
            button8.Name = "button8";
            button8.Size = new Size(1789, 1);
            button8.TabIndex = 6;
            button8.Text = "button8";
            button8.UseVisualStyleBackColor = false;
            // 
            // panel16
            // 
            panel16.Controls.Add(ChkTopHorizontalAdjust);
            panel16.Location = new Point(3, 630);
            panel16.Name = "panel16";
            panel16.Size = new Size(168, 51);
            panel16.TabIndex = 173;
            // 
            // ChkTopHorizontalAdjust
            // 
            ChkTopHorizontalAdjust.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            ChkTopHorizontalAdjust.ForeColor = Color.FromArgb(48, 48, 48);
            ChkTopHorizontalAdjust.Location = new Point(30, 11);
            ChkTopHorizontalAdjust.MinimumSize = new Size(1, 1);
            ChkTopHorizontalAdjust.Name = "ChkTopHorizontalAdjust";
            ChkTopHorizontalAdjust.Size = new Size(108, 29);
            ChkTopHorizontalAdjust.TabIndex = 0;
            ChkTopHorizontalAdjust.Text = "上晶圆调平";
            ChkTopHorizontalAdjust.CheckedChanged += ChkTopHorizontalAdjust_CheckedChanged;
            // 
            // panel19
            // 
            panel19.Controls.Add(ChkTopHorizontalPhoto);
            panel19.Location = new Point(177, 630);
            panel19.Name = "panel19";
            panel19.Size = new Size(168, 51);
            panel19.TabIndex = 174;
            // 
            // ChkTopHorizontalPhoto
            // 
            ChkTopHorizontalPhoto.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            ChkTopHorizontalPhoto.ForeColor = Color.FromArgb(48, 48, 48);
            ChkTopHorizontalPhoto.Location = new Point(14, 11);
            ChkTopHorizontalPhoto.MinimumSize = new Size(1, 1);
            ChkTopHorizontalPhoto.Name = "ChkTopHorizontalPhoto";
            ChkTopHorizontalPhoto.Size = new Size(140, 29);
            ChkTopHorizontalPhoto.TabIndex = 1;
            ChkTopHorizontalPhoto.Text = "调平后去拍照位";
            ChkTopHorizontalPhoto.CheckedChanged += ChkTopHorizontalPhoto_CheckedChanged;
            // 
            // panel33
            // 
            panel33.Controls.Add(label1);
            panel33.Controls.Add(TxtCurX);
            panel33.Controls.Add(LalCurX);
            panel33.Dock = DockStyle.Fill;
            panel33.Location = new Point(3, 60);
            panel33.Name = "panel33";
            panel33.Size = new Size(168, 51);
            panel33.TabIndex = 125;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(130, 17);
            label1.Name = "label1";
            label1.Size = new Size(34, 24);
            label1.TabIndex = 6;
            label1.Text = "mm";
            // 
            // TxtCurX
            // 
            TxtCurX.ButtonSymbolOffset = new Point(0, 0);
            TxtCurX.DecimalPlaces = 3;
            TxtCurX.Enabled = false;
            TxtCurX.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCurX.Location = new Point(33, 12);
            TxtCurX.Margin = new Padding(4, 5, 4, 5);
            TxtCurX.MinimumSize = new Size(1, 16);
            TxtCurX.Name = "TxtCurX";
            TxtCurX.Padding = new Padding(5);
            TxtCurX.ShowText = false;
            TxtCurX.Size = new Size(95, 26);
            TxtCurX.TabIndex = 5;
            TxtCurX.Text = "0.000";
            TxtCurX.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCurX.Type = UITextBox.UIEditType.Double;
            TxtCurX.Watermark = "";
            // 
            // LalCurX
            // 
            LalCurX.AutoSize = true;
            LalCurX.Location = new Point(15, 17);
            LalCurX.Name = "LalCurX";
            LalCurX.Size = new Size(22, 24);
            LalCurX.TabIndex = 0;
            LalCurX.Text = "X";
            // 
            // panel48
            // 
            tableLayoutPanel1.SetColumnSpan(panel48, 10);
            panel48.Controls.Add(lalTextName);
            panel48.Controls.Add(button1);
            panel48.Dock = DockStyle.Fill;
            panel48.Location = new Point(3, 3);
            panel48.Name = "panel48";
            panel48.Size = new Size(1738, 51);
            panel48.TabIndex = 134;
            // 
            // lalTextName
            // 
            lalTextName.AutoSize = true;
            lalTextName.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            lalTextName.Location = new Point(53, 17);
            lalTextName.Name = "lalTextName";
            lalTextName.Size = new Size(106, 24);
            lalTextName.TabIndex = 5;
            lalTextName.Text = "当前位置";
            // 
            // button1
            // 
            button1.BackColor = Color.Silver;
            button1.Location = new Point(2, 25);
            button1.Name = "button1";
            button1.Size = new Size(1789, 1);
            button1.TabIndex = 3;
            button1.Text = "button1";
            button1.UseVisualStyleBackColor = false;
            // 
            // panel34
            // 
            panel34.Controls.Add(label2);
            panel34.Controls.Add(TxtCurY);
            panel34.Controls.Add(LalCurY);
            panel34.Dock = DockStyle.Fill;
            panel34.Location = new Point(177, 60);
            panel34.Name = "panel34";
            panel34.Size = new Size(168, 51);
            panel34.TabIndex = 135;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(130, 17);
            label2.Name = "label2";
            label2.Size = new Size(34, 24);
            label2.TabIndex = 7;
            label2.Text = "mm";
            // 
            // TxtCurY
            // 
            TxtCurY.ButtonSymbolOffset = new Point(0, 0);
            TxtCurY.DecimalPlaces = 3;
            TxtCurY.Enabled = false;
            TxtCurY.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCurY.Location = new Point(33, 12);
            TxtCurY.Margin = new Padding(4, 5, 4, 5);
            TxtCurY.MinimumSize = new Size(1, 16);
            TxtCurY.Name = "TxtCurY";
            TxtCurY.Padding = new Padding(5);
            TxtCurY.ShowText = false;
            TxtCurY.Size = new Size(95, 26);
            TxtCurY.TabIndex = 5;
            TxtCurY.Text = "0.000";
            TxtCurY.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCurY.Type = UITextBox.UIEditType.Double;
            TxtCurY.Watermark = "";
            // 
            // LalCurY
            // 
            LalCurY.AutoSize = true;
            LalCurY.Location = new Point(15, 17);
            LalCurY.Name = "LalCurY";
            LalCurY.Size = new Size(22, 24);
            LalCurY.TabIndex = 0;
            LalCurY.Text = "Y";
            // 
            // panel49
            // 
            panel49.Controls.Add(label4);
            panel49.Controls.Add(TxtCurR);
            panel49.Controls.Add(LalCurR);
            panel49.Dock = DockStyle.Fill;
            panel49.Location = new Point(351, 60);
            panel49.Name = "panel49";
            panel49.Size = new Size(168, 51);
            panel49.TabIndex = 126;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new Point(130, 17);
            label4.Name = "label4";
            label4.Size = new Size(34, 24);
            label4.TabIndex = 7;
            label4.Text = "°";
            // 
            // TxtCurR
            // 
            TxtCurR.ButtonSymbolOffset = new Point(0, 0);
            TxtCurR.DecimalPlaces = 3;
            TxtCurR.Enabled = false;
            TxtCurR.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCurR.Location = new Point(33, 12);
            TxtCurR.Margin = new Padding(4, 5, 4, 5);
            TxtCurR.MinimumSize = new Size(1, 16);
            TxtCurR.Name = "TxtCurR";
            TxtCurR.Padding = new Padding(5);
            TxtCurR.ShowText = false;
            TxtCurR.Size = new Size(95, 26);
            TxtCurR.TabIndex = 4;
            TxtCurR.Text = "0.000";
            TxtCurR.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCurR.Type = UITextBox.UIEditType.Double;
            TxtCurR.Watermark = "";
            // 
            // LalCurR
            // 
            LalCurR.AutoSize = true;
            LalCurR.Location = new Point(15, 17);
            LalCurR.Name = "LalCurR";
            LalCurR.Size = new Size(22, 24);
            LalCurR.TabIndex = 0;
            LalCurR.Text = "R";
            // 
            // panel41
            // 
            panel41.Controls.Add(label5);
            panel41.Controls.Add(TxtCurZ);
            panel41.Controls.Add(LalCurZ);
            panel41.Dock = DockStyle.Fill;
            panel41.Location = new Point(525, 60);
            panel41.Name = "panel41";
            panel41.Size = new Size(168, 51);
            panel41.TabIndex = 136;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new Point(130, 17);
            label5.Name = "label5";
            label5.Size = new Size(34, 24);
            label5.TabIndex = 7;
            label5.Text = "mm";
            // 
            // TxtCurZ
            // 
            TxtCurZ.ButtonSymbolOffset = new Point(0, 0);
            TxtCurZ.DecimalPlaces = 3;
            TxtCurZ.Enabled = false;
            TxtCurZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCurZ.Location = new Point(33, 12);
            TxtCurZ.Margin = new Padding(4, 5, 4, 5);
            TxtCurZ.MinimumSize = new Size(1, 16);
            TxtCurZ.Name = "TxtCurZ";
            TxtCurZ.Padding = new Padding(5);
            TxtCurZ.ShowText = false;
            TxtCurZ.Size = new Size(95, 26);
            TxtCurZ.TabIndex = 5;
            TxtCurZ.Text = "0.000";
            TxtCurZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCurZ.Type = UITextBox.UIEditType.Double;
            TxtCurZ.Watermark = "";
            // 
            // LalCurZ
            // 
            LalCurZ.AutoSize = true;
            LalCurZ.Location = new Point(15, 17);
            LalCurZ.Name = "LalCurZ";
            LalCurZ.Size = new Size(22, 24);
            LalCurZ.TabIndex = 6;
            LalCurZ.Text = "Z";
            // 
            // panel42
            // 
            panel42.Controls.Add(label6);
            panel42.Controls.Add(TxtCurLX);
            panel42.Controls.Add(LalCurLX);
            panel42.Location = new Point(699, 60);
            panel42.Name = "panel42";
            panel42.Size = new Size(168, 51);
            panel42.TabIndex = 137;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new Point(134, 17);
            label6.Name = "label6";
            label6.Size = new Size(34, 24);
            label6.TabIndex = 7;
            label6.Text = "mm";
            // 
            // TxtCurLX
            // 
            TxtCurLX.ButtonSymbolOffset = new Point(0, 0);
            TxtCurLX.DecimalPlaces = 3;
            TxtCurLX.Enabled = false;
            TxtCurLX.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCurLX.Location = new Point(37, 12);
            TxtCurLX.Margin = new Padding(4, 5, 4, 5);
            TxtCurLX.MinimumSize = new Size(1, 16);
            TxtCurLX.Name = "TxtCurLX";
            TxtCurLX.Padding = new Padding(5);
            TxtCurLX.ShowText = false;
            TxtCurLX.Size = new Size(95, 26);
            TxtCurLX.TabIndex = 5;
            TxtCurLX.Text = "0.000";
            TxtCurLX.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCurLX.Type = UITextBox.UIEditType.Double;
            TxtCurLX.Watermark = "";
            // 
            // LalCurLX
            // 
            LalCurLX.AutoSize = true;
            LalCurLX.Location = new Point(11, 17);
            LalCurLX.Name = "LalCurLX";
            LalCurLX.Size = new Size(34, 24);
            LalCurLX.TabIndex = 0;
            LalCurLX.Text = "LX";
            // 
            // panel43
            // 
            panel43.Controls.Add(label7);
            panel43.Controls.Add(TxtCurLY);
            panel43.Controls.Add(LalCurLY);
            panel43.Dock = DockStyle.Fill;
            panel43.Location = new Point(873, 60);
            panel43.Name = "panel43";
            panel43.Size = new Size(168, 51);
            panel43.TabIndex = 138;
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new Point(134, 17);
            label7.Name = "label7";
            label7.Size = new Size(34, 24);
            label7.TabIndex = 7;
            label7.Text = "mm";
            // 
            // TxtCurLY
            // 
            TxtCurLY.ButtonSymbolOffset = new Point(0, 0);
            TxtCurLY.DecimalPlaces = 3;
            TxtCurLY.Enabled = false;
            TxtCurLY.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCurLY.Location = new Point(37, 12);
            TxtCurLY.Margin = new Padding(4, 5, 4, 5);
            TxtCurLY.MinimumSize = new Size(1, 16);
            TxtCurLY.Name = "TxtCurLY";
            TxtCurLY.Padding = new Padding(5);
            TxtCurLY.ShowText = false;
            TxtCurLY.Size = new Size(95, 26);
            TxtCurLY.TabIndex = 5;
            TxtCurLY.Text = "0.000";
            TxtCurLY.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCurLY.Type = UITextBox.UIEditType.Double;
            TxtCurLY.Watermark = "";
            // 
            // LalCurLY
            // 
            LalCurLY.AutoSize = true;
            LalCurLY.Location = new Point(11, 17);
            LalCurLY.Name = "LalCurLY";
            LalCurLY.Size = new Size(34, 24);
            LalCurLY.TabIndex = 0;
            LalCurLY.Text = "LY";
            // 
            // panel44
            // 
            panel44.Controls.Add(label8);
            panel44.Controls.Add(TxtCurLZ);
            panel44.Controls.Add(LalCurLZ);
            panel44.Dock = DockStyle.Fill;
            panel44.Location = new Point(1047, 60);
            panel44.Name = "panel44";
            panel44.Size = new Size(168, 51);
            panel44.TabIndex = 139;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new Point(134, 17);
            label8.Name = "label8";
            label8.Size = new Size(34, 24);
            label8.TabIndex = 7;
            label8.Text = "mm";
            // 
            // TxtCurLZ
            // 
            TxtCurLZ.ButtonSymbolOffset = new Point(0, 0);
            TxtCurLZ.DecimalPlaces = 3;
            TxtCurLZ.Enabled = false;
            TxtCurLZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCurLZ.Location = new Point(37, 12);
            TxtCurLZ.Margin = new Padding(4, 5, 4, 5);
            TxtCurLZ.MinimumSize = new Size(1, 16);
            TxtCurLZ.Name = "TxtCurLZ";
            TxtCurLZ.Padding = new Padding(5);
            TxtCurLZ.ShowText = false;
            TxtCurLZ.Size = new Size(95, 26);
            TxtCurLZ.TabIndex = 5;
            TxtCurLZ.Text = "0.000";
            TxtCurLZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCurLZ.Type = UITextBox.UIEditType.Double;
            TxtCurLZ.Watermark = "";
            // 
            // LalCurLZ
            // 
            LalCurLZ.AutoSize = true;
            LalCurLZ.Location = new Point(11, 17);
            LalCurLZ.Name = "LalCurLZ";
            LalCurLZ.Size = new Size(34, 24);
            LalCurLZ.TabIndex = 0;
            LalCurLZ.Text = "LZ";
            // 
            // panel45
            // 
            panel45.Controls.Add(label9);
            panel45.Controls.Add(TxtCurRX);
            panel45.Controls.Add(LalCurRX);
            panel45.Dock = DockStyle.Fill;
            panel45.Location = new Point(1221, 60);
            panel45.Name = "panel45";
            panel45.Size = new Size(168, 51);
            panel45.TabIndex = 140;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Location = new Point(134, 17);
            label9.Name = "label9";
            label9.Size = new Size(34, 24);
            label9.TabIndex = 7;
            label9.Text = "mm";
            // 
            // TxtCurRX
            // 
            TxtCurRX.ButtonSymbolOffset = new Point(0, 0);
            TxtCurRX.DecimalPlaces = 3;
            TxtCurRX.Enabled = false;
            TxtCurRX.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCurRX.Location = new Point(37, 12);
            TxtCurRX.Margin = new Padding(4, 5, 4, 5);
            TxtCurRX.MinimumSize = new Size(1, 16);
            TxtCurRX.Name = "TxtCurRX";
            TxtCurRX.Padding = new Padding(5);
            TxtCurRX.ShowText = false;
            TxtCurRX.Size = new Size(95, 26);
            TxtCurRX.TabIndex = 5;
            TxtCurRX.Text = "0.000";
            TxtCurRX.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCurRX.Type = UITextBox.UIEditType.Double;
            TxtCurRX.Watermark = "";
            // 
            // LalCurRX
            // 
            LalCurRX.AutoSize = true;
            LalCurRX.Location = new Point(11, 17);
            LalCurRX.Name = "LalCurRX";
            LalCurRX.Size = new Size(34, 24);
            LalCurRX.TabIndex = 0;
            LalCurRX.Text = "RX";
            // 
            // panel46
            // 
            panel46.Controls.Add(label11);
            panel46.Controls.Add(TxtCurRZ);
            panel46.Controls.Add(LalCurRZ);
            panel46.Dock = DockStyle.Fill;
            panel46.Location = new Point(1569, 60);
            panel46.Name = "panel46";
            panel46.Size = new Size(172, 51);
            panel46.TabIndex = 141;
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.Location = new Point(134, 17);
            label11.Name = "label11";
            label11.Size = new Size(34, 24);
            label11.TabIndex = 7;
            label11.Text = "mm";
            // 
            // TxtCurRZ
            // 
            TxtCurRZ.ButtonSymbolOffset = new Point(0, 0);
            TxtCurRZ.DecimalPlaces = 3;
            TxtCurRZ.Enabled = false;
            TxtCurRZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCurRZ.Location = new Point(37, 12);
            TxtCurRZ.Margin = new Padding(4, 5, 4, 5);
            TxtCurRZ.MinimumSize = new Size(1, 16);
            TxtCurRZ.Name = "TxtCurRZ";
            TxtCurRZ.Padding = new Padding(5);
            TxtCurRZ.ShowText = false;
            TxtCurRZ.Size = new Size(95, 26);
            TxtCurRZ.TabIndex = 5;
            TxtCurRZ.Text = "0.000";
            TxtCurRZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCurRZ.Type = UITextBox.UIEditType.Double;
            TxtCurRZ.Watermark = "";
            // 
            // LalCurRZ
            // 
            LalCurRZ.AutoSize = true;
            LalCurRZ.Location = new Point(11, 17);
            LalCurRZ.Name = "LalCurRZ";
            LalCurRZ.Size = new Size(34, 24);
            LalCurRZ.TabIndex = 0;
            LalCurRZ.Text = "RZ";
            // 
            // panel64
            // 
            panel64.Controls.Add(label10);
            panel64.Controls.Add(TxtCurRY);
            panel64.Controls.Add(LalCurRY);
            panel64.Dock = DockStyle.Fill;
            panel64.Location = new Point(1395, 60);
            panel64.Name = "panel64";
            panel64.Size = new Size(168, 51);
            panel64.TabIndex = 157;
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Location = new Point(134, 17);
            label10.Name = "label10";
            label10.Size = new Size(34, 24);
            label10.TabIndex = 7;
            label10.Text = "mm";
            // 
            // TxtCurRY
            // 
            TxtCurRY.ButtonSymbolOffset = new Point(0, 0);
            TxtCurRY.DecimalPlaces = 3;
            TxtCurRY.Enabled = false;
            TxtCurRY.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCurRY.Location = new Point(37, 12);
            TxtCurRY.Margin = new Padding(4, 5, 4, 5);
            TxtCurRY.MinimumSize = new Size(1, 16);
            TxtCurRY.Name = "TxtCurRY";
            TxtCurRY.Padding = new Padding(5);
            TxtCurRY.ShowText = false;
            TxtCurRY.Size = new Size(95, 26);
            TxtCurRY.TabIndex = 5;
            TxtCurRY.Text = "0.000";
            TxtCurRY.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCurRY.Type = UITextBox.UIEditType.Double;
            TxtCurRY.Watermark = "";
            // 
            // LalCurRY
            // 
            LalCurRY.AutoSize = true;
            LalCurRY.Location = new Point(11, 17);
            LalCurRY.Name = "LalCurRY";
            LalCurRY.Size = new Size(34, 24);
            LalCurRY.TabIndex = 0;
            LalCurRY.Text = "RY";
            // 
            // panel10
            // 
            panel10.Controls.Add(TxtTopWaferPhotoLX);
            panel10.Controls.Add(label23);
            panel10.Controls.Add(LalTopWaferPhotoLX);
            panel10.Dock = DockStyle.Fill;
            panel10.Location = new Point(3, 402);
            panel10.Name = "panel10";
            panel10.Size = new Size(168, 51);
            panel10.TabIndex = 10;
            // 
            // TxtTopWaferPhotoLX
            // 
            TxtTopWaferPhotoLX.ButtonSymbolOffset = new Point(0, 0);
            TxtTopWaferPhotoLX.DecimalPlaces = 3;
            TxtTopWaferPhotoLX.Enabled = false;
            TxtTopWaferPhotoLX.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTopWaferPhotoLX.Location = new Point(37, 12);
            TxtTopWaferPhotoLX.Margin = new Padding(4, 5, 4, 5);
            TxtTopWaferPhotoLX.MinimumSize = new Size(1, 16);
            TxtTopWaferPhotoLX.Name = "TxtTopWaferPhotoLX";
            TxtTopWaferPhotoLX.Padding = new Padding(5);
            TxtTopWaferPhotoLX.ShowText = false;
            TxtTopWaferPhotoLX.Size = new Size(95, 26);
            TxtTopWaferPhotoLX.TabIndex = 9;
            TxtTopWaferPhotoLX.Text = "0.000";
            TxtTopWaferPhotoLX.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTopWaferPhotoLX.Type = UITextBox.UIEditType.Double;
            TxtTopWaferPhotoLX.Watermark = "";
            // 
            // label23
            // 
            label23.AutoSize = true;
            label23.Location = new Point(134, 17);
            label23.Name = "label23";
            label23.Size = new Size(34, 24);
            label23.TabIndex = 8;
            label23.Text = "mm";
            // 
            // LalTopWaferPhotoLX
            // 
            LalTopWaferPhotoLX.AutoSize = true;
            LalTopWaferPhotoLX.Location = new Point(11, 17);
            LalTopWaferPhotoLX.Name = "LalTopWaferPhotoLX";
            LalTopWaferPhotoLX.Size = new Size(34, 24);
            LalTopWaferPhotoLX.TabIndex = 6;
            LalTopWaferPhotoLX.Text = "LX";
            // 
            // panel14
            // 
            panel14.Controls.Add(label19);
            panel14.Controls.Add(TxtTopWaferPhotoLY);
            panel14.Controls.Add(LalTopWaferPhotoLY);
            panel14.Dock = DockStyle.Fill;
            panel14.Location = new Point(177, 402);
            panel14.Name = "panel14";
            panel14.Size = new Size(168, 51);
            panel14.TabIndex = 13;
            // 
            // label19
            // 
            label19.AutoSize = true;
            label19.Location = new Point(134, 17);
            label19.Name = "label19";
            label19.Size = new Size(34, 24);
            label19.TabIndex = 10;
            label19.Text = "mm";
            // 
            // TxtTopWaferPhotoLY
            // 
            TxtTopWaferPhotoLY.ButtonSymbolOffset = new Point(0, 0);
            TxtTopWaferPhotoLY.DecimalPlaces = 3;
            TxtTopWaferPhotoLY.Enabled = false;
            TxtTopWaferPhotoLY.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTopWaferPhotoLY.Location = new Point(37, 12);
            TxtTopWaferPhotoLY.Margin = new Padding(4, 5, 4, 5);
            TxtTopWaferPhotoLY.MinimumSize = new Size(1, 16);
            TxtTopWaferPhotoLY.Name = "TxtTopWaferPhotoLY";
            TxtTopWaferPhotoLY.Padding = new Padding(5);
            TxtTopWaferPhotoLY.ShowText = false;
            TxtTopWaferPhotoLY.Size = new Size(95, 26);
            TxtTopWaferPhotoLY.TabIndex = 6;
            TxtTopWaferPhotoLY.Text = "0.000";
            TxtTopWaferPhotoLY.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTopWaferPhotoLY.Type = UITextBox.UIEditType.Double;
            TxtTopWaferPhotoLY.Watermark = "";
            // 
            // LalTopWaferPhotoLY
            // 
            LalTopWaferPhotoLY.AutoSize = true;
            LalTopWaferPhotoLY.Location = new Point(11, 17);
            LalTopWaferPhotoLY.Name = "LalTopWaferPhotoLY";
            LalTopWaferPhotoLY.Size = new Size(34, 24);
            LalTopWaferPhotoLY.TabIndex = 7;
            LalTopWaferPhotoLY.Text = "LY";
            // 
            // panel51
            // 
            panel51.Controls.Add(label22);
            panel51.Controls.Add(TxtTopWaferPhotoRY);
            panel51.Controls.Add(LalTopWaferPhotoRY);
            panel51.Dock = DockStyle.Fill;
            panel51.Location = new Point(699, 402);
            panel51.Name = "panel51";
            panel51.Size = new Size(168, 51);
            panel51.TabIndex = 145;
            // 
            // label22
            // 
            label22.AutoSize = true;
            label22.Location = new Point(134, 17);
            label22.Name = "label22";
            label22.Size = new Size(34, 24);
            label22.TabIndex = 13;
            label22.Text = "mm";
            // 
            // TxtTopWaferPhotoRY
            // 
            TxtTopWaferPhotoRY.ButtonSymbolOffset = new Point(0, 0);
            TxtTopWaferPhotoRY.DecimalPlaces = 3;
            TxtTopWaferPhotoRY.Enabled = false;
            TxtTopWaferPhotoRY.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTopWaferPhotoRY.Location = new Point(37, 12);
            TxtTopWaferPhotoRY.Margin = new Padding(4, 5, 4, 5);
            TxtTopWaferPhotoRY.MinimumSize = new Size(1, 16);
            TxtTopWaferPhotoRY.Name = "TxtTopWaferPhotoRY";
            TxtTopWaferPhotoRY.Padding = new Padding(5);
            TxtTopWaferPhotoRY.ShowText = false;
            TxtTopWaferPhotoRY.Size = new Size(95, 26);
            TxtTopWaferPhotoRY.TabIndex = 6;
            TxtTopWaferPhotoRY.Text = "0.000";
            TxtTopWaferPhotoRY.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTopWaferPhotoRY.Type = UITextBox.UIEditType.Double;
            TxtTopWaferPhotoRY.Watermark = "";
            // 
            // LalTopWaferPhotoRY
            // 
            LalTopWaferPhotoRY.AutoSize = true;
            LalTopWaferPhotoRY.Location = new Point(11, 17);
            LalTopWaferPhotoRY.Name = "LalTopWaferPhotoRY";
            LalTopWaferPhotoRY.Size = new Size(34, 24);
            LalTopWaferPhotoRY.TabIndex = 7;
            LalTopWaferPhotoRY.Text = "RY";
            // 
            // panel52
            // 
            panel52.Controls.Add(label18);
            panel52.Controls.Add(TxtTopWaferPhotoRZ);
            panel52.Controls.Add(LalTopWaferPhotoRZ);
            panel52.Dock = DockStyle.Fill;
            panel52.Location = new Point(873, 402);
            panel52.Name = "panel52";
            panel52.Size = new Size(168, 51);
            panel52.TabIndex = 146;
            // 
            // label18
            // 
            label18.AutoSize = true;
            label18.Location = new Point(134, 17);
            label18.Name = "label18";
            label18.Size = new Size(34, 24);
            label18.TabIndex = 9;
            label18.Text = "mm";
            // 
            // TxtTopWaferPhotoRZ
            // 
            TxtTopWaferPhotoRZ.ButtonSymbolOffset = new Point(0, 0);
            TxtTopWaferPhotoRZ.DecimalPlaces = 3;
            TxtTopWaferPhotoRZ.Enabled = false;
            TxtTopWaferPhotoRZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTopWaferPhotoRZ.Location = new Point(37, 12);
            TxtTopWaferPhotoRZ.Margin = new Padding(4, 5, 4, 5);
            TxtTopWaferPhotoRZ.MinimumSize = new Size(1, 16);
            TxtTopWaferPhotoRZ.Name = "TxtTopWaferPhotoRZ";
            TxtTopWaferPhotoRZ.Padding = new Padding(5);
            TxtTopWaferPhotoRZ.ShowText = false;
            TxtTopWaferPhotoRZ.Size = new Size(95, 26);
            TxtTopWaferPhotoRZ.TabIndex = 7;
            TxtTopWaferPhotoRZ.Text = "0.000";
            TxtTopWaferPhotoRZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTopWaferPhotoRZ.Type = UITextBox.UIEditType.Double;
            TxtTopWaferPhotoRZ.Watermark = "";
            // 
            // LalTopWaferPhotoRZ
            // 
            LalTopWaferPhotoRZ.AutoSize = true;
            LalTopWaferPhotoRZ.Location = new Point(11, 17);
            LalTopWaferPhotoRZ.Name = "LalTopWaferPhotoRZ";
            LalTopWaferPhotoRZ.Size = new Size(34, 24);
            LalTopWaferPhotoRZ.TabIndex = 8;
            LalTopWaferPhotoRZ.Text = "RZ";
            // 
            // panel25
            // 
            panel25.Controls.Add(label25);
            panel25.Controls.Add(TxtBottomWaferPhotoZ);
            panel25.Controls.Add(LalBottomWaferPhotoZ);
            panel25.Dock = DockStyle.Fill;
            panel25.Location = new Point(1047, 516);
            panel25.Name = "panel25";
            panel25.Size = new Size(168, 51);
            panel25.TabIndex = 102;
            // 
            // label25
            // 
            label25.AutoSize = true;
            label25.Location = new Point(130, 17);
            label25.Name = "label25";
            label25.Size = new Size(34, 24);
            label25.TabIndex = 10;
            label25.Text = "mm";
            // 
            // TxtBottomWaferPhotoZ
            // 
            TxtBottomWaferPhotoZ.ButtonSymbolOffset = new Point(0, 0);
            TxtBottomWaferPhotoZ.DecimalPlaces = 3;
            TxtBottomWaferPhotoZ.Enabled = false;
            TxtBottomWaferPhotoZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtBottomWaferPhotoZ.Location = new Point(33, 12);
            TxtBottomWaferPhotoZ.Margin = new Padding(4, 5, 4, 5);
            TxtBottomWaferPhotoZ.MinimumSize = new Size(1, 16);
            TxtBottomWaferPhotoZ.Name = "TxtBottomWaferPhotoZ";
            TxtBottomWaferPhotoZ.Padding = new Padding(5);
            TxtBottomWaferPhotoZ.ShowText = false;
            TxtBottomWaferPhotoZ.Size = new Size(95, 26);
            TxtBottomWaferPhotoZ.TabIndex = 8;
            TxtBottomWaferPhotoZ.Text = "0.000";
            TxtBottomWaferPhotoZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtBottomWaferPhotoZ.Type = UITextBox.UIEditType.Double;
            TxtBottomWaferPhotoZ.Watermark = "";
            // 
            // LalBottomWaferPhotoZ
            // 
            LalBottomWaferPhotoZ.AutoSize = true;
            LalBottomWaferPhotoZ.Location = new Point(15, 17);
            LalBottomWaferPhotoZ.Name = "LalBottomWaferPhotoZ";
            LalBottomWaferPhotoZ.Size = new Size(22, 24);
            LalBottomWaferPhotoZ.TabIndex = 4;
            LalBottomWaferPhotoZ.Text = "Z";
            // 
            // panel70
            // 
            panel70.Controls.Add(label21);
            panel70.Controls.Add(TxtTopWaferPhotoRX);
            panel70.Controls.Add(LalTopWaferPhotoRX);
            panel70.Dock = DockStyle.Fill;
            panel70.Location = new Point(525, 402);
            panel70.Name = "panel70";
            panel70.Size = new Size(168, 51);
            panel70.TabIndex = 164;
            // 
            // label21
            // 
            label21.AutoSize = true;
            label21.Location = new Point(134, 17);
            label21.Name = "label21";
            label21.Size = new Size(34, 24);
            label21.TabIndex = 12;
            label21.Text = "mm";
            // 
            // TxtTopWaferPhotoRX
            // 
            TxtTopWaferPhotoRX.ButtonSymbolOffset = new Point(0, 0);
            TxtTopWaferPhotoRX.DecimalPlaces = 3;
            TxtTopWaferPhotoRX.DoubleValue = 49.95D;
            TxtTopWaferPhotoRX.Enabled = false;
            TxtTopWaferPhotoRX.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTopWaferPhotoRX.Location = new Point(37, 12);
            TxtTopWaferPhotoRX.Margin = new Padding(4, 5, 4, 5);
            TxtTopWaferPhotoRX.MinimumSize = new Size(1, 16);
            TxtTopWaferPhotoRX.Name = "TxtTopWaferPhotoRX";
            TxtTopWaferPhotoRX.Padding = new Padding(5);
            TxtTopWaferPhotoRX.ShowText = false;
            TxtTopWaferPhotoRX.Size = new Size(95, 26);
            TxtTopWaferPhotoRX.TabIndex = 8;
            TxtTopWaferPhotoRX.Text = "49.950";
            TxtTopWaferPhotoRX.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTopWaferPhotoRX.Type = UITextBox.UIEditType.Double;
            TxtTopWaferPhotoRX.Watermark = "";
            // 
            // LalTopWaferPhotoRX
            // 
            LalTopWaferPhotoRX.AutoSize = true;
            LalTopWaferPhotoRX.Location = new Point(11, 17);
            LalTopWaferPhotoRX.Name = "LalTopWaferPhotoRX";
            LalTopWaferPhotoRX.Size = new Size(34, 24);
            LalTopWaferPhotoRX.TabIndex = 7;
            LalTopWaferPhotoRX.Text = "RX";
            // 
            // panel6
            // 
            panel6.Controls.Add(TxtBottomWaferPhotoLX);
            panel6.Controls.Add(label24);
            panel6.Controls.Add(LalBottomWaferPhotoLX);
            panel6.Dock = DockStyle.Fill;
            panel6.Location = new Point(3, 516);
            panel6.Name = "panel6";
            panel6.Size = new Size(168, 51);
            panel6.TabIndex = 165;
            // 
            // TxtBottomWaferPhotoLX
            // 
            TxtBottomWaferPhotoLX.ButtonSymbolOffset = new Point(0, 0);
            TxtBottomWaferPhotoLX.DecimalPlaces = 3;
            TxtBottomWaferPhotoLX.Enabled = false;
            TxtBottomWaferPhotoLX.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtBottomWaferPhotoLX.Location = new Point(37, 12);
            TxtBottomWaferPhotoLX.Margin = new Padding(4, 5, 4, 5);
            TxtBottomWaferPhotoLX.MinimumSize = new Size(1, 16);
            TxtBottomWaferPhotoLX.Name = "TxtBottomWaferPhotoLX";
            TxtBottomWaferPhotoLX.Padding = new Padding(5);
            TxtBottomWaferPhotoLX.ShowText = false;
            TxtBottomWaferPhotoLX.Size = new Size(95, 26);
            TxtBottomWaferPhotoLX.TabIndex = 9;
            TxtBottomWaferPhotoLX.Text = "0.000";
            TxtBottomWaferPhotoLX.TextAlignment = ContentAlignment.MiddleCenter;
            TxtBottomWaferPhotoLX.Type = UITextBox.UIEditType.Double;
            TxtBottomWaferPhotoLX.Watermark = "";
            // 
            // label24
            // 
            label24.AutoSize = true;
            label24.Location = new Point(134, 17);
            label24.Name = "label24";
            label24.Size = new Size(34, 24);
            label24.TabIndex = 8;
            label24.Text = "mm";
            // 
            // LalBottomWaferPhotoLX
            // 
            LalBottomWaferPhotoLX.AutoSize = true;
            LalBottomWaferPhotoLX.Location = new Point(11, 17);
            LalBottomWaferPhotoLX.Name = "LalBottomWaferPhotoLX";
            LalBottomWaferPhotoLX.Size = new Size(34, 24);
            LalBottomWaferPhotoLX.TabIndex = 6;
            LalBottomWaferPhotoLX.Text = "LX";
            // 
            // panel7
            // 
            panel7.Controls.Add(label26);
            panel7.Controls.Add(TxtBottomWaferPhotoLY);
            panel7.Controls.Add(LalBottomWaferPhotoLY);
            panel7.Dock = DockStyle.Fill;
            panel7.Location = new Point(177, 516);
            panel7.Name = "panel7";
            panel7.Size = new Size(168, 51);
            panel7.TabIndex = 166;
            // 
            // label26
            // 
            label26.AutoSize = true;
            label26.Location = new Point(134, 17);
            label26.Name = "label26";
            label26.Size = new Size(34, 24);
            label26.TabIndex = 10;
            label26.Text = "mm";
            // 
            // TxtBottomWaferPhotoLY
            // 
            TxtBottomWaferPhotoLY.ButtonSymbolOffset = new Point(0, 0);
            TxtBottomWaferPhotoLY.DecimalPlaces = 3;
            TxtBottomWaferPhotoLY.Enabled = false;
            TxtBottomWaferPhotoLY.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtBottomWaferPhotoLY.Location = new Point(37, 12);
            TxtBottomWaferPhotoLY.Margin = new Padding(4, 5, 4, 5);
            TxtBottomWaferPhotoLY.MinimumSize = new Size(1, 16);
            TxtBottomWaferPhotoLY.Name = "TxtBottomWaferPhotoLY";
            TxtBottomWaferPhotoLY.Padding = new Padding(5);
            TxtBottomWaferPhotoLY.ShowText = false;
            TxtBottomWaferPhotoLY.Size = new Size(95, 26);
            TxtBottomWaferPhotoLY.TabIndex = 6;
            TxtBottomWaferPhotoLY.Text = "0.000";
            TxtBottomWaferPhotoLY.TextAlignment = ContentAlignment.MiddleCenter;
            TxtBottomWaferPhotoLY.Type = UITextBox.UIEditType.Double;
            TxtBottomWaferPhotoLY.Watermark = "";
            // 
            // LalBottomWaferPhotoLY
            // 
            LalBottomWaferPhotoLY.AutoSize = true;
            LalBottomWaferPhotoLY.Location = new Point(11, 17);
            LalBottomWaferPhotoLY.Name = "LalBottomWaferPhotoLY";
            LalBottomWaferPhotoLY.Size = new Size(34, 24);
            LalBottomWaferPhotoLY.TabIndex = 7;
            LalBottomWaferPhotoLY.Text = "LY";
            // 
            // panel71
            // 
            panel71.Controls.Add(label28);
            panel71.Controls.Add(TxtBottomWaferPhotoLZ);
            panel71.Controls.Add(LalBottomWaferPhotoLZ);
            panel71.Dock = DockStyle.Fill;
            panel71.Location = new Point(351, 516);
            panel71.Name = "panel71";
            panel71.Size = new Size(168, 51);
            panel71.TabIndex = 167;
            // 
            // label28
            // 
            label28.AutoSize = true;
            label28.Location = new Point(134, 17);
            label28.Name = "label28";
            label28.Size = new Size(34, 24);
            label28.TabIndex = 11;
            label28.Text = "mm";
            // 
            // TxtBottomWaferPhotoLZ
            // 
            TxtBottomWaferPhotoLZ.ButtonSymbolOffset = new Point(0, 0);
            TxtBottomWaferPhotoLZ.DecimalPlaces = 3;
            TxtBottomWaferPhotoLZ.Enabled = false;
            TxtBottomWaferPhotoLZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtBottomWaferPhotoLZ.Location = new Point(37, 12);
            TxtBottomWaferPhotoLZ.Margin = new Padding(4, 5, 4, 5);
            TxtBottomWaferPhotoLZ.MinimumSize = new Size(1, 16);
            TxtBottomWaferPhotoLZ.Name = "TxtBottomWaferPhotoLZ";
            TxtBottomWaferPhotoLZ.Padding = new Padding(5);
            TxtBottomWaferPhotoLZ.ShowText = false;
            TxtBottomWaferPhotoLZ.Size = new Size(95, 26);
            TxtBottomWaferPhotoLZ.TabIndex = 8;
            TxtBottomWaferPhotoLZ.Text = "0.000";
            TxtBottomWaferPhotoLZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtBottomWaferPhotoLZ.Type = UITextBox.UIEditType.Double;
            TxtBottomWaferPhotoLZ.Watermark = "";
            // 
            // LalBottomWaferPhotoLZ
            // 
            LalBottomWaferPhotoLZ.AutoSize = true;
            LalBottomWaferPhotoLZ.Location = new Point(11, 17);
            LalBottomWaferPhotoLZ.Name = "LalBottomWaferPhotoLZ";
            LalBottomWaferPhotoLZ.Size = new Size(34, 24);
            LalBottomWaferPhotoLZ.TabIndex = 7;
            LalBottomWaferPhotoLZ.Text = "LZ";
            // 
            // panel12
            // 
            panel12.Controls.Add(label30);
            panel12.Controls.Add(TxtBottomWaferPhotoRX);
            panel12.Controls.Add(LalBottomWaferPhotoRX);
            panel12.Dock = DockStyle.Fill;
            panel12.Location = new Point(525, 516);
            panel12.Name = "panel12";
            panel12.Size = new Size(168, 51);
            panel12.TabIndex = 168;
            // 
            // label30
            // 
            label30.AutoSize = true;
            label30.Location = new Point(134, 17);
            label30.Name = "label30";
            label30.Size = new Size(34, 24);
            label30.TabIndex = 12;
            label30.Text = "mm";
            // 
            // TxtBottomWaferPhotoRX
            // 
            TxtBottomWaferPhotoRX.ButtonSymbolOffset = new Point(0, 0);
            TxtBottomWaferPhotoRX.DecimalPlaces = 3;
            TxtBottomWaferPhotoRX.DoubleValue = 49.95D;
            TxtBottomWaferPhotoRX.Enabled = false;
            TxtBottomWaferPhotoRX.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtBottomWaferPhotoRX.Location = new Point(37, 12);
            TxtBottomWaferPhotoRX.Margin = new Padding(4, 5, 4, 5);
            TxtBottomWaferPhotoRX.MinimumSize = new Size(1, 16);
            TxtBottomWaferPhotoRX.Name = "TxtBottomWaferPhotoRX";
            TxtBottomWaferPhotoRX.Padding = new Padding(5);
            TxtBottomWaferPhotoRX.ShowText = false;
            TxtBottomWaferPhotoRX.Size = new Size(95, 26);
            TxtBottomWaferPhotoRX.TabIndex = 8;
            TxtBottomWaferPhotoRX.Text = "49.950";
            TxtBottomWaferPhotoRX.TextAlignment = ContentAlignment.MiddleCenter;
            TxtBottomWaferPhotoRX.Type = UITextBox.UIEditType.Double;
            TxtBottomWaferPhotoRX.Watermark = "";
            // 
            // LalBottomWaferPhotoRX
            // 
            LalBottomWaferPhotoRX.AutoSize = true;
            LalBottomWaferPhotoRX.Location = new Point(11, 17);
            LalBottomWaferPhotoRX.Name = "LalBottomWaferPhotoRX";
            LalBottomWaferPhotoRX.Size = new Size(34, 24);
            LalBottomWaferPhotoRX.TabIndex = 7;
            LalBottomWaferPhotoRX.Text = "RX";
            // 
            // panel17
            // 
            panel17.Controls.Add(label32);
            panel17.Controls.Add(TxtBottomWaferPhotoRY);
            panel17.Controls.Add(LalBottomWaferPhotoRY);
            panel17.Dock = DockStyle.Fill;
            panel17.Location = new Point(699, 516);
            panel17.Name = "panel17";
            panel17.Size = new Size(168, 51);
            panel17.TabIndex = 169;
            // 
            // label32
            // 
            label32.AutoSize = true;
            label32.Location = new Point(134, 17);
            label32.Name = "label32";
            label32.Size = new Size(34, 24);
            label32.TabIndex = 13;
            label32.Text = "mm";
            // 
            // TxtBottomWaferPhotoRY
            // 
            TxtBottomWaferPhotoRY.ButtonSymbolOffset = new Point(0, 0);
            TxtBottomWaferPhotoRY.DecimalPlaces = 3;
            TxtBottomWaferPhotoRY.Enabled = false;
            TxtBottomWaferPhotoRY.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtBottomWaferPhotoRY.Location = new Point(37, 12);
            TxtBottomWaferPhotoRY.Margin = new Padding(4, 5, 4, 5);
            TxtBottomWaferPhotoRY.MinimumSize = new Size(1, 16);
            TxtBottomWaferPhotoRY.Name = "TxtBottomWaferPhotoRY";
            TxtBottomWaferPhotoRY.Padding = new Padding(5);
            TxtBottomWaferPhotoRY.ShowText = false;
            TxtBottomWaferPhotoRY.Size = new Size(95, 26);
            TxtBottomWaferPhotoRY.TabIndex = 6;
            TxtBottomWaferPhotoRY.Text = "0.000";
            TxtBottomWaferPhotoRY.TextAlignment = ContentAlignment.MiddleCenter;
            TxtBottomWaferPhotoRY.Type = UITextBox.UIEditType.Double;
            TxtBottomWaferPhotoRY.Watermark = "";
            // 
            // LalBottomWaferPhotoRY
            // 
            LalBottomWaferPhotoRY.AutoSize = true;
            LalBottomWaferPhotoRY.Location = new Point(11, 17);
            LalBottomWaferPhotoRY.Name = "LalBottomWaferPhotoRY";
            LalBottomWaferPhotoRY.Size = new Size(34, 24);
            LalBottomWaferPhotoRY.TabIndex = 7;
            LalBottomWaferPhotoRY.Text = "RY";
            // 
            // panel23
            // 
            panel23.Controls.Add(label34);
            panel23.Controls.Add(TxtBottomWaferPhotoRZ);
            panel23.Controls.Add(LalBottomWaferPhotoRZ);
            panel23.Dock = DockStyle.Fill;
            panel23.Location = new Point(873, 516);
            panel23.Name = "panel23";
            panel23.Size = new Size(168, 51);
            panel23.TabIndex = 170;
            // 
            // label34
            // 
            label34.AutoSize = true;
            label34.Location = new Point(134, 17);
            label34.Name = "label34";
            label34.Size = new Size(34, 24);
            label34.TabIndex = 9;
            label34.Text = "mm";
            // 
            // TxtBottomWaferPhotoRZ
            // 
            TxtBottomWaferPhotoRZ.ButtonSymbolOffset = new Point(0, 0);
            TxtBottomWaferPhotoRZ.DecimalPlaces = 3;
            TxtBottomWaferPhotoRZ.Enabled = false;
            TxtBottomWaferPhotoRZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtBottomWaferPhotoRZ.Location = new Point(37, 12);
            TxtBottomWaferPhotoRZ.Margin = new Padding(4, 5, 4, 5);
            TxtBottomWaferPhotoRZ.MinimumSize = new Size(1, 16);
            TxtBottomWaferPhotoRZ.Name = "TxtBottomWaferPhotoRZ";
            TxtBottomWaferPhotoRZ.Padding = new Padding(5);
            TxtBottomWaferPhotoRZ.ShowText = false;
            TxtBottomWaferPhotoRZ.Size = new Size(95, 26);
            TxtBottomWaferPhotoRZ.TabIndex = 7;
            TxtBottomWaferPhotoRZ.Text = "0.000";
            TxtBottomWaferPhotoRZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtBottomWaferPhotoRZ.Type = UITextBox.UIEditType.Double;
            TxtBottomWaferPhotoRZ.Watermark = "";
            // 
            // LalBottomWaferPhotoRZ
            // 
            LalBottomWaferPhotoRZ.AutoSize = true;
            LalBottomWaferPhotoRZ.Location = new Point(11, 17);
            LalBottomWaferPhotoRZ.Name = "LalBottomWaferPhotoRZ";
            LalBottomWaferPhotoRZ.Size = new Size(34, 24);
            LalBottomWaferPhotoRZ.TabIndex = 8;
            LalBottomWaferPhotoRZ.Text = "RZ";
            // 
            // panel39
            // 
            panel39.Dock = DockStyle.Fill;
            panel39.Location = new Point(1395, 288);
            panel39.Name = "panel39";
            panel39.Size = new Size(168, 51);
            panel39.TabIndex = 116;
            // 
            // panel24
            // 
            panel24.Controls.Add(label37);
            panel24.Controls.Add(TxtTopWaferPhotoZ);
            panel24.Controls.Add(label39);
            panel24.Location = new Point(1047, 402);
            panel24.Name = "panel24";
            panel24.Size = new Size(168, 51);
            panel24.TabIndex = 197;
            // 
            // label37
            // 
            label37.AutoSize = true;
            label37.Location = new Point(130, 17);
            label37.Name = "label37";
            label37.Size = new Size(34, 24);
            label37.TabIndex = 10;
            label37.Text = "mm";
            // 
            // TxtTopWaferPhotoZ
            // 
            TxtTopWaferPhotoZ.ButtonSymbolOffset = new Point(0, 0);
            TxtTopWaferPhotoZ.DecimalPlaces = 3;
            TxtTopWaferPhotoZ.Enabled = false;
            TxtTopWaferPhotoZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTopWaferPhotoZ.Location = new Point(33, 12);
            TxtTopWaferPhotoZ.Margin = new Padding(4, 5, 4, 5);
            TxtTopWaferPhotoZ.MinimumSize = new Size(1, 16);
            TxtTopWaferPhotoZ.Name = "TxtTopWaferPhotoZ";
            TxtTopWaferPhotoZ.Padding = new Padding(5);
            TxtTopWaferPhotoZ.ShowText = false;
            TxtTopWaferPhotoZ.Size = new Size(95, 26);
            TxtTopWaferPhotoZ.TabIndex = 8;
            TxtTopWaferPhotoZ.Text = "0.000";
            TxtTopWaferPhotoZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTopWaferPhotoZ.Type = UITextBox.UIEditType.Double;
            TxtTopWaferPhotoZ.Watermark = "";
            // 
            // label39
            // 
            label39.AutoSize = true;
            label39.Location = new Point(15, 17);
            label39.Name = "label39";
            label39.Size = new Size(22, 24);
            label39.TabIndex = 4;
            label39.Text = "Z";
            // 
            // panel56
            // 
            panel56.Controls.Add(label41);
            panel56.Controls.Add(TxtTopWaferPhotoX);
            panel56.Controls.Add(label43);
            panel56.Location = new Point(1221, 402);
            panel56.Name = "panel56";
            panel56.Size = new Size(168, 51);
            panel56.TabIndex = 198;
            // 
            // label41
            // 
            label41.AutoSize = true;
            label41.Location = new Point(130, 17);
            label41.Name = "label41";
            label41.Size = new Size(34, 24);
            label41.TabIndex = 6;
            label41.Text = "mm";
            // 
            // TxtTopWaferPhotoX
            // 
            TxtTopWaferPhotoX.ButtonSymbolOffset = new Point(0, 0);
            TxtTopWaferPhotoX.DecimalPlaces = 3;
            TxtTopWaferPhotoX.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTopWaferPhotoX.Location = new Point(33, 12);
            TxtTopWaferPhotoX.Margin = new Padding(4, 5, 4, 5);
            TxtTopWaferPhotoX.MinimumSize = new Size(1, 16);
            TxtTopWaferPhotoX.Name = "TxtTopWaferPhotoX";
            TxtTopWaferPhotoX.Padding = new Padding(5);
            TxtTopWaferPhotoX.ShowText = false;
            TxtTopWaferPhotoX.Size = new Size(95, 26);
            TxtTopWaferPhotoX.TabIndex = 5;
            TxtTopWaferPhotoX.Text = "0.000";
            TxtTopWaferPhotoX.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTopWaferPhotoX.Type = UITextBox.UIEditType.Double;
            TxtTopWaferPhotoX.Watermark = "";
            // 
            // label43
            // 
            label43.AutoSize = true;
            label43.Location = new Point(15, 17);
            label43.Name = "label43";
            label43.Size = new Size(22, 24);
            label43.TabIndex = 0;
            label43.Text = "X";
            // 
            // panel57
            // 
            panel57.Controls.Add(label44);
            panel57.Controls.Add(TxtBottomWaferPhotoX);
            panel57.Controls.Add(label45);
            panel57.Location = new Point(1221, 516);
            panel57.Name = "panel57";
            panel57.Size = new Size(168, 51);
            panel57.TabIndex = 199;
            // 
            // label44
            // 
            label44.AutoSize = true;
            label44.Location = new Point(130, 17);
            label44.Name = "label44";
            label44.Size = new Size(34, 24);
            label44.TabIndex = 6;
            label44.Text = "mm";
            // 
            // TxtBottomWaferPhotoX
            // 
            TxtBottomWaferPhotoX.ButtonSymbolOffset = new Point(0, 0);
            TxtBottomWaferPhotoX.DecimalPlaces = 3;
            TxtBottomWaferPhotoX.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtBottomWaferPhotoX.Location = new Point(33, 12);
            TxtBottomWaferPhotoX.Margin = new Padding(4, 5, 4, 5);
            TxtBottomWaferPhotoX.MinimumSize = new Size(1, 16);
            TxtBottomWaferPhotoX.Name = "TxtBottomWaferPhotoX";
            TxtBottomWaferPhotoX.Padding = new Padding(5);
            TxtBottomWaferPhotoX.ShowText = false;
            TxtBottomWaferPhotoX.Size = new Size(95, 26);
            TxtBottomWaferPhotoX.TabIndex = 5;
            TxtBottomWaferPhotoX.Text = "0.000";
            TxtBottomWaferPhotoX.TextAlignment = ContentAlignment.MiddleCenter;
            TxtBottomWaferPhotoX.Type = UITextBox.UIEditType.Double;
            TxtBottomWaferPhotoX.Watermark = "";
            // 
            // label45
            // 
            label45.AutoSize = true;
            label45.Location = new Point(15, 17);
            label45.Name = "label45";
            label45.Size = new Size(22, 24);
            label45.TabIndex = 0;
            label45.Text = "X";
            // 
            // panel58
            // 
            panel58.Controls.Add(label46);
            panel58.Controls.Add(TxtTopWaferPhotoY);
            panel58.Controls.Add(label47);
            panel58.Location = new Point(1395, 402);
            panel58.Name = "panel58";
            panel58.Size = new Size(168, 51);
            panel58.TabIndex = 200;
            // 
            // label46
            // 
            label46.AutoSize = true;
            label46.Location = new Point(130, 17);
            label46.Name = "label46";
            label46.Size = new Size(34, 24);
            label46.TabIndex = 7;
            label46.Text = "mm";
            // 
            // TxtTopWaferPhotoY
            // 
            TxtTopWaferPhotoY.ButtonSymbolOffset = new Point(0, 0);
            TxtTopWaferPhotoY.DecimalPlaces = 3;
            TxtTopWaferPhotoY.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTopWaferPhotoY.Location = new Point(33, 12);
            TxtTopWaferPhotoY.Margin = new Padding(4, 5, 4, 5);
            TxtTopWaferPhotoY.MinimumSize = new Size(1, 16);
            TxtTopWaferPhotoY.Name = "TxtTopWaferPhotoY";
            TxtTopWaferPhotoY.Padding = new Padding(5);
            TxtTopWaferPhotoY.ShowText = false;
            TxtTopWaferPhotoY.Size = new Size(95, 26);
            TxtTopWaferPhotoY.TabIndex = 5;
            TxtTopWaferPhotoY.Text = "0.000";
            TxtTopWaferPhotoY.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTopWaferPhotoY.Type = UITextBox.UIEditType.Double;
            TxtTopWaferPhotoY.Watermark = "";
            // 
            // label47
            // 
            label47.AutoSize = true;
            label47.Location = new Point(15, 17);
            label47.Name = "label47";
            label47.Size = new Size(22, 24);
            label47.TabIndex = 0;
            label47.Text = "Y";
            // 
            // panel59
            // 
            panel59.Controls.Add(label48);
            panel59.Controls.Add(TxtBottomWaferPhotoY);
            panel59.Controls.Add(label49);
            panel59.Location = new Point(1395, 516);
            panel59.Name = "panel59";
            panel59.Size = new Size(168, 51);
            panel59.TabIndex = 201;
            // 
            // label48
            // 
            label48.AutoSize = true;
            label48.Location = new Point(130, 17);
            label48.Name = "label48";
            label48.Size = new Size(34, 24);
            label48.TabIndex = 7;
            label48.Text = "mm";
            // 
            // TxtBottomWaferPhotoY
            // 
            TxtBottomWaferPhotoY.ButtonSymbolOffset = new Point(0, 0);
            TxtBottomWaferPhotoY.DecimalPlaces = 3;
            TxtBottomWaferPhotoY.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtBottomWaferPhotoY.Location = new Point(33, 12);
            TxtBottomWaferPhotoY.Margin = new Padding(4, 5, 4, 5);
            TxtBottomWaferPhotoY.MinimumSize = new Size(1, 16);
            TxtBottomWaferPhotoY.Name = "TxtBottomWaferPhotoY";
            TxtBottomWaferPhotoY.Padding = new Padding(5);
            TxtBottomWaferPhotoY.ShowText = false;
            TxtBottomWaferPhotoY.Size = new Size(95, 26);
            TxtBottomWaferPhotoY.TabIndex = 5;
            TxtBottomWaferPhotoY.Text = "0.000";
            TxtBottomWaferPhotoY.TextAlignment = ContentAlignment.MiddleCenter;
            TxtBottomWaferPhotoY.Type = UITextBox.UIEditType.Double;
            TxtBottomWaferPhotoY.Watermark = "";
            // 
            // label49
            // 
            label49.AutoSize = true;
            label49.Location = new Point(15, 17);
            label49.Name = "label49";
            label49.Size = new Size(22, 24);
            label49.TabIndex = 0;
            label49.Text = "Y";
            // 
            // panel60
            // 
            panel60.Controls.Add(label50);
            panel60.Controls.Add(TxtTopWaferPhotoR);
            panel60.Controls.Add(label51);
            panel60.Location = new Point(1569, 402);
            panel60.Name = "panel60";
            panel60.Size = new Size(168, 51);
            panel60.TabIndex = 202;
            // 
            // label50
            // 
            label50.AutoSize = true;
            label50.Location = new Point(130, 17);
            label50.Name = "label50";
            label50.Size = new Size(34, 24);
            label50.TabIndex = 7;
            label50.Text = "°";
            // 
            // TxtTopWaferPhotoR
            // 
            TxtTopWaferPhotoR.ButtonSymbolOffset = new Point(0, 0);
            TxtTopWaferPhotoR.DecimalPlaces = 3;
            TxtTopWaferPhotoR.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTopWaferPhotoR.Location = new Point(33, 12);
            TxtTopWaferPhotoR.Margin = new Padding(4, 5, 4, 5);
            TxtTopWaferPhotoR.MinimumSize = new Size(1, 16);
            TxtTopWaferPhotoR.Name = "TxtTopWaferPhotoR";
            TxtTopWaferPhotoR.Padding = new Padding(5);
            TxtTopWaferPhotoR.ShowText = false;
            TxtTopWaferPhotoR.Size = new Size(95, 26);
            TxtTopWaferPhotoR.TabIndex = 4;
            TxtTopWaferPhotoR.Text = "0.000";
            TxtTopWaferPhotoR.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTopWaferPhotoR.Type = UITextBox.UIEditType.Double;
            TxtTopWaferPhotoR.Watermark = "";
            // 
            // label51
            // 
            label51.AutoSize = true;
            label51.Location = new Point(15, 17);
            label51.Name = "label51";
            label51.Size = new Size(22, 24);
            label51.TabIndex = 0;
            label51.Text = "R";
            // 
            // panel61
            // 
            panel61.Controls.Add(label52);
            panel61.Controls.Add(TxtBottomWaferPhotoR);
            panel61.Controls.Add(label53);
            panel61.Location = new Point(1569, 516);
            panel61.Name = "panel61";
            panel61.Size = new Size(168, 51);
            panel61.TabIndex = 203;
            // 
            // label52
            // 
            label52.AutoSize = true;
            label52.Location = new Point(130, 17);
            label52.Name = "label52";
            label52.Size = new Size(34, 24);
            label52.TabIndex = 7;
            label52.Text = "°";
            // 
            // TxtBottomWaferPhotoR
            // 
            TxtBottomWaferPhotoR.ButtonSymbolOffset = new Point(0, 0);
            TxtBottomWaferPhotoR.DecimalPlaces = 3;
            TxtBottomWaferPhotoR.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtBottomWaferPhotoR.Location = new Point(33, 12);
            TxtBottomWaferPhotoR.Margin = new Padding(4, 5, 4, 5);
            TxtBottomWaferPhotoR.MinimumSize = new Size(1, 16);
            TxtBottomWaferPhotoR.Name = "TxtBottomWaferPhotoR";
            TxtBottomWaferPhotoR.Padding = new Padding(5);
            TxtBottomWaferPhotoR.ShowText = false;
            TxtBottomWaferPhotoR.Size = new Size(95, 26);
            TxtBottomWaferPhotoR.TabIndex = 4;
            TxtBottomWaferPhotoR.Text = "0.000";
            TxtBottomWaferPhotoR.TextAlignment = ContentAlignment.MiddleCenter;
            TxtBottomWaferPhotoR.Type = UITextBox.UIEditType.Double;
            TxtBottomWaferPhotoR.Watermark = "";
            // 
            // label53
            // 
            label53.AutoSize = true;
            label53.Location = new Point(15, 17);
            label53.Name = "label53";
            label53.Size = new Size(22, 24);
            label53.TabIndex = 0;
            label53.Text = "R";
            // 
            // panel55
            // 
            panel55.Controls.Add(panel62);
            panel55.Location = new Point(1395, 630);
            panel55.Name = "panel55";
            panel55.Size = new Size(168, 51);
            panel55.TabIndex = 185;
            // 
            // panel62
            // 
            panel62.Controls.Add(label58);
            panel62.Controls.Add(TxtTakeDownWaferPos);
            panel62.Location = new Point(3, 0);
            panel62.Name = "panel62";
            panel62.Size = new Size(168, 51);
            panel62.TabIndex = 208;
            // 
            // label58
            // 
            label58.AutoSize = true;
            label58.Location = new Point(130, 17);
            label58.Name = "label58";
            label58.Size = new Size(34, 24);
            label58.TabIndex = 16;
            label58.Text = "mm";
            // 
            // TxtTakeDownWaferPos
            // 
            TxtTakeDownWaferPos.ButtonSymbolOffset = new Point(0, 0);
            TxtTakeDownWaferPos.DecimalPlaces = 3;
            TxtTakeDownWaferPos.Enabled = false;
            TxtTakeDownWaferPos.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTakeDownWaferPos.Location = new Point(33, 12);
            TxtTakeDownWaferPos.Margin = new Padding(4, 5, 4, 5);
            TxtTakeDownWaferPos.MinimumSize = new Size(1, 16);
            TxtTakeDownWaferPos.Name = "TxtTakeDownWaferPos";
            TxtTakeDownWaferPos.Padding = new Padding(5);
            TxtTakeDownWaferPos.ShowText = false;
            TxtTakeDownWaferPos.Size = new Size(95, 26);
            TxtTakeDownWaferPos.TabIndex = 15;
            TxtTakeDownWaferPos.Text = "0.000";
            TxtTakeDownWaferPos.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTakeDownWaferPos.Type = UITextBox.UIEditType.Double;
            TxtTakeDownWaferPos.Watermark = "";
            // 
            // panel36
            // 
            panel36.Controls.Add(lalTakeUpWaferPos);
            panel36.Location = new Point(351, 687);
            panel36.Name = "panel36";
            panel36.Size = new Size(168, 51);
            panel36.TabIndex = 205;
            // 
            // lalTakeUpWaferPos
            // 
            lalTakeUpWaferPos.AutoSize = true;
            lalTakeUpWaferPos.Location = new Point(57, 17);
            lalTakeUpWaferPos.Name = "lalTakeUpWaferPos";
            lalTakeUpWaferPos.Size = new Size(82, 24);
            lalTakeUpWaferPos.TabIndex = 2;
            lalTakeUpWaferPos.Text = "吸附位";
            // 
            // panel37
            // 
            panel37.Controls.Add(label57);
            panel37.Controls.Add(TxtTakeUpWaferPos);
            panel37.Location = new Point(525, 687);
            panel37.Name = "panel37";
            panel37.Size = new Size(168, 51);
            panel37.TabIndex = 206;
            // 
            // label57
            // 
            label57.AutoSize = true;
            label57.Location = new Point(134, 17);
            label57.Name = "label57";
            label57.Size = new Size(34, 24);
            label57.TabIndex = 16;
            label57.Text = "mm";
            // 
            // TxtTakeUpWaferPos
            // 
            TxtTakeUpWaferPos.ButtonSymbolOffset = new Point(0, 0);
            TxtTakeUpWaferPos.DecimalPlaces = 3;
            TxtTakeUpWaferPos.Enabled = false;
            TxtTakeUpWaferPos.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTakeUpWaferPos.Location = new Point(37, 12);
            TxtTakeUpWaferPos.Margin = new Padding(4, 5, 4, 5);
            TxtTakeUpWaferPos.MinimumSize = new Size(1, 16);
            TxtTakeUpWaferPos.Name = "TxtTakeUpWaferPos";
            TxtTakeUpWaferPos.Padding = new Padding(5);
            TxtTakeUpWaferPos.ShowText = false;
            TxtTakeUpWaferPos.Size = new Size(95, 26);
            TxtTakeUpWaferPos.TabIndex = 15;
            TxtTakeUpWaferPos.Text = "0.000";
            TxtTakeUpWaferPos.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTakeUpWaferPos.Type = UITextBox.UIEditType.Double;
            TxtTakeUpWaferPos.Watermark = "";
            // 
            // panel63
            // 
            panel63.Controls.Add(lalZLevel);
            panel63.Location = new Point(3, 687);
            panel63.Name = "panel63";
            panel63.Size = new Size(168, 51);
            panel63.TabIndex = 209;
            // 
            // lalZLevel
            // 
            lalZLevel.AutoSize = true;
            lalZLevel.Location = new Point(57, 17);
            lalZLevel.Name = "lalZLevel";
            lalZLevel.Size = new Size(82, 24);
            lalZLevel.TabIndex = 2;
            lalZLevel.Text = "调平位";
            // 
            // panel65
            // 
            panel65.Controls.Add(label54);
            panel65.Controls.Add(TxtZLevel);
            panel65.Location = new Point(177, 687);
            panel65.Name = "panel65";
            panel65.Size = new Size(168, 51);
            panel65.TabIndex = 210;
            // 
            // label54
            // 
            label54.AutoSize = true;
            label54.Location = new Point(134, 17);
            label54.Name = "label54";
            label54.Size = new Size(34, 24);
            label54.TabIndex = 16;
            label54.Text = "mm";
            // 
            // TxtZLevel
            // 
            TxtZLevel.ButtonSymbolOffset = new Point(0, 0);
            TxtZLevel.DecimalPlaces = 3;
            TxtZLevel.Enabled = false;
            TxtZLevel.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtZLevel.Location = new Point(37, 12);
            TxtZLevel.Margin = new Padding(4, 5, 4, 5);
            TxtZLevel.MinimumSize = new Size(1, 16);
            TxtZLevel.Name = "TxtZLevel";
            TxtZLevel.Padding = new Padding(5);
            TxtZLevel.ShowText = false;
            TxtZLevel.Size = new Size(95, 26);
            TxtZLevel.TabIndex = 15;
            TxtZLevel.Text = "0.000";
            TxtZLevel.TextAlignment = ContentAlignment.MiddleCenter;
            TxtZLevel.Type = UITextBox.UIEditType.Double;
            TxtZLevel.Watermark = "";
            // 
            // panel35
            // 
            panel35.Controls.Add(label55);
            panel35.Controls.Add(TxtUpThick);
            panel35.Location = new Point(525, 630);
            panel35.Name = "panel35";
            panel35.Size = new Size(168, 51);
            panel35.TabIndex = 112;
            // 
            // label55
            // 
            label55.AutoSize = true;
            label55.Location = new Point(134, 17);
            label55.Name = "label55";
            label55.Size = new Size(34, 24);
            label55.TabIndex = 16;
            label55.Text = "mm";
            // 
            // TxtUpThick
            // 
            TxtUpThick.ButtonSymbolOffset = new Point(0, 0);
            TxtUpThick.DecimalPlaces = 3;
            TxtUpThick.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtUpThick.Location = new Point(37, 12);
            TxtUpThick.Margin = new Padding(4, 5, 4, 5);
            TxtUpThick.MinimumSize = new Size(1, 16);
            TxtUpThick.Name = "TxtUpThick";
            TxtUpThick.Padding = new Padding(5);
            TxtUpThick.ShowText = false;
            TxtUpThick.Size = new Size(95, 26);
            TxtUpThick.TabIndex = 15;
            TxtUpThick.Text = "0.000";
            TxtUpThick.TextAlignment = ContentAlignment.MiddleCenter;
            TxtUpThick.Type = UITextBox.UIEditType.Double;
            TxtUpThick.Watermark = "";
            // 
            // panel68
            // 
            panel68.Controls.Add(LalUpThick);
            panel68.Location = new Point(351, 630);
            panel68.Name = "panel68";
            panel68.Size = new Size(168, 51);
            panel68.TabIndex = 211;
            // 
            // LalUpThick
            // 
            LalUpThick.AutoSize = true;
            LalUpThick.Location = new Point(41, 17);
            LalUpThick.Name = "LalUpThick";
            LalUpThick.Size = new Size(130, 24);
            LalUpThick.TabIndex = 2;
            LalUpThick.Text = "晶圆片厚度";
            // 
            // panel53
            // 
            panel53.Controls.Add(lalTakeDownWaferPos);
            panel53.Location = new Point(1221, 630);
            panel53.Name = "panel53";
            panel53.Size = new Size(168, 51);
            panel53.TabIndex = 207;
            // 
            // lalTakeDownWaferPos
            // 
            lalTakeDownWaferPos.AutoSize = true;
            lalTakeDownWaferPos.Location = new Point(57, 17);
            lalTakeDownWaferPos.Name = "lalTakeDownWaferPos";
            lalTakeDownWaferPos.Size = new Size(82, 24);
            lalTakeDownWaferPos.TabIndex = 2;
            lalTakeDownWaferPos.Text = "贴片位";
            // 
            // panel38
            // 
            panel38.Controls.Add(label56);
            panel38.Controls.Add(TxtBottomThick);
            panel38.Location = new Point(1047, 630);
            panel38.Name = "panel38";
            panel38.Size = new Size(168, 51);
            panel38.TabIndex = 115;
            // 
            // label56
            // 
            label56.AutoSize = true;
            label56.Location = new Point(134, 17);
            label56.Name = "label56";
            label56.Size = new Size(34, 24);
            label56.TabIndex = 19;
            label56.Text = "mm";
            // 
            // TxtBottomThick
            // 
            TxtBottomThick.ButtonSymbolOffset = new Point(0, 0);
            TxtBottomThick.DecimalPlaces = 3;
            TxtBottomThick.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtBottomThick.Location = new Point(37, 12);
            TxtBottomThick.Margin = new Padding(4, 5, 4, 5);
            TxtBottomThick.MinimumSize = new Size(1, 16);
            TxtBottomThick.Name = "TxtBottomThick";
            TxtBottomThick.Padding = new Padding(5);
            TxtBottomThick.ShowText = false;
            TxtBottomThick.Size = new Size(95, 26);
            TxtBottomThick.TabIndex = 18;
            TxtBottomThick.Text = "0.000";
            TxtBottomThick.TextAlignment = ContentAlignment.MiddleCenter;
            TxtBottomThick.Type = UITextBox.UIEditType.Double;
            TxtBottomThick.Watermark = "";
            // 
            // panel40
            // 
            panel40.Controls.Add(label27);
            panel40.Location = new Point(873, 630);
            panel40.Name = "panel40";
            panel40.Size = new Size(168, 51);
            panel40.TabIndex = 117;
            // 
            // label27
            // 
            label27.AutoSize = true;
            label27.Location = new Point(41, 17);
            label27.Name = "label27";
            label27.Size = new Size(130, 24);
            label27.TabIndex = 3;
            label27.Text = "晶圆片厚度";
            // 
            // panel27
            // 
            panel27.Controls.Add(LalSpacer);
            panel27.Location = new Point(351, 174);
            panel27.Name = "panel27";
            panel27.Size = new Size(168, 51);
            panel27.TabIndex = 191;
            // 
            // LalSpacer
            // 
            LalSpacer.AutoSize = true;
            LalSpacer.Location = new Point(49, 17);
            LalSpacer.Name = "LalSpacer";
            LalSpacer.Size = new Size(106, 24);
            LalSpacer.TabIndex = 3;
            LalSpacer.Text = "隔片厚度";
            // 
            // panel66
            // 
            panel66.Controls.Add(label31);
            panel66.Controls.Add(TxtSpacerThick);
            panel66.Location = new Point(525, 174);
            panel66.Name = "panel66";
            panel66.Size = new Size(168, 51);
            panel66.TabIndex = 194;
            // 
            // label31
            // 
            label31.AutoSize = true;
            label31.Location = new Point(134, 17);
            label31.Name = "label31";
            label31.Size = new Size(34, 24);
            label31.TabIndex = 15;
            label31.Text = "mm";
            // 
            // TxtSpacerThick
            // 
            TxtSpacerThick.ButtonSymbolOffset = new Point(0, 0);
            TxtSpacerThick.DecimalPlaces = 3;
            TxtSpacerThick.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtSpacerThick.Location = new Point(37, 12);
            TxtSpacerThick.Margin = new Padding(4, 5, 4, 5);
            TxtSpacerThick.MinimumSize = new Size(1, 16);
            TxtSpacerThick.Name = "TxtSpacerThick";
            TxtSpacerThick.Padding = new Padding(5);
            TxtSpacerThick.ShowText = false;
            TxtSpacerThick.Size = new Size(95, 26);
            TxtSpacerThick.TabIndex = 7;
            TxtSpacerThick.Text = "0.000";
            TxtSpacerThick.TextAlignment = ContentAlignment.MiddleCenter;
            TxtSpacerThick.Type = UITextBox.UIEditType.Double;
            TxtSpacerThick.Watermark = "";
            // 
            // panel30
            // 
            panel30.Controls.Add(lalMaterial);
            panel30.Location = new Point(873, 687);
            panel30.Name = "panel30";
            panel30.Size = new Size(168, 51);
            panel30.TabIndex = 204;
            // 
            // lalMaterial
            // 
            lalMaterial.AutoSize = true;
            lalMaterial.Location = new Point(49, 17);
            lalMaterial.Name = "lalMaterial";
            lalMaterial.Size = new Size(106, 24);
            lalMaterial.TabIndex = 2;
            lalMaterial.Text = "产品材质";
            // 
            // panel50
            // 
            panel50.Controls.Add(cmbMaterial);
            panel50.Location = new Point(1047, 687);
            panel50.Name = "panel50";
            panel50.Size = new Size(168, 51);
            panel50.TabIndex = 183;
            // 
            // cmbMaterial
            // 
            cmbMaterial.FormattingEnabled = true;
            cmbMaterial.Items.AddRange(new object[] { "硅", "玻璃" });
            cmbMaterial.Location = new Point(37, 13);
            cmbMaterial.Name = "cmbMaterial";
            cmbMaterial.Size = new Size(95, 32);
            cmbMaterial.TabIndex = 1;
            // 
            // panel69
            // 
            panel69.Controls.Add(label59);
            panel69.Location = new Point(1395, 174);
            panel69.Name = "panel69";
            panel69.Size = new Size(168, 51);
            panel69.TabIndex = 212;
            // 
            // label59
            // 
            label59.AutoSize = true;
            label59.Location = new Point(49, 17);
            label59.Name = "label59";
            label59.Size = new Size(106, 24);
            label59.TabIndex = 2;
            label59.Text = "视觉作业";
            // 
            // panel72
            // 
            panel72.Controls.Add(DUDVisual);
            panel72.Location = new Point(1569, 174);
            panel72.Name = "panel72";
            panel72.Size = new Size(168, 51);
            panel72.TabIndex = 213;
            // 
            // DUDVisual
            // 
            DUDVisual.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            DUDVisual.Location = new Point(26, 11);
            DUDVisual.Margin = new Padding(4, 5, 4, 5);
            DUDVisual.Maximum = 300D;
            DUDVisual.Minimum = 0D;
            DUDVisual.MinimumSize = new Size(100, 0);
            DUDVisual.Name = "DUDVisual";
            DUDVisual.ShowText = false;
            DUDVisual.Size = new Size(116, 29);
            DUDVisual.Step = 1D;
            DUDVisual.TabIndex = 0;
            DUDVisual.Text = "uiDoubleUpDown1";
            DUDVisual.TextAlignment = ContentAlignment.MiddleCenter;
            // 
            // panel31
            // 
            panel31.Controls.Add(LalMarkDistance);
            panel31.Location = new Point(1047, 174);
            panel31.Name = "panel31";
            panel31.Size = new Size(168, 51);
            panel31.TabIndex = 195;
            // 
            // LalMarkDistance
            // 
            LalMarkDistance.AutoSize = true;
            LalMarkDistance.Location = new Point(49, 17);
            LalMarkDistance.Name = "LalMarkDistance";
            LalMarkDistance.Size = new Size(106, 24);
            LalMarkDistance.TabIndex = 2;
            LalMarkDistance.Text = "Mark距离";
            // 
            // panel32
            // 
            panel32.Controls.Add(label35);
            panel32.Controls.Add(TxtMarkDistance);
            panel32.Location = new Point(1221, 174);
            panel32.Name = "panel32";
            panel32.Size = new Size(168, 51);
            panel32.TabIndex = 196;
            // 
            // label35
            // 
            label35.AutoSize = true;
            label35.Location = new Point(134, 17);
            label35.Name = "label35";
            label35.Size = new Size(34, 24);
            label35.TabIndex = 15;
            label35.Text = "mm";
            // 
            // TxtMarkDistance
            // 
            TxtMarkDistance.ButtonSymbolOffset = new Point(0, 0);
            TxtMarkDistance.DecimalPlaces = 3;
            TxtMarkDistance.DoubleValue = 124D;
            TxtMarkDistance.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtMarkDistance.Location = new Point(37, 12);
            TxtMarkDistance.Margin = new Padding(4, 5, 4, 5);
            TxtMarkDistance.MinimumSize = new Size(1, 16);
            TxtMarkDistance.Name = "TxtMarkDistance";
            TxtMarkDistance.Padding = new Padding(5);
            TxtMarkDistance.ShowText = false;
            TxtMarkDistance.Size = new Size(95, 26);
            TxtMarkDistance.TabIndex = 7;
            TxtMarkDistance.Text = "124.000";
            TxtMarkDistance.TextAlignment = ContentAlignment.MiddleCenter;
            TxtMarkDistance.Type = UITextBox.UIEditType.Double;
            TxtMarkDistance.Watermark = "";
            // 
            // panel3
            // 
            panel3.Controls.Add(cmbSize);
            panel3.Location = new Point(873, 174);
            panel3.Name = "panel3";
            panel3.Size = new Size(168, 51);
            panel3.TabIndex = 190;
            // 
            // cmbSize
            // 
            cmbSize.FormattingEnabled = true;
            cmbSize.Items.AddRange(new object[] { "6寸", "8寸" });
            cmbSize.Location = new Point(37, 13);
            cmbSize.Name = "cmbSize";
            cmbSize.Size = new Size(95, 32);
            cmbSize.TabIndex = 0;
            // 
            // panel11
            // 
            panel11.Controls.Add(LalSize);
            panel11.Location = new Point(699, 174);
            panel11.Name = "panel11";
            panel11.Size = new Size(168, 51);
            panel11.TabIndex = 189;
            // 
            // LalSize
            // 
            LalSize.AutoSize = true;
            LalSize.Location = new Point(49, 17);
            LalSize.Name = "LalSize";
            LalSize.Size = new Size(106, 24);
            LalSize.TabIndex = 0;
            LalSize.Text = "产品尺寸";
            // 
            // panel54
            // 
            panel54.Controls.Add(BtnOpenCalFile);
            panel54.Location = new Point(1221, 744);
            panel54.Name = "panel54";
            panel54.Size = new Size(168, 51);
            panel54.TabIndex = 184;
            // 
            // BtnOpenCalFile
            // 
            BtnOpenCalFile.Location = new Point(19, 6);
            BtnOpenCalFile.Name = "BtnOpenCalFile";
            BtnOpenCalFile.Size = new Size(131, 39);
            BtnOpenCalFile.TabIndex = 144;
            BtnOpenCalFile.Text = "打开标定文件";
            BtnOpenCalFile.UseVisualStyleBackColor = true;
            BtnOpenCalFile.Click += BtnOpenCalFile_Click;
            // 
            // panel5
            // 
            panel5.Controls.Add(BtnSave);
            panel5.Location = new Point(1395, 744);
            panel5.Name = "panel5";
            panel5.Size = new Size(168, 51);
            panel5.TabIndex = 173;
            // 
            // BtnSave
            // 
            BtnSave.Location = new Point(19, 6);
            BtnSave.Name = "BtnSave";
            BtnSave.Size = new Size(131, 39);
            BtnSave.TabIndex = 143;
            BtnSave.Text = "保存对准文件";
            BtnSave.UseVisualStyleBackColor = true;
            BtnSave.Click += BtnSave_Click;
            // 
            // panel22
            // 
            panel22.Controls.Add(BtnOpen);
            panel22.Location = new Point(1569, 744);
            panel22.Name = "panel22";
            panel22.Size = new Size(168, 51);
            panel22.TabIndex = 91;
            // 
            // BtnOpen
            // 
            BtnOpen.Location = new Point(19, 6);
            BtnOpen.Name = "BtnOpen";
            BtnOpen.Size = new Size(131, 39);
            BtnOpen.TabIndex = 144;
            BtnOpen.Text = "打开对准文件";
            BtnOpen.UseVisualStyleBackColor = true;
            BtnOpen.Click += BtnOpen_Click;
            // 
            // AlignPara
            // 
            AlignPara.Controls.Add(tableLayoutPanel1);
            AlignPara.FillColor = Color.FromArgb(248, 248, 248);
            AlignPara.FillColor2 = Color.FromArgb(248, 248, 248);
            AlignPara.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            AlignPara.Location = new Point(37, 57);
            AlignPara.Margin = new Padding(4, 5, 4, 5);
            AlignPara.MinimumSize = new Size(1, 1);
            AlignPara.Name = "AlignPara";
            AlignPara.RectColor = Color.FromArgb(140, 140, 140);
            AlignPara.Size = new Size(1827, 821);
            AlignPara.Style = UIStyle.Custom;
            AlignPara.TabIndex = 84;
            AlignPara.Text = null;
            AlignPara.TextAlignment = ContentAlignment.MiddleCenter;
            // 
            // FTitlePage2
            // 
            AllowShowTitle = true;
            AutoScaleMode = AutoScaleMode.None;
            BackColor = Color.FromArgb(248, 248, 248);
            ClientSize = new Size(1900, 895);
            ControlBoxFillHoverColor = Color.FromArgb(163, 163, 163);
            Controls.Add(AlignPara);
            Name = "FTitlePage2";
            Padding = new Padding(0, 35, 0, 0);
            PageIndex = 1002;
            RectColor = Color.FromArgb(140, 140, 140);
            ShowTitle = true;
            Style = UIStyle.Custom;
            Text = "对准参数";
            tableLayoutPanel1.ResumeLayout(false);
            panel9.ResumeLayout(false);
            panel2.ResumeLayout(false);
            panel2.PerformLayout();
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            panel47.ResumeLayout(false);
            panel47.PerformLayout();
            panel29.ResumeLayout(false);
            panel29.PerformLayout();
            panel28.ResumeLayout(false);
            panel28.PerformLayout();
            panel26.ResumeLayout(false);
            panel26.PerformLayout();
            panel21.ResumeLayout(false);
            panel21.PerformLayout();
            panel20.ResumeLayout(false);
            panel20.PerformLayout();
            panel18.ResumeLayout(false);
            panel18.PerformLayout();
            panel13.ResumeLayout(false);
            panel13.PerformLayout();
            panel67.ResumeLayout(false);
            panel67.PerformLayout();
            panel15.ResumeLayout(false);
            panel15.PerformLayout();
            panel4.ResumeLayout(false);
            panel4.PerformLayout();
            panel8.ResumeLayout(false);
            panel8.PerformLayout();
            panel16.ResumeLayout(false);
            panel19.ResumeLayout(false);
            panel33.ResumeLayout(false);
            panel33.PerformLayout();
            panel48.ResumeLayout(false);
            panel48.PerformLayout();
            panel34.ResumeLayout(false);
            panel34.PerformLayout();
            panel49.ResumeLayout(false);
            panel49.PerformLayout();
            panel41.ResumeLayout(false);
            panel41.PerformLayout();
            panel42.ResumeLayout(false);
            panel42.PerformLayout();
            panel43.ResumeLayout(false);
            panel43.PerformLayout();
            panel44.ResumeLayout(false);
            panel44.PerformLayout();
            panel45.ResumeLayout(false);
            panel45.PerformLayout();
            panel46.ResumeLayout(false);
            panel46.PerformLayout();
            panel64.ResumeLayout(false);
            panel64.PerformLayout();
            panel10.ResumeLayout(false);
            panel10.PerformLayout();
            panel14.ResumeLayout(false);
            panel14.PerformLayout();
            panel51.ResumeLayout(false);
            panel51.PerformLayout();
            panel52.ResumeLayout(false);
            panel52.PerformLayout();
            panel25.ResumeLayout(false);
            panel25.PerformLayout();
            panel70.ResumeLayout(false);
            panel70.PerformLayout();
            panel6.ResumeLayout(false);
            panel6.PerformLayout();
            panel7.ResumeLayout(false);
            panel7.PerformLayout();
            panel71.ResumeLayout(false);
            panel71.PerformLayout();
            panel12.ResumeLayout(false);
            panel12.PerformLayout();
            panel17.ResumeLayout(false);
            panel17.PerformLayout();
            panel23.ResumeLayout(false);
            panel23.PerformLayout();
            panel24.ResumeLayout(false);
            panel24.PerformLayout();
            panel56.ResumeLayout(false);
            panel56.PerformLayout();
            panel57.ResumeLayout(false);
            panel57.PerformLayout();
            panel58.ResumeLayout(false);
            panel58.PerformLayout();
            panel59.ResumeLayout(false);
            panel59.PerformLayout();
            panel60.ResumeLayout(false);
            panel60.PerformLayout();
            panel61.ResumeLayout(false);
            panel61.PerformLayout();
            panel55.ResumeLayout(false);
            panel62.ResumeLayout(false);
            panel62.PerformLayout();
            panel36.ResumeLayout(false);
            panel36.PerformLayout();
            panel37.ResumeLayout(false);
            panel37.PerformLayout();
            panel63.ResumeLayout(false);
            panel63.PerformLayout();
            panel65.ResumeLayout(false);
            panel65.PerformLayout();
            panel35.ResumeLayout(false);
            panel35.PerformLayout();
            panel68.ResumeLayout(false);
            panel68.PerformLayout();
            panel53.ResumeLayout(false);
            panel53.PerformLayout();
            panel38.ResumeLayout(false);
            panel38.PerformLayout();
            panel40.ResumeLayout(false);
            panel40.PerformLayout();
            panel27.ResumeLayout(false);
            panel27.PerformLayout();
            panel66.ResumeLayout(false);
            panel66.PerformLayout();
            panel30.ResumeLayout(false);
            panel30.PerformLayout();
            panel50.ResumeLayout(false);
            panel69.ResumeLayout(false);
            panel69.PerformLayout();
            panel72.ResumeLayout(false);
            panel31.ResumeLayout(false);
            panel31.PerformLayout();
            panel32.ResumeLayout(false);
            panel32.PerformLayout();
            panel3.ResumeLayout(false);
            panel11.ResumeLayout(false);
            panel11.PerformLayout();
            panel54.ResumeLayout(false);
            panel5.ResumeLayout(false);
            panel22.ResumeLayout(false);
            AlignPara.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        private Panel panel15;
        private WaferAligner.ULine uLine4;
        private Panel panel4;
        private WaferAligner.ULine uLine2;
        private Panel panel14;

        private Panel panel8;
        private WaferAligner.ULine uLine3;
        private Panel panel16;
        private WaferAligner.ULine uLine5;
        private Panel panel19;
        private Panel panel22;
        private WaferAligner.ULine uLine6;
        private Panel panel25;
        private Label LalBottomWaferPhotoZ;

        private Label LalTopWaferPhotoLZ;
        private Label LalTopWaferPhotoRX;
        private Label LalTopWaferPhotoRZ;
        private Panel panel35;
        private Panel panel38;
        private Panel panel39;
        private Panel panel40;
        private Panel panel33;
        private Panel panel48;


        private Label LalCurX;
        private Panel panel34;

        private Label LalCurY;
        private Panel panel49;

        private Label LalCurR;
        private Panel panel41;
        private Panel panel42;

        private Label LalCurLX;
        private Panel panel43;

        private Label LalCurLY;
        private Panel panel44;
        private Label LalCurLZ;
        private Panel panel45;
        private Label LalCurRX;
        private Panel panel46;
        private Label LalCurRZ;
        private Panel panel51;
        private Panel panel52;

        private WaferAligner.ULine uLine7;
        private WaferAligner.ULine uLine1;
        private WaferAligner.ULine uLine8;
        private UITextBox TxtCurR;
        private Label LalCurZ;
        private UITextBox TxtCurZ;
        private UITextBox TxtCurLX;
        private UITextBox TxtCurLY;
        private UITextBox TxtCurLZ;
        private UITextBox TxtCurRX;
        private UITextBox TxtCurRZ;
        private UITextBox TxtCurX;
        private UITextBox TxtCurY;
        private Panel panel64;
        private UITextBox TxtCurRY;
        private Label LalCurRY;
        private Label label1;
        private Label label2;
        private Label label4;
        private Label label5;
        private Label label6;
        private Label label7;
        private Label label8;
        private Label label9;
        private Label label11;
        private Label label10;
        private Label lalTextName;
        private Button button1;
        private Label label15;
        private Button button12;
        private Label label12;
        private Button button6;
        private Label label13;
        private Button button8;
        private Panel panel67;
        private Label LalTopWaferPhotoLY;
        private Label LalTopWaferPhotoRY;
        private Panel panel70;
        private UITextBox TxtTopWaferPhotoLY;
        private UITextBox TxtTopWaferPhotoRY;
        private UITextBox TxtTopWaferPhotoRX;
        private UITextBox TxtTopWaferPhotoLZ;
        private UITextBox TxtTopWaferPhotoRZ;
        private Label label18;
        private Label label20;
        private Label label19;
        private Label label22;
        private Label label21;
        private UIPanel AlignPara;
        private Panel panel10;
        private Label label23;
        private Label LalTopWaferPhotoLX;
        private UITextBox TxtTopWaferPhotoLX;
        private Panel panel6;
        private UITextBox TxtBottomWaferPhotoLX;
        private Label label24;
        private Label LalBottomWaferPhotoLX;
        private Panel panel7;
        private Label label26;
        private UITextBox TxtBottomWaferPhotoLY;
        private Label LalBottomWaferPhotoLY;
        private Panel panel71;
        private Label label28;
        private UITextBox TxtBottomWaferPhotoLZ;
        private Label LalBottomWaferPhotoLZ;
        private Panel panel12;
        private Label label30;
        private UITextBox TxtBottomWaferPhotoRX;
        private Label LalBottomWaferPhotoRX;
        private Panel panel17;
        private Label label32;
        private UITextBox TxtBottomWaferPhotoRY;
        private Label LalBottomWaferPhotoRY;
        private Panel panel23;
        private Label label34;
        private Label LalBottomWaferPhotoRZ;
        private UITextBox TxtBottomWaferPhotoRZ;
        private UICheckBox ChkTopHorizontalAdjust;
        private Panel panel53;
        private UITextBox TxtBottomWaferPhotoZ;
        private Label label25;
        private Button BtnOpen;
        private Panel panel5;
        private Button BtnSave;
        private Label label14;
        private Button button10;
        private Panel panel47;
        private Label label42;
        private UITextBox TxtCalZ;
        private Label LalCalZ;
        private Panel panel29;
        private Label label40;
        private UITextBox TxtCalRZ;
        private Label LalCalRZ;
        private Panel panel28;
        private Label label38;
        private UITextBox TxtCalRY;
        private Label LalCalRY;
        private Panel panel26;
        private Label label36;
        private UITextBox TxtCalRX;
        private Label LalCalRX;
        private Panel panel21;
        private Label label33;
        private UITextBox TxtCalLZ;
        private Label LalCalLZ;
        private Panel panel20;
        private Label label29;
        private UITextBox TxtCalLY;
        private Label LalCalLY;
        private Panel panel18;
        private UITextBox TxtCalLX;
        private Label label17;
        private Label LalCalLX;
        private Panel panel13;
        private Label label16;
        private Button button2;
        private Panel panel50;
        private Panel panel54;
        private Button BtnOpenCalFile;
        private Panel panel32;
        private UITextBox TxtMarkDistance;
        private Panel panel31;
        private Label LalMarkDistance;
        private Panel panel66;
        private UITextBox TxtSpacerThick;
        private Panel panel27;
        private Panel panel3;
        private ComboBox cmbSize;
        private Panel panel11;
        private Label LalSize;
        private Panel panel9;
        private UITextBox TxtName;
        private Panel panel2;
        private Label LalName;
        private Panel panel1;
        private Label label3;
        private Button button4;
        private Panel panel55;
        private Label label35;
        private Label label31;
        private Panel panel24;
        private Label label37;
        private UITextBox TxtTopWaferPhotoZ;
        private Label label39;
        private Panel panel56;
        private Label label41;
        private UITextBox TxtTopWaferPhotoX;
        private Label label43;
        private Panel panel57;
        private Label label44;
        private UITextBox TxtBottomWaferPhotoX;
        private Label label45;
        private Panel panel58;
        private Label label46;
        private UITextBox TxtTopWaferPhotoY;
        private Label label47;
        private Panel panel59;
        private Label label48;
        private UITextBox TxtBottomWaferPhotoY;
        private Label label49;
        private Panel panel60;
        private Label label50;
        private UITextBox TxtTopWaferPhotoR;
        private Label label51;
        private Panel panel61;
        private Label label52;
        private UITextBox TxtBottomWaferPhotoR;
        private Label label53;
        private UICheckBox ChkTopHorizontalPhoto;
        private Label label55;
        private UITextBox TxtUpThick;
        private Label LalUpThick;
        private Label label56;
        private UITextBox TxtBottomThick;
        private Label LalSpacer;
        private Panel panel30;
        private Label lalMaterial;
        private ComboBox cmbMaterial;
        private Panel panel36;
        private Label lalTakeUpWaferPos;
        private Panel panel37;
        private Label label57;
        private UITextBox TxtTakeUpWaferPos;
        private Label lalTakeDownWaferPos;
        private Panel panel62;
        private Label label58;
        private UITextBox TxtTakeDownWaferPos;
        private Panel panel63;
        private Label lalZLevel;
        private Panel panel65;
        private Label label54;
        private UITextBox TxtZLevel;
        private Panel panel68;
        private Label label27;
        private Panel panel72;
        private Panel panel69;
        private Label label59;
        private UIDoubleUpDown DUDVisual;
        internal TableLayoutPanel tableLayoutPanel1;
    }
}