using System;

namespace WaferAligner.Services
{
    /// <summary>
    /// 状态更新服务接口，用于替代静态访问模式
    /// </summary>
    public interface IStatusUpdateService
    {
        /// <summary>
        /// 更新状态文本
        /// </summary>
        /// <param name="status">状态文本</param>
        void UpdateStatus(string status);
        
        /// <summary>
        /// 获取当前状态文本
        /// </summary>
        /// <returns>当前状态文本</returns>
        string GetCurrentStatus();
        
        /// <summary>
        /// 注册状态更新事件
        /// </summary>
        /// <param name="handler">状态更新事件处理器</param>
        void RegisterStatusUpdateHandler(Action<string> handler);
        
        /// <summary>
        /// 注销状态更新事件
        /// </summary>
        /// <param name="handler">状态更新事件处理器</param>
        void UnregisterStatusUpdateHandler(Action<string> handler);
    }
} 