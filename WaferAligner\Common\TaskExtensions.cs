using System;
using System.Threading;
using System.Threading.Tasks;

namespace WaferAligner.Common
{
    /// <summary>
    /// Task相关扩展方法
    /// </summary>
    public static class TaskExtensions
    {
        /// <summary>
        /// 为任务添加超时处理
        /// </summary>
        /// <typeparam name="T">任务结果类型</typeparam>
        /// <param name="task">要添加超时的任务</param>
        /// <param name="timeoutMilliseconds">超时时间（毫秒）</param>
        /// <returns>原始任务的结果，如果超时则抛出TimeoutException</returns>
        public static async Task<T> WithTimeout<T>(this Task<T> task, int timeoutMilliseconds)
        {
            var timeoutTask = Task.Delay(timeoutMilliseconds);
            var completedTask = await Task.WhenAny(task, timeoutTask);
            
            if (completedTask == timeoutTask)
            {
                throw new TimeoutException($"操作超时（{timeoutMilliseconds}毫秒）");
            }
            
            return await task; // 这里会传播任务的异常
        }

        /// <summary>
        /// 为无返回值任务添加超时处理
        /// </summary>
        /// <param name="task">要添加超时的任务</param>
        /// <param name="timeoutMilliseconds">超时时间（毫秒）</param>
        public static async Task WithTimeout(this Task task, int timeoutMilliseconds)
        {
            var timeoutTask = Task.Delay(timeoutMilliseconds);
            var completedTask = await Task.WhenAny(task, timeoutTask);
            
            if (completedTask == timeoutTask)
            {
                throw new TimeoutException($"操作超时（{timeoutMilliseconds}毫秒）");
            }
            
            await task; // 这里会传播任务的异常
        }
    }
} 