namespace WaferAligner.Services
{
    /// <summary>
    /// 气缸相关常量定义
    /// </summary>
    public static class CylinderConstants
    {
        /// <summary>
        /// 气缸类型定义
        /// </summary>
        public static class CylinderTypes
        {
            /// <summary>
            /// 顶部晶圆气缸
            /// </summary>
            public const string TOP_WAFER = "TOPWAFER";
            
            /// <summary>
            /// 托盘晶圆内气缸
            /// </summary>
            public const string TRAY_WAFER_INNER = "TRAYWAFERINNER";
            
            /// <summary>
            /// 托盘晶圆外气缸
            /// </summary>
            public const string TRAY_WAFER_OUTER = "TRAYWAFERUTER";
            
            /// <summary>
            /// 托盘气缸
            /// </summary>
            public const string TRAY = "TRAY";
            
            /// <summary>
            /// 卡盘锁气缸
            /// </summary>
            public const string CHUCK_LOCK = "CHUCKLOCK";
            
            /// <summary>
            /// 水平调节气缸
            /// </summary>
            public const string HORIZONTAL_ADJUST = "HORIZONTALADJUST";
        }
        
        /// <summary>
        /// 气缸状态定义
        /// </summary>
        public static class CylinderStates
        {
            /// <summary>
            /// 关闭状态
            /// </summary>
            public const int CLOSED = 0;
            
            /// <summary>
            /// 打开状态
            /// </summary>
            public const int OPENED = 1;
            
            /// <summary>
            /// 未知状态
            /// </summary>
            public const int UNKNOWN = 2;
        }
        
        /// <summary>
        /// 气缸操作相关常量
        /// </summary>
        public static class CylinderOperations
        {
            /// <summary>
            /// 气缸操作默认超时时间(毫秒)
            /// </summary>
            public const int DEFAULT_TIMEOUT_MS = 5000;
            
            /// <summary>
            /// 气缸状态检查间隔(毫秒)
            /// </summary>
            public const int STATE_CHECK_INTERVAL_MS = 100;
        }
    }
} 