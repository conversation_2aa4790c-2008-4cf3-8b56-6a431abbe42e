@echo off
chcp 65001 >nul
echo ========================================
echo    WaferAligner 环境配置切换工具
echo ========================================
echo.

REM 显示当前配置
echo 当前配置状态:
echo ----------------

REM 检查App.config中的配置
findstr "DevelopmentMode" App.config >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=2 delims==" %%a in ('findstr "DevelopmentMode" App.config ^| findstr "value"') do (
        set current_config=%%a
        set current_config=!current_config:"=!
        set current_config=!current_config: =!
        set current_config=!current_config:/>=!
    )
) else (
    set current_config=未找到
)

REM 检查环境变量
if defined WAFER_ALIGNER_DEV_MODE (
    set env_config=%WAFER_ALIGNER_DEV_MODE%
) else (
    set env_config=未设置
)

echo App.config配置: %current_config%
echo 环境变量配置: %env_config%
echo.

REM 显示菜单
echo 请选择要切换的环境:
echo [1] 开发环境 (跳过硬件连接，界面流畅)
echo [2] 测试环境 (跳过硬件连接，用于测试)
echo [3] 生产环境 (连接真实硬件)
echo [4] 显示当前有效配置
echo [5] 退出
echo.

set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto dev_mode
if "%choice%"=="2" goto test_mode
if "%choice%"=="3" goto prod_mode
if "%choice%"=="4" goto show_config
if "%choice%"=="5" goto exit
echo 无效选择，请重新运行脚本
pause
goto exit

:dev_mode
echo.
echo 正在切换到开发环境...
set WAFER_ALIGNER_DEV_MODE=true
setx WAFER_ALIGNER_DEV_MODE true >nul 2>&1
echo ✓ 环境变量已设置: WAFER_ALIGNER_DEV_MODE=true
echo ✓ 开发环境配置完成
echo.
echo 特性: 跳过所有硬件连接，界面响应流畅
goto restart_prompt

:test_mode
echo.
echo 正在切换到测试环境...
set WAFER_ALIGNER_DEV_MODE=true
setx WAFER_ALIGNER_DEV_MODE true >nul 2>&1
echo ✓ 环境变量已设置: WAFER_ALIGNER_DEV_MODE=true
echo ✓ 测试环境配置完成
echo.
echo 特性: 跳过硬件连接，适用于功能测试
goto restart_prompt

:prod_mode
echo.
echo 正在切换到生产环境...
set WAFER_ALIGNER_DEV_MODE=
setx WAFER_ALIGNER_DEV_MODE "" >nul 2>&1
echo ✓ 环境变量已清除
echo ✓ 生产环境配置完成
echo.
echo 注意: 需要确保以下硬件已连接:
echo - 汇川PLC设备 (IP: ************, ************)
echo - 串口设备 (默认: COM1, 115200波特率)
goto restart_prompt

:show_config
echo.
echo 当前有效配置:
echo ================
if defined WAFER_ALIGNER_DEV_MODE (
    if "%WAFER_ALIGNER_DEV_MODE%"=="true" (
        echo 模式: 开发/测试模式
        echo 来源: 环境变量
        echo 硬件连接: 跳过
    ) else (
        echo 模式: 生产模式
        echo 来源: 环境变量覆盖
        echo 硬件连接: 启用
    )
) else (
    echo 模式: 生产模式 ^(默认^)
    echo 来源: App.config
    echo 硬件连接: 启用
)
echo.
pause
goto exit

:restart_prompt
echo.
echo 配置已更新，建议重启应用程序以使配置生效
set /p restart="是否现在启动WaferAligner? (Y/N): "
if /i "%restart%"=="Y" (
    echo 正在启动程序...
    start "" "WaferAligner.exe"
)

:exit
echo.
echo 感谢使用环境配置切换工具
pause
