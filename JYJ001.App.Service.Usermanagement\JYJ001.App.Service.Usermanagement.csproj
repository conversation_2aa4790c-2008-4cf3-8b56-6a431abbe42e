﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Services\Service.Common\JYJ001.App.Service.Common.Interface\JYJ001.App.Service.Common.Interface.csproj" />
    <ProjectReference Include="..\Services\Service.Common\JYJ001.App.Service.Common\JYJ001.App.Service.Common.csproj" />
    <ProjectReference Include="..\WaferAligner.EventIds\WaferAligner.EventIds.csproj" />
  </ItemGroup>

</Project>
