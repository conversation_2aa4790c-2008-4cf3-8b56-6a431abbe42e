﻿using System;
using System.Threading;
using System.Threading.Tasks;

namespace WaferAligner.SerialControl.Interfaces
{
    /// <summary>
    /// SerialCom.dll封装接口
    /// </summary>
    public interface ISerialComWrapper : IDisposable
    {
        /// <summary>
        /// 获取DLL版本
        /// </summary>
        int GetVersion();

        /// <summary>
        /// 打开控制器设备
        /// </summary>
        /// <param name="controlNum">控制器编号</param>
        int OpenDevice(int controlNum);

        /// <summary>
        /// 打开串口
        /// </summary>
        /// <param name="comPort">串口号</param>
        /// <param name="baudRate">波特率</param>
        int OpenComPort(int comPort, int baudRate);

        /// <summary>
        /// 关闭串口
        /// </summary>
        int CloseComPort();

        /// <summary>
        /// 设置控制轴
        /// </summary>
        /// <param name="axisShift">轴位移量</param>
        int SetControlAxis(uint axisShift);

        /// <summary>
        /// 轴使能
        /// </summary>
        /// <param name="address">轴地址</param>
        /// <param name="kg">开关指令 'K'=开启, 'F'=关闭</param>
        int AxisEnable(uint address, char kg);

        /// <summary>
        /// 读取位置
        /// </summary>
        /// <param name="address">轴地址</param>
        int ReadPosition(uint address);

        /// <summary>
        /// 绝对位置移动
        /// </summary>
        /// <param name="address">轴地址</param>
        /// <param name="position">目标位置</param>
        int PositionAbsoluteMove(uint address, int position);

        /// <summary>
        /// 相对位置移动
        /// </summary>
        /// <param name="address">轴地址</param>
        /// <param name="position">相对位置</param>
        int PositionRelativeMove(uint address, int position);

        /// <summary>
        /// 停止轴运动
        /// </summary>
        /// <param name="address">轴地址</param>
        int AxisStop(uint address);

        /// <summary>
        /// 获取轴状态
        /// </summary>
        /// <param name="address">轴地址</param>
        /// <param name="statusNum">状态编号</param>
        int GetAxisStatus(int address, int statusNum);

        /// <summary>
        /// 清除电机错误
        /// </summary>
        /// <param name="address">轴地址</param>
        int ClearMotorError(int address);

        /// <summary>
        /// 设置速度
        /// </summary>
        /// <param name="address">轴地址</param>
        /// <param name="type">速度类型 'R'=运行速度, 'J'=点动速度</param>
        /// <param name="countsPerS">速度值（计数/秒）</param>
        int SetSpeed(uint address, char type, uint countsPerS);

        /// <summary>
        /// 获取速度
        /// </summary>
        /// <param name="address">轴地址</param>
        /// <param name="type">速度类型 'R'=运行速度, 'J'=点动速度</param>
        int GetSpeed(uint address, char type);

        /// <summary>
        /// 设置零位置
        /// </summary>
        /// <param name="address">轴地址</param>
        int SetZeroPosition(uint address);

        /// <summary>
        /// JOG移动
        /// </summary>
        /// <param name="address">轴地址</param>
        /// <param name="cmd">命令类型 'F'=正向, 'B'=反向, 'S'=停止</param>
        int JogMove(uint address, char cmd);
    }
}
