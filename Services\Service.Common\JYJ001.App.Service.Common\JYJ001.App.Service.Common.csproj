﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFrameworks>net6.0-windows</TargetFrameworks>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="System.Reactive" Version="5.0.0" />
	</ItemGroup>
	        <ItemGroup>
                <ProjectReference Include="..\..\..\Common\Aya.Extension\Aya.Extension.csproj" />      
                <ProjectReference Include="..\..\..\Common\Aya.Log\Aya.Log.csproj" />      
                <ProjectReference Include="..\..\..\WaferAligner.EventIds\WaferAligner.EventIds.csproj" />
                <ProjectReference Include="..\JYJ001.App.Service.Common.Extension\JYJ001.App.Service.Common.Extension.csproj" />
                <ProjectReference Include="..\JYJ001.App.Service.Common.Interface\JYJ001.App.Service.Common.Interface.csproj" />
        </ItemGroup>
	<ItemGroup>
		<Compile Update="Properties\Resources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>Resources.resx</DependentUpon>
		</Compile>
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Update="Properties\Resources.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>Resources.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>
	<ItemGroup>
		<None Update="system_configuration.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>
</Project>