﻿using CommunityToolkit.Mvvm.ComponentModel;
using JYJ001.App.Services.Common.Extension;
using JYJ001.App.Services.Common.Interfaces;
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using WaferAligner.Interfaces;
using WaferAligner.SerialControl.Interfaces;
using WaferAligner.EventIds;
namespace WaferAligner.SerialControl.Models
{
    /// <summary>
    /// 串口轴视图模型
    /// 实现ISerialAxisViewModel接口，使用ISerialAxisController作为后端
    /// </summary>
    public partial class SerialAxisViewModel : ObservableObject, ISerialAxisViewModel
    {
       
        private readonly ISerialAxisController _controller;
        private readonly ILoggingService _loggingService;
        private const int PositionMultiplier = 10500; // 位置单位转换乘数

        private int _targetPos = 0;
        private int _currentPos = 0;
        
        // 添加状态更新节流相关字段
        private DateTime _lastStatusUpdateTime = DateTime.MinValue;
        private const int MinStatusUpdateIntervalMs = 500; // 最小状态更新间隔（毫秒）
        private bool _isUpdatingStatus = false;
        private readonly object _updateLock = new object();
        
        // 添加移动操作日志抑制相关字段
        private DateTime _lastMoveFailLogTime = DateTime.MinValue;
        private int _moveFailLogCount = 0;
        private readonly object _moveLogLock = new object();
        private const int MaxMoveFailLogCount = 3; // 最大连续失败日志次数
        private const int MoveFailLogIntervalSeconds = 30; // 日志间隔（秒）

        private bool _disposed = false;

        /// <summary>
        /// 释放资源（同步）
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;
            try
            {
                // 注销事件
                _controller.PositionChanged -= OnControllerPositionChanged;
                _controller.StateChanged -= OnControllerStateChanged;
                // 释放控制器
                if (_controller is IDisposable disposable)
                {
                    disposable.Dispose();
                }
                _loggingService?.LogInformation($"{AxisName}串口轴资源已释放", EventIds.EventIds.Resource_Released);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"{AxisName}串口轴资源释放失败", EventIds.EventIds.Resource_Released);
            }
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源（异步）
        /// </summary>
        public async ValueTask DisposeAsync()
        {
            if (_disposed) return;
            _disposed = true;
            try
            {
                // 注销事件
                _controller.PositionChanged -= OnControllerPositionChanged;
                _controller.StateChanged -= OnControllerStateChanged;
                // 释放控制器
                if (_controller is IAsyncDisposable asyncDisposable)
                {
                    await asyncDisposable.DisposeAsync();
                }
                else if (_controller is IDisposable disposable)
                {
                    disposable.Dispose();
                }
                _loggingService?.LogInformation($"{AxisName}串口轴资源已异步释放", EventIds.EventIds.Resource_Released);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"{AxisName}串口轴资源异步释放失败", EventIds.EventIds.Resource_Released);
            }
            GC.SuppressFinalize(this);
        }

        [ObservableProperty]
        private bool _isConnected;

        [ObservableProperty]
        private bool _isReady;

        [ObservableProperty]
        private bool _isEnabled;

        [ObservableProperty]
        private bool _hasError;

        [ObservableProperty]
        private bool _arrive_position;

        public SerialAxisViewModel(ISerialAxisController controller, ILoggingService loggingService)
        {
            _controller = controller ?? throw new ArgumentNullException(nameof(controller));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            
            // 初始化属性
            AxisName = controller.AxisName;
            IsConnected = controller.IsConnected;
            IsReady = false;
            IsEnabled = false;
            HasError = false;
            Arrive_position = true;
            
            // 注册事件处理
            _controller.PositionChanged += OnControllerPositionChanged;
            _controller.StateChanged += OnControllerStateChanged;
            
            // 初始化状态
            _ = UpdateStatusAsync();
        }

        /// <summary>
        /// 轴名称
        /// </summary>
        public string AxisName { get; }

        /// <summary>
        /// 检查是否应该记录移动失败日志
        /// </summary>
        private bool ShouldLogMoveFailure()
        {
            // 如果已连接，总是记录日志
            if (IsConnected) return true;
            
            lock (_moveLogLock)
            {
                DateTime now = DateTime.Now;
                
                // 检查是否已达到最大连续失败日志次数
                if (_moveFailLogCount >= MaxMoveFailLogCount)
                {
                    // 检查是否已经过了抑制间隔
                    if ((now - _lastMoveFailLogTime).TotalSeconds < MoveFailLogIntervalSeconds)
                    {
                        return false; // 在抑制期内，不记录日志
                    }
                    else
                    {
                        // 抑制期已过，重置计数，允许记录日志
                        _moveFailLogCount = 1;
                        _lastMoveFailLogTime = now;
                        return true;
                    }
                }
                else
                {
                    // 未达到最大次数，记录日志并增加计数
                    _moveFailLogCount++;
                    _lastMoveFailLogTime = now;
                    
                    // 如果达到最大次数，记录一条特殊警告
                    if (_moveFailLogCount == MaxMoveFailLogCount)
                    {
                        _loggingService.LogWarning($"{AxisName}轴移动失败日志将被抑制{MoveFailLogIntervalSeconds}秒", 
                            EventIds.EventIds.Axis_Move_Error);
                    }
                    
                    return true;
                }
            }
        }

        #region 事件处理
        private void OnControllerPositionChanged(object sender, SerialAxisEventArgs e)
        {
            if (e.AxisName == AxisName)
            {
                _currentPos = e.Position;
                // 实际位置需要转换单位
                // 注意：如果有UI绑定需要更新，应在这里处理
            }
        }
        
        private void OnControllerStateChanged(object sender, SerialAxisEventArgs e)
        {
            if (e.AxisName == AxisName)
            {
                // 更新状态，使用节流机制
                _ = UpdateStatusWithThrottleAsync();
            }
        }
        
        /// <summary>
        /// 带节流的状态更新
        /// </summary>
        private async Task UpdateStatusWithThrottleAsync()
        {
            // 检查是否需要节流
            DateTime now = DateTime.Now;
            bool shouldUpdate = false;
            
            lock (_updateLock)
            {
                if (!_isUpdatingStatus && 
                    (now - _lastStatusUpdateTime).TotalMilliseconds >= MinStatusUpdateIntervalMs)
                {
                    _isUpdatingStatus = true;
                    shouldUpdate = true;
                }
            }
            
            if (shouldUpdate)
            {
                try
                {
                    await UpdateStatusAsync();
                }
                finally
                {
                    lock (_updateLock)
                    {
                        _lastStatusUpdateTime = DateTime.Now;
                        _isUpdatingStatus = false;
                    }
                }
            }
        }
        
        private async Task UpdateStatusAsync()
        {
            try
            {
                IsConnected = _controller.IsConnected;
                if (!IsConnected) return;
                
                // 获取使能状态
                var enableTask = _controller.EnableAxisAsync(CancellationToken.None);
                IsEnabled = await enableTask;
                IsReady = IsEnabled;
                
                // 获取报警状态
                var alarmTask = _controller.GetAlarmStateAsync(CancellationToken.None);
                HasError = await alarmTask != 0;
                
                // 获取运行状态
                var runTask = _controller.GetRunStateAsync(CancellationToken.None);
                var runState = await runTask;
                Arrive_position = runState == 0; // 0表示停止状态
                
                // 获取位置
                var posTask = _controller.GetPositionAsync(CancellationToken.None);
                _currentPos = await posTask;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"更新{AxisName}轴状态失败", EventIds.EventIds.Axis_Move_Error);
            }
        }
        #endregion

        #region 位置相关
        public async Task<bool> MoveToPositionAsync(double position)
        {
            try
            {
                // 检查连接状态
                if (!IsConnected)
                {
                    // 未连接时，使用日志抑制机制
                    if (ShouldLogMoveFailure())
                    {
                        _loggingService.LogWarning($"{AxisName}轴未连接，无法移动到位置{position}", EventIds.EventIds.Axis_Move_Error);
                    }
                    return false;
                }
                
                // 重置移动失败日志计数
                lock (_moveLogLock)
                {
                    _moveFailLogCount = 0;
                }
                
                // 转换单位并调用控制器方法
                int pos = (int)(position * PositionMultiplier);
                bool result = await _controller.MoveToPositionAsync(pos, false);
                
                // 只记录成功日志，或者连接正常但操作失败的日志
                if (result)
                {
                    _loggingService.LogInformation($"{AxisName}轴移动到位置{position}，结果：成功", EventIds.EventIds.Axis_Move_Started);
                }
                else
                {
                    _loggingService.LogWarning($"{AxisName}轴移动到位置{position}失败", EventIds.EventIds.Axis_Move_Error);
                }
                
                return result;
            }
            catch (Exception ex)
            {
                if (IsConnected) // 只在连接正常但操作异常时记录错误日志
                {
                    _loggingService.LogError(ex, $"{AxisName}轴移动到位置{position}失败", EventIds.EventIds.Axis_Move_Error);
                }
                return false;
            }
        }

        public async Task<bool> SetPositionAsync(double position)
        {
            try
            {
                // 保存目标位置
                _targetPos = (int)(position * PositionMultiplier);
                _loggingService.LogInformation($"设置{AxisName}轴目标位置为{position}", EventIds.EventIds.Axis_Move_Started);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置{AxisName}轴位置{position}失败", EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }

        public async Task<double> GetCurrentPositionAsync()
        {
            try
            {
                int pos = await _controller.GetPositionAsync();
                _currentPos = pos;
                double position = pos / (double)PositionMultiplier;
                return position;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取{AxisName}轴当前位置失败", EventIds.EventIds.Axis_Move_Error);
                return 0;
            }
        }
        
        [Obsolete("请使用GetCurrentPositionAsync异步方法")]
        public int GetPosition()
        {
            try
            {
                // 使用缓存的当前位置，避免同步阻塞
                return _currentPos;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取{AxisName}轴位置失败", EventIds.EventIds.Axis_Move_Error);
                return 0;
            }
        }

        public async Task<bool> SetMultiplePositionsAsync(double x, double y, double r)
        {
            // XyrAxisViewModel特有方法，对于单轴不适用
            _loggingService.LogWarning($"{AxisName}轴不支持多轴位置设置", EventIds.EventIds.Axis_Move_Error);
            return await Task.FromResult(false);
        }
        #endregion

        #region 运动控制
        public async Task<bool> HomeAsync()
        {
            try
            {
                bool result = await _controller.HomeAsync();
                _loggingService.LogInformation($"{AxisName}轴回零操作，结果：{(result ? "成功" : "失败")}", EventIds.EventIds.Axis_Move_Started);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{AxisName}轴回零失败", EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }

        public async Task<bool> StopAsync()
        {
            try
            {
                bool result = await _controller.StopAsync();
                _loggingService.LogInformation($"{AxisName}轴停止操作，结果：{(result ? "成功" : "失败")}", EventIds.EventIds.Axis_Move_Started);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{AxisName}轴停止失败", EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }

        public async Task<bool> ResetAsync()
        {
            try
            {
                bool result = await _controller.ClearErrorAsync();
                _loggingService.LogInformation($"{AxisName}轴清除错误，结果：{(result ? "成功" : "失败")}", EventIds.EventIds.Axis_Move_Started);
                
                // 更新轴状态，使用节流机制
                await UpdateStatusWithThrottleAsync();
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{AxisName}轴清除错误失败", EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }

        public async Task<bool> JogForwardAsync()
        {
            try
            {
                // 使用相对移动模拟点动
                const int jogDistance = 1000; // 点动距离
                bool result = await _controller.MoveToPositionAsync(jogDistance, true);
                _loggingService.LogInformation($"{AxisName}轴正向点动，结果：{(result ? "成功" : "失败")}", EventIds.EventIds.Axis_Move_Started);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{AxisName}轴正向点动失败", EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }

        public async Task<bool> JogBackwardAsync()
        {
            try
            {
                // 使用相对移动模拟点动
                const int jogDistance = -1000; // 点动距离
                bool result = await _controller.MoveToPositionAsync(jogDistance, true);
                _loggingService.LogInformation($"{AxisName}轴反向点动，结果：{(result ? "成功" : "失败")}", EventIds.EventIds.Axis_Move_Started);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{AxisName}轴反向点动失败", EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }

        public async Task<bool> JogStopAsync()
        {
            try
            {
                bool result = await _controller.StopAsync();
                _loggingService.LogInformation($"{AxisName}轴点动停止，结果：{(result ? "成功" : "失败")}", EventIds.EventIds.Axis_Move_Started);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{AxisName}轴点动停止失败", EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }

        // 原始接口兼容方法
        [Obsolete("请使用StopAsync异步方法")]
        public int Stop()
        {
            // 启动异步操作但不等待，立即返回成功，减少阻塞风险
            _ = StopAsync();
            return 1;
        }

        [Obsolete("请使用HomeAsync异步方法")]
        public int Home()
        {
            // 启动异步操作但不等待，立即返回成功，减少阻塞风险
            _ = HomeAsync();
            return 1;
        }

        [Obsolete("请使用ResetAsync异步方法")]
        public int ClearMotorError()
        {
            // 启动异步操作但不等待，立即返回成功，减少阻塞风险
            _ = _controller.ClearErrorAsync();
            return 1;
        }
        
        public void JOF_F_Start()
        {
            _ = JogForwardAsync();
        }

        public void JOG_B_Start()
        {
            _ = JogBackwardAsync();
        }

        public void JOG_Stop()
        {
            _ = JogStopAsync();
        }
        #endregion

        #region 速度控制
        public async Task<bool> SetJogSpeedAsync(double speed)
        {
            try
            {
                bool result = await _controller.SetJogSpeedAsync((uint)speed);
                _loggingService.LogInformation($"设置{AxisName}轴点动速度为{speed}，结果：{(result ? "成功" : "失败")}", EventIds.EventIds.Axis_Move_Started);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置{AxisName}轴点动速度失败", EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }

        public async Task<bool> SetRunSpeedAsync(double speed)
        {
            try
            {
                bool result = await _controller.SetRunSpeedAsync((uint)speed);
                _loggingService.LogInformation($"设置{AxisName}轴运行速度为{speed}，结果：{(result ? "成功" : "失败")}", EventIds.EventIds.Axis_Move_Started);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置{AxisName}轴运行速度失败", EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }
        
        [Obsolete("请使用SetJogSpeedAsync异步方法")]
        public int SetJogSpeed(uint speed)
        {
            // 启动异步操作但不等待，立即返回成功，减少阻塞风险
            _ = SetJogSpeedAsync(speed);
            return 1;
        }
        
        [Obsolete("请使用SetRunSpeedAsync异步方法")]
        public int SetRunSpeed(uint speed)
        {
            // 启动异步操作但不等待，立即返回成功，减少阻塞风险
            _ = SetRunSpeedAsync(speed);
            return 1;
        }
        
        public int GetJogSpeed()
        {
            try
            {
                // 默认点动速度，实际可能需要从控制器获取
                return 500;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取{AxisName}轴点动速度失败", EventIds.EventIds.Axis_Move_Error);
                return 0;
            }
        }
        
        public int GetSpeed()
        {
            try
            {
                // 默认运行速度，实际可能需要从控制器获取
                return 1000;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取{AxisName}轴运行速度失败", EventIds.EventIds.Axis_Move_Error);
                return 0;
            }
        }
        #endregion

        #region 事件通知和动作
        private Action<object> _positionChangeAction;
        private Action<object> _stateChangeAction;

        public void RegisterAction(string variableName, Action<object> action)
        {
            try
            {
                switch (variableName.ToLower())
                {
                    case "position":
                        _positionChangeAction = action;
                        _loggingService.LogDebug($"注册{AxisName}轴位置变化动作", EventIds.EventIds.Resource_Registered);
                        break;
                    case "state":
                        _stateChangeAction = action;
                        _loggingService.LogDebug($"注册{AxisName}轴状态变化动作", EventIds.EventIds.Resource_Registered);
                        break;
                    default:
                        _loggingService.LogWarning($"尝试注册未知变量{variableName}的动作", EventIds.EventIds.Resource_Registered);
                        break;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"注册{AxisName}轴变量{variableName}的动作失败", EventIds.EventIds.Unhandled_Exception);
            }
        }

        public void UnregisterAction(string variableName)
        {
            try
            {
                switch (variableName.ToLower())
                {
                    case "position":
                        _positionChangeAction = null;
                        _loggingService.LogDebug($"注销{AxisName}轴位置变化动作", EventIds.EventIds.Resource_Released);
                        break;
                    case "state":
                        _stateChangeAction = null;
                        _loggingService.LogDebug($"注销{AxisName}轴状态变化动作", EventIds.EventIds.Resource_Released);
                        break;
                    default:
                        _loggingService.LogWarning($"尝试注销未知变量{variableName}的动作", EventIds.EventIds.Resource_Released);
                        break;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"注销{AxisName}轴变量{variableName}的动作失败", EventIds.EventIds.Unhandled_Exception);
            }
        }
        #endregion

        #region 连接管理
        public async Task<bool> ConnectAsync(string address, int port)
        {
            try
            {
                // 更新状态，使用节流机制
                await UpdateStatusWithThrottleAsync();
                return _controller.IsConnected;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"连接{AxisName}轴控制器失败", EventIds.EventIds.Plc_Connection_Failed);
                return false;
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                // 控制器会在其他地方处理断开连接
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"断开{AxisName}轴控制器连接失败", EventIds.EventIds.Plc_Connection_Failed);
            }
        }
        #endregion

        #region 状态查询
        [Obsolete("请使用GetRunStateAsync异步方法")]
        public int GetRunState()
        {
            try
            {
                // 使用缓存状态，避免同步阻塞
                return Arrive_position ? 0 : 1;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取{AxisName}轴运行状态失败", EventIds.EventIds.Axis_Move_Error);
                return -1;
            }
        }

        public int GetEnableState()
        {
            try
            {
                return IsEnabled ? 1 : 0;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取{AxisName}轴使能状态失败", EventIds.EventIds.Axis_Move_Error);
                return 0;
            }
        }

        public int GetAlarmState()
        {
            try
            {
                return HasError ? 1 : 0;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取{AxisName}轴报警状态失败", EventIds.EventIds.Axis_Move_Error);
                return 0;
            }
        }

        public async Task<int> GetEnableStateAsync()
        {
            try
            {
                // 更新状态，使用节流机制
                await UpdateStatusWithThrottleAsync();
                return IsEnabled ? 1 : 0;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"异步获取{AxisName}轴使能状态失败", EventIds.EventIds.Axis_Move_Error);
                return 0;
            }
        }

        public async Task<int> GetAlarmStateAsync()
        {
            try
            {
                int state = await _controller.GetAlarmStateAsync();
                HasError = state != 0;
                return state;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"异步获取{AxisName}轴报警状态失败", EventIds.EventIds.Axis_Move_Error);
                return 0;
            }
        }

        public async Task<int> GetRunStateAsync()
        {
            try
            {
                int state = await _controller.GetRunStateAsync();
                Arrive_position = state == 0;
                return state;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"异步获取{AxisName}轴运行状态失败", EventIds.EventIds.Axis_Move_Error);
                return -1;
            }
        }

        public async Task<int> GetCurrentSpeedAsync()
        {
            try
            {
                // 默认速度，实际可能需要从控制器获取
                await Task.CompletedTask;
                return GetSpeed();
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"异步获取{AxisName}轴速度失败", EventIds.EventIds.Axis_Move_Error);
                return 0;
            }
        }

        public async Task<int> GetJogSpeedAsync()
        {
            try
            {
                // 默认点动速度，实际可能需要从控制器获取
                await Task.CompletedTask;
                return GetJogSpeed();
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"异步获取{AxisName}轴点动速度失败", EventIds.EventIds.Axis_Move_Error);
                return 0;
            }
        }
        #endregion

        #region 初始化
        // 初始化轴
        public async Task<bool> InitAxisAsync()
        {
            try
            {
                // 执行初始化操作
                bool enableResult = await _controller.EnableAxisAsync();
                if (!enableResult)
                {
                                         _loggingService.LogWarning($"{AxisName}轴初始化失败：使能失败", EventIds.EventIds.Axis_Move_Error);
                    return false;
                }
                
                // 更新轴状态，使用节流机制
                await UpdateStatusWithThrottleAsync();
                
                _loggingService.LogInformation($"{AxisName}轴初始化成功", EventIds.EventIds.Axis_Move_Started);
                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{AxisName}轴初始化失败", EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }
        #endregion

        #region 位置设置与执行
        /// <summary>
        /// 设置目标位置
        /// </summary>
        public int SetPosition(int position)
        {
            try
            {
                _targetPos = position;
                return 1;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置{AxisName}轴位置失败", EventIds.EventIds.Axis_Move_Error);
                return 0;
            }
        }

        /// <summary>
        /// 执行移动到目标位置
        /// </summary>
        public int GoPosition()
        {
            try
            {
                // 检查连接状态
                if (!IsConnected)
                {
                    // 未连接时，使用日志抑制机制
                    if (ShouldLogMoveFailure())
                    {
                        _loggingService.LogWarning($"{AxisName}轴未连接，无法移动到目标位置", EventIds.EventIds.Axis_Move_Error);
                    }
                    return 0;
                }
                
                // 重置移动失败日志计数
                lock (_moveLogLock)
                {
                    _moveFailLogCount = 0;
                }
                
                bool result = _controller.MoveToPositionAsync(_targetPos, false).GetAwaiter().GetResult();
                return result ? 1 : 0;
            }
            catch (Exception ex)
            {
                if (IsConnected) // 只在连接正常但操作异常时记录错误日志
                {
                    _loggingService.LogError(ex, $"{AxisName}轴移动到目标位置失败", EventIds.EventIds.Axis_Move_Error);
                }
                return 0;
            }
        }
        #endregion
    }
}
