﻿using System;

namespace WaferAligner
{
    partial class MyLED
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            
            // 释放TimerWrapper资源
            if (disposing && _timer != null)
            {
                try
                {
                    _timer.Stop();
                    _timer.RemoveElapsedHandler(Timer_Tick);
                    _timer.Dispose();
                    _timer = null;
                }
                catch (Exception ex)
                {

                    throw;
                }
            }
            
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
        }

        #endregion
    }
}
