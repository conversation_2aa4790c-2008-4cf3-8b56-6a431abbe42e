﻿namespace WaferAligner
{
    partial class ULine
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            button1 = new Button();
            button2 = new Button();
            lalTextName = new Label();
            SuspendLayout();
            // 
            // button1
            // 
            button1.BackColor = Color.Silver;
            button1.Location = new Point(141, 14);
            button1.Name = "button1";
            button1.Size = new Size(900, 1);
            button1.TabIndex = 0;
            button1.Text = "button1";
            button1.UseVisualStyleBackColor = false;
            // 
            // button2
            // 
            button2.BackColor = Color.Silver;
            button2.Location = new Point(6, 14);
            button2.Name = "button2";
            button2.Size = new Size(30, 1);
            button2.TabIndex = 1;
            button2.Text = "button2";
            button2.UseVisualStyleBackColor = false;
            // 
            // lalTextName
            // 
            lalTextName.AutoSize = true;
            lalTextName.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            lalTextName.Location = new Point(41, 6);
            lalTextName.Name = "lalTextName";
            lalTextName.Size = new Size(87, 16);
            lalTextName.TabIndex = 2;
            lalTextName.Text = "1：固定XXX";
            // 
            // ULine
            // 
            AutoScaleDimensions = new SizeF(7F, 17F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(248, 248, 248);
            Controls.Add(lalTextName);
            Controls.Add(button2);
            Controls.Add(button1);
            Name = "ULine";
            Size = new Size(1229, 33);
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Button button1;
        private Button button2;
        private Label lalTextName;
    }
}
