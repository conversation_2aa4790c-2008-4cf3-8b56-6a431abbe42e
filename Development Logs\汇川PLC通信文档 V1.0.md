# 汇川PLC通信文档 V1.0

## 一、简介与背景

本文档详细描述WaferAligner项目中与汇川PLC通信相关的机制、组件和流程。WaferAligner项目当前使用汇川PLC控制系统，通过ModbusTCP协议实现通信。由于项目正在进行Phase 3重构，本文档可作为理解现有系统和规划后续优化的基础。

### 1.1 文档目的
- 梳理当前汇川PLC通信架构和实现
- 明确关键类、接口和数据流
- 为"PLC通信批处理优化"任务提供参考

### 1.2 关联重构任务
- Phase 3重构计划中的"PLC通信批处理优化"任务
- 变量组机制设计与实现
- 基于事件的PLC通信机制研究

## 二、架构概述

### 2.1 分层结构

WaferAligner项目的PLC通信采用分层设计，从底层到上层分别为：

1. **通信基础层**：直接调用ModbusTCP API
   - StandardModbusApi.dll - 汇川PLC提供的通信库

2. **客户端层**：封装通信基础层
   - InvoancePlcClient - 底层PLC通信客户端

3. **实例层**：实现通用接口
   - InvoancePlcInstance - 实现IPlcInstance接口的具体类

4. **服务层**：提供业务级接口
   - PlcConnectionManager - 管理PLC连接
   - PlcVariableService - 提供PLC变量读写服务

5. **模型层**：业务逻辑实现
   - AxisViewModel系列 - 实现轴控制逻辑
   - MainWindowViewModel - 实现主窗口业务逻辑

6. **UI层**：用户界面
   - FTitlePage系列 - 各功能页面
     - FTitlePage1 - 键合对准页面
     - FTitlePage2 - 对准参数页面
     - FTitlePage3 - 运动参数页面
     - FTitlePage4 - 用户管理页面
   - FHeaderMainFooter - 主框架窗体

### 2.2 通信模式

当前系统采用混合式通信模式：

1. **定时轮询模式**（主要方式）
   - 通过定时器定期读取PLC变量
   - 周期性刷新UI显示

2. **事件监控模式**（辅助方式）
   - 注册需要监控的变量
   - 变量值变化时触发回调

### 2.3 系统架构图

```
┌──────────────────────────────────────────────────────────────────┐
│                           UI层                                   │
│  ┌──────────┐  ┌──────────┐  ┌──────────┐  ┌──────────────────┐  │
│  │FTitlePage1│  │FTitlePage2│  │FTitlePage3│  │FHeaderMainFooter│  │
│  └────┬─────┘  └────┬─────┘  └────┬─────┘  └────────┬─────────┘  │
└───────┼──────────────┼──────────────┼────────────────┼────────────┘
         │              │              │                │
         ▼              ▼              ▼                ▼
┌──────────────────────────────────────────────────────────────────┐
│                           模型层                                 │
│  ┌──────────┐  ┌──────────┐  ┌──────────┐  ┌──────────────────┐  │
│  │XAxisView │  │YAxisView │  │RAxisView │  │MainWindowViewModel│  │
│  │Model     │  │Model     │  │Model     │  │                  │  │
│  └────┬─────┘  └────┬─────┘  └────┬─────┘  └────────┬─────────┘  │
└───────┼──────────────┼──────────────┼────────────────┼────────────┘
         │              │              │                │
         └──────────────┼──────────────┼────────────────┘
                        │              │
                        ▼              ▼
┌──────────────────────────────────────────────────────────────────┐
│                           服务层                                 │
│           ┌───────────────────────┐  ┌─────────────────────────┐ │
│           │  PlcVariableService   │  │   PlcConnectionManager  │ │
│           └────────────┬──────────┘  └───────────┬─────────────┘ │
└────────────────────────┼───────────────────────┬─┼────────────────┘
                         │                       │ │
                         ▼                       │ │
┌──────────────────────────────────────────────┐ │ │
│                  实例层                      │ │ │
│             ┌───────────────────┐            │ │ │
│             │  IPlcInstance     │◄───────────┘ │ │
│             └────────┬──────────┘              │ │
│                      │                         │ │
│                      ▼                         │ │
│             ┌───────────────────┐              │ │
│             │ InvoancePlcInstance│◄─────────────┘ │
│             └────────┬──────────┘                │
└──────────────────────┼──────────────────────────┘
                       │
                       ▼
┌──────────────────────────────────────────────┐
│                  客户端层                    │
│             ┌───────────────────┐            │
│             │ InvoancePlcClient │            │
│             └────────┬──────────┘            │
└──────────────────────┼──────────────────────┘
                       │
                       ▼
┌──────────────────────────────────────────────┐
│                  通信基础层                  │
│             ┌───────────────────┐            │
│             │StandardModbusApi.dll│          │
│             └───────────────────┘            │
└──────────────────────────────────────────────┘
```

## 三、核心类与接口

### 3.1 通信基础层

#### 3.1.1 StandardModbusApi.dll
- **功能**：提供与汇川PLC通信的底层API
- **关键函数**：
  - `Init_ETH_String` - 初始化以太网连接
  - `Am600_Read_Soft_Elem` - 读取软元件
  - `Am600_Write_Soft_Elem` - 写入软元件
  - `Exit_ETH` - 关闭以太网连接
- **示例调用**：
  ```csharp
  // 初始化连接
  Init_ETH_String(ipAddress, 0, port);
  
  // 读取MW元件
  byte[] buffer = new byte[count * 2];
  Am600_Read_Soft_Elem(SoftElemType.ELEM_MW, address, count, buffer);
  
  // 写入MW元件
  Am600_Write_Soft_Elem_Int16(SoftElemType.ELEM_MW, address, 1, new short[] { value });
  ```

### 3.2 客户端层

#### 3.2.1 InvoancePlcClient
- **命名空间**：`PLC.Inovance.Client`
- **功能**：封装与汇川PLC的直接通信
- **关键属性**：
  - `IsConnected` - 连接状态
  - `HCNotificationChanged` - 变量变更事件
- **关键方法**：
  - `Connect` - 连接PLC
  - `ReadDataAsync` - 读取变量
  - `WriteData` - 写入变量
  - `LoadSymbols` - 加载符号表
  - `KeepAliveRequest` - 心跳检测
- **代码示例**：
  ```csharp
  // 读取变量示例
  public object? ReadDataAsync(string name, Type type)
  {
      var symbol = Symbols.Find(v => v.Name == name);
      if (symbol == null)
          throw new NotFoundSymbolException(name);
          
      // 创建缓冲区
      byte[] buffer = new byte[symbol.Size];
      
      // 读取PLC内存
      int ret = Am600_Read_Soft_Elem(SoftElemType.ELEM_MW, symbol.Address, 
                                    symbol.Size / 2, buffer);
      if (ret != 1)
          return null;
          
      return CastValue(new Span<byte>(buffer), type);
  }
  ```

#### 3.2.2 InvoanceVariableChangedEventArgs
- **命名空间**：`PLC.Inovance.Client`
- **功能**：变量变更事件参数
- **关键属性**：
  - `Value` - 变量新值
  - `Name` - 变量名称
  - `Handle` - 变量句柄
- **用法示例**：
  ```csharp
  // 事件处理方法
  client.HCNotificationChanged += (sender, e) => {
      Console.WriteLine($"变量 {e.Name} 变更为: {e.Value}");
      // 处理变量变更
  };
  ```

### 3.3 实例层

#### 3.3.1 IPlcInstance
- **命名空间**：`Aya.PLC.Base`
- **功能**：定义PLC实例通用接口
- **关键方法**：
  - `Connect` - 连接PLC
  - `ReadVariableAsync` - 异步读取变量
  - `WriteVariableAsync` - 异步写入变量
  - `RegisterMonitorVariables` - 注册监视变量
- **接口定义**：
  ```csharp
  public interface IPlcInstance
  {
      bool IsConnected { get; }
      void Connect(string ip, int nIpPort, int nNetId);
      Task<object> ReadVariableAsync<ReadInfo>(ReadInfo infos, CancellationToken cancel);
      Task<bool> WriteVariableAsync<WriteInfo>(WriteInfo info, CancellationToken cancle);
      IDictionary<uint,(string,Type)> RegisterMonitorVariables<T,S>(T variableInfo, S settings);
      void AddNotification<T>(EventHandler<T> function);
      void RemoveNotification<NT>(EventHandler<NT> function);
  }
  ```

#### 3.3.2 InvoancePlcInstance
- **命名空间**：`PLC.Inovance`
- **功能**：实现IPlcInstance接口的汇川PLC实例
- **关键属性**：
  - `IsConnected` - 连接状态
  - `IsConnecting` - 正在连接标志
- **关键方法**：
  - `Connect` - 连接PLC
  - `Disconnect` - 断开连接
  - `ReadVariableAsync` - 异步读取变量
  - `WriteVariableAsync` - 异步写入变量
  - `RegisterMonitorVariables` - 注册监视变量
- **方法实现示例**：
  ```csharp
  public async Task<object> ReadVariableAsync<ReadInfo>(ReadInfo infos, CancellationToken cancel)
  {
      // 连接状态检查
      return await CheckConnectStateBeforOperatorAsync<object, ReadInfo>(
          async (info) => {
              var name = "";
              var type = typeof(object);
              // ...获取变量名和类型...
              
              // 调用客户端读取方法
              return _client.ReadDataAsync(name, type);
          }, 
          infos
      );
  }
  ```

### 3.4 服务层

#### 3.4.1 PlcConnectionManager
- **命名空间**：`WaferAligner.Services`
- **功能**：管理多个PLC连接
- **关键属性**：
  - `ConnectionStateChanged` - 连接状态变更事件
- **关键方法**：
  - `ConnectAsync` - 异步连接PLC
  - `DisconnectAsync` - 异步断开连接
  - `IsConnected` - 检查连接状态
  - `GetPlcInstance` - 获取PLC实例
  - `InitializeAsync` - 初始化所有连接
- **具体实现示例**：
  ```csharp
  public async Task<bool> ConnectAsync(string connectionName, string address, int port)
  {
      if (_disposed || string.IsNullOrEmpty(connectionName))
          return false;

      try
      {
          var plcInstance = new InvoancePlcInstance(_loggingService);
          var connectionInfo = new PlcConnectionInfo
          {
              Name = connectionName,
              Address = address,
              Port = port,
              PlcInstance = plcInstance,
              IsConnected = false
          };

          // 添加到连接字典
          _connections.AddOrUpdate(connectionName, connectionInfo, 
                                  (key, oldValue) => connectionInfo);

          // 执行实际连接
          bool success = await Task.Run(() => {
              try {
                  plcInstance.Connect(address, port, 0);
                  return plcInstance.IsConnected;
              } catch {
                  return false;
              }
          });

          connectionInfo.IsConnected = success;
          
          // 触发连接状态变化事件
          ConnectionStateChanged?.Invoke(this, new PlcConnectionEventArgs {
              ConnectionName = connectionName,
              IsConnected = success,
              Address = address,
              Port = port
          });

          return success;
      }
      catch (Exception ex)
      {
          _loggingService.LogError(ex, $"连接PLC {connectionName} 时发生错误", 
                                  WaferAligner.EventIds.EventIds.PlcConnectFailed);
          return false;
      }
  }
  ```

#### 3.4.2 PlcVariableService
- **命名空间**：`WaferAligner.Services`
- **功能**：提供安全的PLC变量操作
- **关键方法**：
  - `ReadVariableSafelyAsync` - 安全异步读取变量
  - `WriteVariableSafelyAsync` - 安全异步写入变量
  - `IsConnectionOpen` - 检查连接状态
- **服务实现示例**：
  ```csharp
  public async Task<T> ReadVariableSafelyAsync<T>(string variableName, T defaultValue = default)
  {
      try
      {
          var readInfo = new PLCVarReadInfo { 
              Name = variableName, 
              Type = typeof(T) 
          };
          var value = await _plcInstance.ReadVariableAsync(readInfo, CancellationToken.None);
          return value is T t ? t : defaultValue;
      }
      catch (Exception ex)
      {
          _loggingService.LogError(ex, $"读取PLC变量失败: {variableName}");
          return defaultValue;
      }
  }
  ```

### 3.5 数据传输类

#### 3.5.1 PLCVarReadInfo
- **命名空间**：`Aya.PLC.Base`
- **功能**：封装PLC变量读取信息
- **关键属性**：
  - `Name` - 变量名
  - `Type` - 变量类型
- **用法示例**：
  ```csharp
  var readInfo = new PLCVarReadInfo { 
      Name = $"{AxisConstants.AXIS_GVL}.UpperWaferAdSorptionSensor", 
      Type = typeof(bool) 
  };
  var result = await plcInstance.ReadVariableAsync(readInfo, CancellationToken.None);
  ```

#### 3.5.2 PLCVarWriteInfo
- **命名空间**：`Aya.PLC.Base`
- **功能**：封装PLC变量写入信息
- **关键属性**：
  - `Name` - 变量名
  - `Value` - 写入值
- **用法示例**：
  ```csharp
  var writeInfo = new PLCVarWriteInfo { 
      Name = $"{AxisConstants.AXIS_GVL}.UpperChuckVacuum", 
      Value = true 
  };
  var success = await plcInstance.WriteVariableAsync(writeInfo, CancellationToken.None);
  ```

## 四、通信机制详解

### 4.1 PLC数据读取机制

#### 4.1.1 定时轮询读取流程
1. **触发阶段**
   - 由`FTitlePage3.TmrInput_Tick`定时器触发(300-500ms)
   - 创建超时保护令牌(250ms)

2. **并行读取阶段**
   - 创建变量名数组定义要读取的变量
   - 为每个变量创建独立的异步读取任务
   - 使用`Task.WhenAny`和`Task.WhenAll`实现带超时的等待
   - 处理读取结果或超时情况

3. **数据传输阶段**
   - `PlcVariableService.ReadVariableSafelyAsync`创建变量读取信息
   - `InvoancePlcInstance.ReadVariableAsync`检查连接状态并转发请求
   - `InvoancePlcClient.ReadDataAsync`查找符号并执行读取

4. **硬件交互阶段**
   - 调用`Am600_Read_Soft_Elem`等底层函数
   - 解析返回数据到指定类型
   - 通过返回值链返回到应用层

```
┌───────────────┐      ┌───────────────────┐      ┌───────────────────┐
│ FTitlePage3   │      │ PlcVariableService│      │InvoancePlcInstance│
│ TmrInput_Tick │      │                   │      │                   │
└───────┬───────┘      └─────────┬─────────┘      └─────────┬─────────┘
        │                        │                          │
        │ 1. 定时触发            │                          │
        ├───────────────────────►│                          │
        │                        │                          │
        │                        │ 2. 创建读取信息          │
        │                        ├─────────────────────────►│
        │                        │                          │
        │                        │                          │ 3. 检查连接状态
        │                        │                          ├───────┐
        │                        │                          │       │
        │                        │                          │◄──────┘
        │                        │                          │
        │                        │                          │ 4. 调用客户端读取
        │                        │                          ├───────┐
        │                        │                          │       ▼
        │                        │                          │  ┌────────────────┐
        │                        │                          │  │InvoancePlcClient│
        │                        │                          │  └────────┬───────┘
        │                        │                          │           │
        │                        │                          │           │ 5. 查找符号
        │                        │                          │           ├───────┐
        │                        │                          │           │       │
        │                        │                          │           │◄──────┘
        │                        │                          │           │
        │                        │                          │           │ 6. 调用底层API
        │                        │                          │           ├───────┐
        │                        │                          │           │       ▼
        │                        │                          │           │  ┌────────────────┐
        │                        │                          │           │  │StandardModbusAPI│
        │                        │                          │           │  └────────┬───────┘
        │                        │                          │           │           │
        │                        │                          │           │◄──────────┘
        │                        │                          │           │ 7. 返回结果
        │                        │                          │◄──────────┘
        │                        │                          │
        │                        │◄─────────────────────────┤ 8. 返回转换后的值
        │                        │                          │
        │◄───────────────────────┤ 9. 返回读取结果         │
        │                        │                          │
        │ 10. 更新UI             │                          │
        ├───────┐                │                          │
        │       │                │                          │
        │◄──────┘                │                          │
        │                        │                          │
┌───────┴───────┐      ┌─────────┴─────────┐      ┌─────────┴─────────┐
│ FTitlePage3   │      │ PlcVariableService│      │InvoancePlcInstance│
│ TmrInput_Tick │      │                   │      │                   │
└───────────────┘      └───────────────────┘      └───────────────────┘
```

## 五、符号表机制

### 5.1 符号表文件
1. **文件来源**
   - 从汇川PLC编程软件导出的符号表
   - 保存在`%APPDATA%\Aligner\PLCSymbols\`目录
   - 文件名包含`.Device.Application.json`后缀

2. **文件格式**
   - JSON格式保存PLC变量信息
   - 包含变量名、地址、数据类型等信息
   - 示例结构:
     ```json
     {
       "symbols": [
         {
           "Name": "AXIS_GVL.UpperWaferAdSorptionSensor",
           "Address": 3000,
           "Type": "BOOL",
           "Comment": "上晶圆吸附传感器"
         },
         {
           "Name": "AXIS_GVL.LowerChuckAdSorptionSensor",
           "Address": 3001,
           "Type": "BOOL",
           "Comment": "下卡盘吸附传感器"
         }
       ]
     }
     ```

## 六、后续优化建议

### 6.1 PLC通信批处理优化

#### 6.1.1 变量组机制
- 设计`PLCVariableGroup`类定义相关变量组
- 实现变量依赖关系管理
- 支持变量组的按需加载和卸载

#### 6.1.2 批量读写接口
- 优化`IPlcInstance`接口，强化批量操作
- 实现真正的底层批量读写
- 优化错误处理和重试机制

### 6.2 模拟事件通知机制

#### 6.2.1 差异检测轮询
- 保留轮询机制，但升级为"智能轮询"
- 只在检测到变量值变化时触发事件处理
- 内部仍使用轮询，但对应用层呈现事件接口

#### 6.2.2 变化率优化轮询
- 对不同变量采用不同的轮询频率
- 变化频繁的变量高频轮询，稳定的变量低频轮询
- 通过历史数据分析优化轮询频率

#### 6.2.3 PLC内部逻辑辅助
- 在PLC程序中添加"变更标志位"
- 每当关键变量变化，PLC程序设置对应的标志位
- 应用程序只需轮询这些标志位
- 发现标志位变化时，再读取对应的实际变量

### 6.3 架构增强

#### 6.3.1 更严格的分层
- 强化服务层和表现层分离
- 减少UI直接访问PLC实例
- 完善依赖注入和服务注册

#### 6.3.2 异步操作增强
- 减少UI线程阻塞操作
- 完善异步操作的取消和超时机制
- 增强异步操作的错误恢复能力

## 附录

### A. 关键类关系图
```
┌──────────────────────────────────────────────────────┐
│                      UI层                           │
│                                                     │
│  ┌───────────┐   ┌───────────┐   ┌───────────┐      │
│  │FTitlePage1│   │FTitlePage2│   │FTitlePage3│      │
│  └─────┬─────┘   └─────┬─────┘   └─────┬─────┘      │
└────────┼───────────────┼───────────────┼────────────┘
          │               │               │
          ▼               ▼               ▼
┌──────────────────────────────────────────────────────┐
│                      模型层                         │
│                                                     │
│  ┌───────────────┐      ┌─────────────────────┐     │
│  │AxisViewModels │      │MainWindowViewModel  │     │
│  └───────┬───────┘      └──────────┬──────────┘     │
└──────────┼──────────────────────────┼────────────────┘
           │                          │
           │                          │
           ▼                          ▼
┌──────────────────────────────────────────────────────┐
│                      服务层                         │
│                                                     │
│  ┌───────────────────┐   ┌─────────────────────┐    │
│  │PlcVariableService │   │PlcConnectionManager │    │
│  └──────────┬────────┘   └──────────┬──────────┘    │
└─────────────┼───────────────────────┼────────────────┘
              │                       │
              │           ┌───────────┘
              ▼           ▼
┌──────────────────────────────────────────────────────┐
│                      接口层                         │
│                                                     │
│              ┌──────────────────┐                   │
│              │   IPlcInstance   │                   │
│              └────────┬─────────┘                   │
└───────────────────────┼───────────────────────────────┘
                        │
                        ▼
┌──────────────────────────────────────────────────────┐
│                      实现层                         │
│                                                     │
│            ┌───────────────────────┐                │
│            │  InvoancePlcInstance  │                │
│            └───────────┬───────────┘                │
└────────────────────────┼────────────────────────────┘
                         │
                         ▼
┌──────────────────────────────────────────────────────┐
│                      客户端层                       │
│                                                     │
│             ┌──────────────────────┐                │
│             │  InvoancePlcClient   │                │
│             └───────────┬──────────┘                │
└─────────────────────────┼──────────────────────────────┘
                          │
                          ▼
┌──────────────────────────────────────────────────────┐
│                      API层                          │
│                                                     │
│            ┌────────────────────────┐               │
│            │  StandardModbusApi.dll │               │
│            └────────────────────────┘               │
└──────────────────────────────────────────────────────┘
```

**文档版本**：1.0  
**创建日期**：2025年07月18日  
**适用范围**：WaferAligner项目 Phase 3重构计划