using JYJ001.App.Service.Usermanagement;

namespace JYJ001.App.Service.Usermanagement
{
    public static class UserManagementExtensions
    {
        /// <summary>
        /// 检查用户是否具有管理员权限
        /// </summary>
        public static bool IsAdmin(this IUserManagement userManagement, string username)
        {
            return userManagement.CheckPermission(username, "system.admin");
        }

        /// <summary>
        /// 检查用户是否可以编辑配方
        /// </summary>
        public static bool CanEditRecipe(this IUserManagement userManagement, string username)
        {
            return userManagement.CheckPermission(username, "recipe.edit");
        }

        /// <summary>
        /// 检查用户是否可以控制系统
        /// </summary>
        public static bool CanControlSystem(this IUserManagement userManagement, string username)
        {
            return userManagement.CheckPermission(username, "process.control");
        }

        /// <summary>
        /// 检查用户是否可以管理用户
        /// </summary>
        public static bool CanManageUsers(this IUserManagement userManagement, string username)
        {
            return userManagement.CheckPermission(username, "user.edit");
        }

        /// <summary>
        /// 获取用户的所有权限
        /// </summary>
        public static IEnumerable<string> GetUserPermissions(this IUserManagement userManagement, string username)
        {
            var user = userManagement.GetUserInfo(username);
            if (user == null) return Enumerable.Empty<string>();

            var allPermissions = userManagement.GetAllPermissions().Select(p => p.Name).ToList();
            var userPermissions = new List<string>();

            foreach (var roleName in user.Roles)
            {
                var role = userManagement.GetAllRoles().FirstOrDefault(r => r.Name == roleName);
                if (role != null)
                {
                    if (role.Permissions.Contains("*"))
                    {
                        return allPermissions; // 返回所有权限
                    }
                    userPermissions.AddRange(role.Permissions);
                }
            }

            return userPermissions.Distinct();
        }
    }
} 