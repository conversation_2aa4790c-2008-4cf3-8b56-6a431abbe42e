# WaferAligner串口通信文档 V1.0

## 一、简介与背景

本文档详细分析WaferAligner项目中串口通信的架构、组件和工作机制。WaferAligner项目使用串口通信控制XYR轴运动，这部分功能在最近的重构中已经从PLC通信中分离出来，形成了独立的架构。

### 1.1 文档目的
- 详细描述串口通信架构和实现机制
- 明确关键类、接口和数据流向
- 评估重构后的架构优势与挑战

### 1.2 关联重构任务
- 串口通信整改（已完成）
- Phase 3重构计划中的通信系统优化任务
- 与PLC通信的架构分离

## 二、架构概述

### 2.1 分层结构

WaferAligner项目串口通信采用分层设计，从底层到上层分别为：

1. **通信基础层**：直接调用SerialCom.dll
   - SerialCom.dll - 第三方提供的串口通信库

2. **封装层**：封装底层DLL调用
   - SerialComWrapper - 封装SerialCom.dll的所有API调用

3. **连接管理层**：管理串口连接生命周期
   - SerialConnectionManager - 管理串口连接状态、保活检测

4. **控制器层**：提供轴控制API
   - SerialAxisController - 实现单个轴控制逻辑
   - SerialAxisControllerFactory - 创建和管理轴控制器实例

5. **视图模型层**：提供业务级API
   - SerialAxisViewModel - 实现IXyrAxisViewModel接口，支持UI绑定

6. **UI层**：用户界面
   - FTitlePage系列 - 各功能页面通过工厂方法获取轴视图模型

### 2.2 系统架构图

```
┌──────────────────────────────────────────────────────────────────┐
│                           UI层                                   │
│  ┌──────────┐  ┌──────────┐  ┌──────────┐  ┌──────────────────┐  │
│  │FTitlePage1│  │FTitlePage2│  │FTitlePage3│  │FHeaderMainFooter│  │
│  └────┬─────┘  └────┬─────┘  └────┬─────┘  └────────┬─────────┘  │
└───────┼──────────────┼──────────────┼────────────────┼────────────┘
         │              │              │                │
         ▼              ▼              ▼                ▼
┌──────────────────────────────────────────────────────────────────┐
│                        工厂层                                    │
│  ┌───────────────────────────────────────────────────────────┐   │
│  │                 AxisViewModelFactory                      │   │
│  └─────────────────────────────┬─────────────────────────────┘   │
└─────────────────────────────────┼───────────────────────────────────┘
                                  │
                                  ▼
┌──────────────────────────────────────────────────────────────────┐
│                      视图模型层                                 │
│  ┌──────────────────────────────────────────────────────────┐   │
│  │                 SerialAxisViewModel                      │   │
│  └─────────────────────────────┬─────────────────────────────┘   │
└─────────────────────────────────┼───────────────────────────────────┘
                                  │
                                  ▼
┌──────────────────────────────────────────────────────────────────┐
│                      控制器层                                   │
│  ┌────────────────────────┐        ┌────────────────────────┐   │
│  │ SerialAxisController   │        │SerialAxisController    │   │
│  │       Factory          │◄───────┤                        │   │
│  └────────────┬───────────┘        └────────────┬───────────┘   │
└───────────────┼────────────────────────────────┬────────────────┘
                │                                │
                │                                ▼
                │              ┌──────────────────────────────────┐
                │              │     连接管理层                   │
                │              │  ┌──────────────────────────┐    │
                │              │  │  SerialConnectionManager │    │
                │              │  └────────────┬─────────────┘    │
                │              └───────────────┬──────────────────┘
                │                              │
                └──────────────►               ▼
                               ┌──────────────────────────────────┐
                               │           封装层                 │
                               │      ┌────────────────┐          │
                               │      │SerialComWrapper│          │
                               │      └───────┬────────┘          │
                               └──────────────┼───────────────────┘
                                              │
                                              ▼
                               ┌──────────────────────────────────┐
                               │        通信基础层                │
                               │      ┌────────────────┐          │
                               │      │  SerialCom.dll │          │
                               │      └────────────────┘          │
                               └──────────────────────────────────┘
```

### 2.3 通信模式

串口通信采用以下模式：

1. **直接控制模式**：
   - 通过方法调用直接发送命令到控制器
   - 采用异步操作避免阻塞UI线程

2. **事件通知机制**：
   - 通过传统事件和事件总线双通道通知状态变化
   - 支持位置变更、状态变更等通知

3. **定时保活机制**：
   - 通过定时器定期检查连接状态
   - 自动重连机制确保稳定性

## 三、核心类与接口

### 3.1 通信基础层

#### 3.1.1 SerialCom.dll
- **功能**：提供与串口设备通信的底层API
- **关键函数**：
  - `DLL_OpenCom` - 打开串口
  - `DLL_CloseCom` - 关闭串口
  - `DLL_AxisEnable` - 轴使能控制
  - `DLL_ReadPosition` - 读取当前位置
  - `DLL_PositionAbsoluteMove` - 绝对位置移动
  - `DLL_PositionRelativeMove` - 相对位置移动
  - `DLL_AxisStop` - 停止轴移动

### 3.2 封装层

#### 3.2.1 ISerialComWrapper
- **命名空间**：`WaferAligner.SerialControl.Interfaces`
- **功能**：定义与SerialCom.dll交互的接口
- **关键方法**：
  - `OpenComPort` - 打开串口
  - `CloseComPort` - 关闭串口
  - `AxisEnable` - 轴使能控制
  - `ReadPosition` - 读取位置
  - `PositionAbsoluteMove` - 绝对位置移动
  - `PositionRelativeMove` - 相对位置移动
- **接口定义**：
  ```csharp
  public interface ISerialComWrapper : IDisposable
  {
      int GetVersion();
      int OpenDevice(int controlNum);
      int OpenComPort(int comPort, int baudRate);
      int CloseComPort();
      int SetControlAxis(uint axisShift);
      int AxisEnable(uint address, char kg);
      int ReadPosition(uint address);
      int PositionAbsoluteMove(uint address, int position);
      int PositionRelativeMove(uint address, int position);
      int AxisStop(uint address);
      // ...更多方法
  }
  ```

#### 3.2.2 SerialComWrapper
- **命名空间**：`WaferAligner.SerialControl.Implementation`
- **功能**：实现ISerialComWrapper接口，封装SerialCom.dll
- **实现特点**：
  - 使用P/Invoke调用非托管代码
  - 统一的错误处理和日志记录
  - 资源安全释放
- **代码示例**：
  ```csharp
  public class SerialComWrapper : ISerialComWrapper
  {
      [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
      private static extern Int32 DLL_OpenCom(Int32 Com, Int32 Baud);
      
      public int OpenComPort(int comPort, int baudRate)
      {
          CheckDisposed();
          try
          {
              _loggingService.LogDebug($"尝试打开串口: COM{comPort}, {baudRate}波特率", 
                  WaferAligner.EventIds.EventIds.SerialConnectStart);
              return DLL_OpenCom(comPort, baudRate);
          }
          catch (Exception ex)
          {
              _loggingService.LogError(ex, $"打开串口失败: COM{comPort}, {baudRate}波特率", 
                  WaferAligner.EventIds.EventIds.SerialConnectFailed);
              return -1;
          }
      }
      // ...其他方法实现
  }
  ```

### 3.3 连接管理层

#### 3.3.1 ISerialConnectionManager
- **命名空间**：`WaferAligner.SerialControl.Interfaces`
- **功能**：管理串口连接的生命周期
- **关键属性和方法**：
  - `IsConnected` - 连接状态
  - `ConnectionStateChanged` - 连接状态变化事件
  - `ConnectAsync` - 异步连接
  - `DisconnectAsync` - 异步断开连接
  - `InitializeAsync` - 初始化连接
- **接口定义**：
  ```csharp
  public interface ISerialConnectionManager : IDisposable
  {
      bool IsConnected { get; }
      event EventHandler<SerialConnectionEventArgs> ConnectionStateChanged;
      Task InitializeAsync();
      Task<bool> ConnectAsync(int comPort, int baudRate, CancellationToken cancellationToken = default);
      Task<bool> DisconnectAsync(CancellationToken cancellationToken = default);
  }
  ```

#### 3.3.2 SerialConnectionManager
- **命名空间**：`WaferAligner.SerialControl.Implementation`
- **功能**：实现ISerialConnectionManager接口
- **实现特点**：
  - 自动重连机制
  - 定时保活检测
  - 超时控制与异常处理
  - 开发模式支持
- **代码示例**：
  ```csharp
  public class SerialConnectionManager : ISerialConnectionManager, IDisposable
  {
      private readonly TimerWrapper _keepAliveTimer;
      
      public SerialConnectionManager(
          ILoggingService loggingService, 
          ISerialComWrapper serialComWrapper,
          ResourceManager resourceManager)
      {
          // ...初始化代码
          
          // 创建并注册保活定时器
          _keepAliveTimer = new TimerWrapper(5000);
          _keepAliveTimer.AddElapsedHandler(OnKeepAliveTimer);
          _resourceManager.RegisterResource("SerialConnectionManager_KeepAliveTimer", _keepAliveTimer);
      }
      
      public async Task<bool> ConnectAsync(int comPort, int baudRate, CancellationToken cancellationToken = default)
      {
          // ...连接实现
          using (var cts = new CancellationTokenSource(5000)) // 5秒超时
          using (var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cts.Token, cancellationToken))
          {
              return await Task.Run(() => {
                  // ...连接逻辑
              }, linkedCts.Token);
          }
      }
      
      // ...其他方法实现
  }
  ```

### 3.4 控制器层

#### 3.4.1 ISerialAxisController
- **命名空间**：`WaferAligner.SerialControl.Interfaces`
- **功能**：定义轴控制器接口
- **关键属性和方法**：
  - `AxisName` - 轴名称
  - `IsConnected` - 连接状态
  - `EnableAxisAsync` - 异步使能
  - `GetPositionAsync` - 异步获取位置
  - `MoveToPositionAsync` - 异步移动到位置
  - `StopAsync` - 异步停止
- **接口定义**：
  ```csharp
  public interface ISerialAxisController : IDisposable
  {
      string AxisName { get; }
      bool IsConnected { get; }
      
      event EventHandler<SerialAxisEventArgs> PositionChanged;
      event EventHandler<SerialAxisEventArgs> StateChanged;
      
      Task<bool> EnableAxisAsync(CancellationToken cancellationToken = default);
      Task<int> GetPositionAsync(CancellationToken cancellationToken = default);
      Task<bool> MoveToPositionAsync(int position, bool isRelative = false, CancellationToken cancellationToken = default);
      Task<bool> StopAsync(CancellationToken cancellationToken = default);
      Task<bool> HomeAsync(CancellationToken cancellationToken = default);
      Task<bool> ClearErrorAsync(CancellationToken cancellationToken = default);
      Task<int> GetRunStateAsync(CancellationToken cancellationToken = default);
      Task<int> GetAlarmStateAsync(CancellationToken cancellationToken = default);
  }
  ```

#### 3.4.2 SerialAxisController
- **命名空间**：`WaferAligner.SerialControl.Implementation`
- **功能**：实现ISerialAxisController接口
- **实现特点**：
  - 智能日志抑制机制
  - 事件通知和事件总线集成
  - 异步操作与取消支持
  - 轴地址自动映射
- **代码示例**：
  ```csharp
  public class SerialAxisController : ISerialAxisController, IDisposable
  {
      private readonly uint _axisAddress;
      
      public SerialAxisController(
          string axisName, 
          ILoggingService loggingService,
          ISerialConnectionManager serialConnectionManager,
          ISerialComWrapper serialComWrapper,
          IEventBus eventBus)
      {
          // ...初始化代码
          
          // 根据轴名称设置地址
          _axisAddress = _axisName switch
          {
              "X" => 2,
              "Y" => 1,
              "R" => 3,
              _ => throw new ArgumentException($"不支持的轴名称: {_axisName}", nameof(axisName))
          };
      }
      
      public async Task<bool> MoveToPositionAsync(int position, bool isRelative = default, CancellationToken cancellationToken = default)
      {
          if (!CheckConnectionWithLogSuppression("移动")) return false;
          
          try
          {
              return await Task.Run(() => 
              {
                  // 发布移动开始事件
                  _eventBus.Publish(new SerialAxisMovingEvent(_axisName, position, isRelative));
                  
                  int result;
                  if (isRelative) {
                      result = _serialComWrapper.PositionRelativeMove(_axisAddress, position);
                  } else {
                      result = _serialComWrapper.PositionAbsoluteMove(_axisAddress, position);
                  }
                  
                  // ...处理结果和日志
                  return result == 1;
              }, cancellationToken);
          }
          catch (Exception ex)
          {
              // ...异常处理
              return false;
          }
      }
      
      // ...其他方法实现
  }
  ```

### 3.5 视图模型层

#### 3.5.1 SerialAxisViewModel
- **命名空间**：`WaferAligner.SerialControl.Models`
- **功能**：实现IXyrAxisViewModel接口，支持UI绑定
- **实现特点**：
  - 使用ISerialAxisController作为后端
  - 兼容XyrAxisViewModel的API
  - 状态更新节流机制
  - 日志抑制智能化
  - Observable属性支持UI绑定
- **代码示例**：
  ```csharp
  public partial class SerialAxisViewModel : ObservableObject, IXyrAxisViewModel
  {
      private readonly ISerialAxisController _controller;
      private const int PositionMultiplier = 10500; // 位置单位转换乘数
      
      // 添加状态更新节流相关字段
      private DateTime _lastStatusUpdateTime = DateTime.MinValue;
      private const int MinStatusUpdateIntervalMs = 500; // 最小状态更新间隔
      
      // 添加移动操作日志抑制相关字段
      private DateTime _lastMoveFailLogTime = DateTime.MinValue;
      private int _moveFailLogCount = 0;
      
      [ObservableProperty]
      private bool _isConnected;
      
      [ObservableProperty]
      private bool _isReady;
      
      // ...其他属性
      
      public SerialAxisViewModel(ISerialAxisController controller, ILoggingService loggingService)
      {
          _controller = controller ?? throw new ArgumentNullException(nameof(controller));
          _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
          
          // 初始化属性
          AxisName = controller.AxisName;
          IsConnected = controller.IsConnected;
          
          // 注册事件处理
          _controller.PositionChanged += OnControllerPositionChanged;
          _controller.StateChanged += OnControllerStateChanged;
          
          // 初始化状态
          _ = UpdateStatusAsync();
      }
      
      // ...其他方法实现
  }
  ```

## 四、通信流程详解

### 4.1 串口连接流程

1. **初始化阶段**
   - UI页面通过AxisViewModelFactory获取SerialAxisViewModel实例
   - SerialAxisViewModel通过SerialAxisController实现轴控制
   - SerialConnectionManager初始化串口连接

2. **连接建立阶段**
   - SerialConnectionManager调用SerialComWrapper.OpenComPort打开串口
   - 设置控制轴、参数配置
   - 连接成功后发布ConnectionStateChanged事件

3. **连接维护阶段**
   - 定时器定期调用KeepAliveAsync检查连接状态
   - 如果连接断开，自动尝试重连

4. **连接释放阶段**
   - 调用DisconnectAsync断开连接
   - 清理资源，释放定时器

### 4.2 轴控制流程

1. **轴使能**
   - UI触发EnableAxisAsync请求
   - SerialAxisController验证连接状态
   - 调用SerialComWrapper.AxisEnable进行使能
   - 等待使能完成，发布StateChanged事件

2. **位置读取**
   - 定时或按需调用GetPositionAsync
   - SerialComWrapper.ReadPosition读取位置
   - 发布PositionChanged事件更新UI

3. **位置移动**
   - UI触发MoveToPositionAsync请求
   - SerialAxisController调用适当的移动方法
   - 移动开始后，通过事件机制更新UI状态
   - 周期性读取位置更新移动状态

4. **错误处理**
   - 检测错误状态，发布异常事件
   - 自动尝试恢复或通知用户

### 4.3 通信序列图

```
┌───────────┐        ┌─────────────────┐        ┌─────────────────────┐        ┌───────────────┐        ┌────────────┐
│   UI页面   │        │SerialAxisViewModel│        │ SerialAxisController │        │SerialComWrapper│        │SerialCom.dll│
└─────┬─────┘        └────────┬────────┘        └─────────┬───────────┘        └───────┬───────┘        └─────┬──────┘
      │                       │                           │                            │                       │
      │ 1. EnableAxis调用     │                           │                            │                       │
      │──────────────────────►│                           │                            │                       │
      │                       │                           │                            │                       │
      │                       │ 2. EnableAxisAsync调用    │                           │                       │
      │                       │───────────────────────────►                            │                       │
      │                       │                           │                            │                       │
      │                       │                           │ 3. 检查连接状态            │                       │
      │                       │                           │─────────────────┐          │                       │
      │                       │                           │                 │          │                       │
      │                       │                           │◄────────────────┘          │                       │
      │                       │                           │                            │                       │
      │                       │                           │ 4. AxisEnable调用          │                       │
      │                       │                           │───────────────────────────►│                       │
      │                       │                           │                            │                       │
      │                       │                           │                            │ 5. DLL_AxisEnable调用 │
      │                       │                           │                            │──────────────────────►│
      │                       │                           │                            │                       │
      │                       │                           │                            │◄──────────────────────│
      │                       │                           │                            │ 6. 返回结果          │
      │                       │                           │◄───────────────────────────┤                       │
      │                       │                           │ 7. 返回成功状态            │                       │
      │                       │                           │                            │                       │
      │                       │                           │ 8. 发布StateChanged事件    │                       │
      │                       │◄───────────────────────────┤                           │                       │
      │                       │                           │                            │                       │
      │                       │ 9. UI属性更新             │                           │                       │
      │◄──────────────────────┤                           │                            │                       │
      │                       │                           │                            │                       │
      │ 10. UI刷新显示        │                           │                            │                       │
      │─────────────┐         │                           │                            │                       │
      │            │         │                           │                            │                       │
      │◄────────────┘         │                           │                            │                       │
      │                       │                           │                            │                       │
```

## 五、核心机制

### 5.1 日志抑制机制

重构后的串口通信实现了智能化的日志抑制机制，避免在连接失败或操作失败时产生大量重复警告日志：

1. **连接日志抑制**
   - SerialAxisController中实现CheckConnectionWithLogSuppression方法
   - 连接失败日志在达到阈值后会开始抑制
   - 按时间间隔（默认30秒）周期性记录汇总警告
   - 代码示例：
     ```csharp
     private bool CheckConnectionWithLogSuppression(string operationName)
     {
         if (IsConnected) {
             _connectionWarningCount = 0;
             return true;
         }
         
         DateTime now = DateTime.Now;
         bool shouldLog = false;
         
         if (_connectionWarningCount < MaxConnectionWarningCount) {
             shouldLog = true;
             _connectionWarningCount++;
         }
         else if ((now - _lastConnectionWarningTime).TotalSeconds >= ConnectionWarningIntervalSeconds) {
             shouldLog = true;
             _connectionWarningCount = 1;
         }
         
         if (shouldLog) {
             _lastConnectionWarningTime = now;
             // 记录警告日志...
         }
         
         return false;
     }
     ```

2. **移动失败日志抑制**
   - SerialAxisViewModel中实现ShouldLogMoveFailure方法
   - 使用计数器和时间间隔控制日志频率
   - 在已连接状态下始终记录日志
   - 只抑制未连接状态下的重复失败日志

### 5.2 状态更新节流机制

为避免过于频繁的状态更新导致UI卡顿，实现了节流机制：

1. **状态更新节流**
   - SerialAxisViewModel中实现UpdateStatusWithThrottleAsync方法
   - 使用最小更新间隔（默认500毫秒）控制更新频率
   - 使用锁机制确保线程安全
   - 代码示例：
     ```csharp
     private async Task UpdateStatusWithThrottleAsync()
     {
         DateTime now = DateTime.Now;
         bool shouldUpdate = false;
         
         lock (_updateLock)
         {
             if (!_isUpdatingStatus && 
                 (now - _lastStatusUpdateTime).TotalMilliseconds >= MinStatusUpdateIntervalMs)
             {
                 _isUpdatingStatus = true;
                 shouldUpdate = true;
             }
         }
         
         if (shouldUpdate)
         {
             try {
                 await UpdateStatusAsync();
             }
             finally {
                 lock (_updateLock)
                 {
                     _lastStatusUpdateTime = DateTime.Now;
                     _isUpdatingStatus = false;
                 }
             }
         }
     }
     ```

### 5.3 事件通知双通道机制

重构后的串口通信同时支持传统事件和事件总线两种通知机制：

1. **传统事件机制**
   - 定义PositionChanged和StateChanged事件
   - 向上兼容原有代码

2. **事件总线机制**
   - 使用IEventBus发布事件
   - 支持松耦合的事件订阅
   - 代码示例：
     ```csharp
     private void RaisePositionChanged(int position)
     {
         try
         {
             // 发布事件总线事件
             _eventBus.Publish(new SerialAxisPositionChangedEvent(_axisName, position));
             
             // 触发传统事件
             PositionChanged?.Invoke(this, new SerialAxisEventArgs { 
                 AxisName = _axisName,
                 Position = position
             });
         }
         catch (Exception ex)
         {
             _loggingService.LogError(ex, $"发布{_axisName}轴位置变化事件异常", EventIds.SerialAxisOperation);
         }
     }
     ```

### 5.4 异步操作与资源管理

重构后的串口通信全面支持异步操作和资源安全管理：

1. **全面异步支持**
   - 所有阻塞操作都实现了异步API
   - 支持取消令牌（CancellationToken）
   - 超时保护机制

2. **资源安全管理**
   - 使用ResourceManager注册资源
   - 实现IDisposable接口确保资源释放
   - 自定义清理逻辑

## 六、与PLC通信的差异

串口通信与PLC通信存在以下主要差异：

### 6.1 通信协议差异
- **串口通信**：使用自定义协议，通过SerialCom.dll实现
- **PLC通信**：使用ModbusTCP协议，通过StandardModbusApi.dll实现

### 6.2 架构模式差异
- **串口通信**：直接命令-响应模式，同步读写
- **PLC通信**：轮询与事件混合模式，变量监控

### 6.3 事件处理差异
- **串口通信**：主动触发事件，双通道通知（传统事件+事件总线）
- **PLC通信**：被动监听变量变化，只有回调事件

### 6.4 连接管理差异
- **串口通信**：单一连接管理，焦点在稳定性
- **PLC通信**：多连接管理，需要处理连接池和路由

## 七、优化建议

### 7.1 性能优化

1. **轴状态缓存机制**
   - 实现轴状态缓存，避免频繁读取
   - 根据变化频率动态调整读取间隔
   - 实现变化检测，仅在状态变化时更新

2. **批量操作支持**
   - 实现多轴同步移动命令
   - 批量状态读取优化

### 7.2 可靠性增强

1. **连接恢复增强**
   - 实现更智能的连接重试策略
   - 添加断线重连后的状态恢复机制

2. **错误处理增强**
   - 更细粒度的错误分类
   - 错误自动恢复策略

### 7.3 测试与监控

1. **模拟测试模式**
   - 增强开发模式模拟能力
   - 添加随机延迟和错误注入

2. **性能监控**
   - 添加通信延迟统计
   - 操作成功率监控

## 八、结论

WaferAligner项目串口通信经过重构，已经形成了清晰、分层、可扩展的架构。通过接口抽象和依赖注入，实现了与PLC通信的完全分离，并保持了良好的向后兼容性。重构后的串口通信支持全面的异步操作，实现了智能日志抑制和状态更新节流，大幅提升了系统的响应性和稳定性。

从架构上看，新的串口通信系统与PLC通信系统完全解耦，符合单一职责原则，便于后续各自独立优化和扩展。后续可以进一步考虑性能优化、可靠性增强和监控能力提升。

**文档版本**：1.0  
**创建日期**：2025年07月28日  
**适用范围**：WaferAligner项目 Phase 3重构计划 