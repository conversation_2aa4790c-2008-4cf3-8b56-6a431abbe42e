# 用户管理与权限专项合并版

## 一、系统功能概述

WaferAligner系统实现了基于角色的权限管理功能，用于控制不同用户对系统功能的访问权限。本专项文档整合了用户管理操作手册、用户角色权限管理说明等内容，全面记录了用户管理与权限系统的设计、实现和使用方法。

### 1.1 支持的角色类型

系统支持三种角色：

- **管理员（Admin）**：拥有所有系统权限，无任何限制
- **工程师（Engineer）**：拥有大部分系统权限，包括用户管理和参数配置
- **操作员（Operator）**：只能访问基本操作功能，不能访问用户管理页面

### 1.2 系统架构

#### 1.2.1 用户管理服务层
- **IUserManagement**：用户管理服务接口
- **UserManagementService**：用户管理服务实现
- **JsonStorageService**：用户数据存储服务

#### 1.2.2 权限模型
- **UserInfo**：用户信息模型
- **Role**：角色模型
- **Permission**：权限模型

#### 1.2.3 前端权限控制
- **CurrentUser**：当前用户信息管理
- **FHeaderMainFooter**：主窗体权限控制
- **FTitlePage5**：用户管理页面权限验证

## 二、角色与权限设计

### 2.1 默认角色和权限

#### 2.1.1 管理员角色（Admin）
- 权限：`*`（所有权限）
- 功能：可以访问所有页面和功能，包括用户管理
- 标识：用户列表中显示为红色文字

#### 2.1.2 工程师角色（Engineer）
- 权限：
  - `system.view`：查看系统信息
  - `system.config`：系统配置
  - `recipe.view`：查看配方
  - `recipe.edit`：编辑配方
  - `process.view`：查看工艺
  - `process.control`：工艺控制
  - `data.view`：查看数据
  - `data.export`：导出数据
  - `user.management`：用户管理页面访问
  - `parameter.config`：对准参数配置页面访问
  - `motion.config`：运动参数配置页面访问
- 功能：可以访问大部分功能，包括用户管理和参数配置
- 标识：用户列表中显示为蓝色文字

#### 2.1.3 操作员角色（Operator）
- 权限：
  - `system.view`：查看系统信息
  - `process.view`：查看工艺
  - `process.control`：工艺控制
- 功能：只能访问键合对准页面，**不能访问对准参数、运动参数和用户管理页面**
- 标识：用户列表中显示为正常黑色文字

### 2.2 默认用户账户

系统会自动创建以下默认用户账户：

#### 2.2.1 管理员账户
- **用户名**：Admin
- **密码**：123456
- **角色**：管理员
- **权限**：所有权限

#### 2.2.2 操作员账户
- **用户名**：Operator
- **密码**：123456
- **角色**：操作员
- **权限**：基本操作权限（不包括用户管理）

## 三、权限控制实现

### 3.1 页面级别控制

在`FHeaderMainFooter.cs`中，根据用户权限动态添加页面：

```csharp
// 根据用户权限决定是否添加对准参数页面
if (CurrentUser.CanConfigParameters())
{
    AddPage(FTP2, 1002);
    Header.CreateNode("对准参数", 1002);
}

// 根据用户权限决定是否添加运动参数页面
if (CurrentUser.CanConfigMotion())
{
    AddPage(FTP3, 1003);
    Header.CreateNode("运动参数", 1003);
}

// 根据用户权限决定是否添加用户管理页面
if (CurrentUser.CanManageUsers())
{
    AddPage(FTP5, 1005);
    Header.CreateNode("用户管理", 1005);
}
```

### 3.2 页面内部验证

在页面加载时进行权限验证：

```csharp
// FTitlePage2 对准参数页面权限检查
if (!CurrentUser.CanConfigParameters())
{
    MessageBox.Show("您没有权限访问对准参数配置页面。\n\n只有管理员和工程师可以配置参数。", 
        "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning);
    this.Enabled = false;
    return;
}

// FTitlePage3 运动参数页面权限检查
if (!CurrentUser.CanConfigMotion())
{
    MessageBox.Show("您没有权限访问运动参数配置页面。\n\n只有管理员和工程师可以配置参数。", 
        "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning);
    this.Enabled = false;
    return;
}

// FTitlePage5 用户管理页面权限检查
if (!CurrentUser.CanManageUsers())
{
    MessageBox.Show("您没有权限访问用户管理页面。\n\n只有管理员和工程师可以管理用户。", 
        "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning);
    this.Enabled = false;
    return;
}
```

### 3.3 功能级别控制

在各功能点实现权限检查：

```csharp
// 功能级别权限检查示例
public void SaveParameters()
{
    // 检查是否有保存参数的权限
    if (!CurrentUser.HasPermission("parameter.edit"))
    {
        MessageBox.Show("您没有权限保存参数设置。", "权限不足", 
            MessageBoxButtons.OK, MessageBoxIcon.Warning);
        return;
    }
    
    // 执行保存操作
    // ...
}
```

## 四、用户管理界面功能

### 4.1 访问用户管理

#### 4.1.1 登录要求
- 使用管理员或工程师账户登录系统
- 默认管理员账户：用户名 `Admin`，密码 `123456`

#### 4.1.2 打开用户管理页面
1. 成功登录后，在主界面顶部找到"用户管理"选项卡
2. 点击"用户管理"选项卡进入用户管理页面

### 4.2 用户管理页面 (FTitlePage5)

用户管理页面提供完整的用户管理功能，包括：

#### 4.2.1 用户列表显示
- 显示所有用户的详细信息：
  - 用户名、角色、描述、状态
  - 最后登录时间、创建时间
- 用户状态可视化显示：
  - 管理员：红色文字
  - 工程师：蓝色文字
  - 禁用用户：灰色文字
- 实时显示用户统计信息：
  - 总用户数、活跃用户数、各角色数量

#### 4.2.2 用户操作功能
1. **新增用户**
   - 点击"添加"按钮或使用快捷键打开新增用户对话框
   - 用户名验证（3-20字符，只允许字母、数字、下划线）
   - 密码强度检查（6-50字符，至少包含一个字母和数字）
   - 重复密码确认
   - 角色选择（管理员、工程师、操作员）
   - 用户描述（可选）
   - 实时检查用户名是否已存在

2. **编辑用户**
   - 双击用户行可切换用户启用/禁用状态
   - 右键菜单提供编辑、删除、刷新选项

3. **删除用户**
   - 点击"删除"按钮或右键菜单删除选中用户
   - 不能删除当前登录的用户
   - 删除前会显示确认对话框

4. **搜索功能**
   - 使用快捷键 Ctrl+F 打开搜索对话框
   - 支持按用户名、描述、角色搜索
   - 搜索结果实时显示，支持模糊匹配

5. **快捷键支持**
   - Ctrl+F：打开搜索功能
   - F5：刷新用户列表

#### 4.2.3 新增用户对话框 (FAddUserDialog)
专业的用户添加界面，提供：
- 用户名输入和实时验证
- 密码输入和强度提示
- 重复密码确认
- 角色选择下拉框
- 用户描述文本框
- 实时显示用户名可用性
- 角色权限说明显示
- 完整的输入验证和错误提示

## 五、用户管理操作指南

### 5.1 查看用户列表
用户管理页面显示所有系统用户的信息：
- **用户名**：用户的登录名
- **角色**：用户的权限角色（管理员、工程师、操作员）
- **描述**：用户的详细说明
- **状态**：用户是否处于活跃状态
- **最后登录时间**：用户最近一次登录的时间
- **创建时间**：用户账户的创建时间

### 5.2 新增用户操作步骤

1. 以管理员或工程师身份登录系统
2. 点击"用户管理"选项卡
3. 点击"添加"按钮
4. 在弹出的对话框中填写以下信息：
   - **用户名**：3-20个字符，只能包含字母、数字和下划线
   - **密码**：6-50个字符，必须包含至少一个字母和一个数字
   - **确认密码**：重新输入密码进行确认
   - **角色**：从下拉菜单选择用户角色
   - **描述**：可选，输入用户的详细描述
5. 点击"确定"按钮完成用户创建

**注意事项**：
- 用户名不能重复
- 密码会自动进行强度检查
- 对话框会实时显示输入验证结果

### 5.3 管理现有用户

**启用/禁用用户**：
- 双击任意用户行可以切换该用户的启用/禁用状态
- 禁用的用户无法登录系统，显示为灰色文字

**删除用户**：
1. 选中要删除的用户行
2. 点击"删除"按钮
3. 在确认对话框中点击"是"确认删除
4. **注意**：不能删除当前登录的用户

**右键菜单**：
在用户列表中点击右键可以访问快捷菜单：
- **编辑**：切换用户状态
- **删除**：删除选中用户
- **刷新**：重新加载用户列表

### 5.4 搜索用户

**快速搜索**：
1. 按下 `Ctrl+F` 组合键
2. 在弹出的输入框中输入搜索关键词
3. 系统会自动筛选匹配的用户

**搜索范围**：
- 用户名
- 用户描述
- 用户角色

## 六、界面信息说明

### 6.1 状态栏信息
页面标题栏显示实时统计信息：
- 总用户数
- 活跃用户数
- 禁用用户数
- 各角色用户数量

### 6.2 颜色标识
- **红色文字**：管理员用户
- **蓝色文字**：工程师用户
- **黑色文字**：操作员用户
- **灰色文字**：禁用用户

## 七、扩展功能

### 7.1 添加新权限
1. 在`JsonStorageService.cs`的`GetDefaultPermissions()`方法中添加新权限
2. 在相应角色的权限列表中添加该权限
3. 在需要权限控制的页面或功能中使用`CurrentUser.HasPermission()`进行验证

### 7.2 添加新角色
1. 在`JsonStorageService.cs`的`GetDefaultRoles()`方法中添加新角色
2. 定义该角色的权限列表
3. 更新用户管理界面，在角色下拉框中添加新角色选项
4. 在权限检查逻辑中处理新角色

## 八、测试方法

### 8.1 管理员登录测试
1. 使用账户：Admin / 123456 登录
2. 验证可以看到所有页面选项卡（键合对准、对准参数、运动参数、用户管理）
3. 点击"用户管理"页面，验证可以正常访问
4. 测试用户管理功能：
   - 添加新用户
   - 编辑用户状态
   - 删除用户
   - 搜索用户

### 8.2 工程师登录测试
1. 创建一个工程师账户（如果没有的话）
2. 以工程师身份登录
3. 验证可以访问用户管理、对准参数、运动参数页面
4. 测试参数配置和用户管理功能

### 8.3 操作员登录测试
1. 使用账户：Operator / 123456 登录
2. 验证**只能**看到"键合对准"页面选项卡
3. 验证**不能**看到"对准参数"、"运动参数"、"用户管理"页面选项卡
4. 尝试通过其他方式访问受限页面，验证会被阻止

## 九、常见问题

### Q: 忘记了管理员密码怎么办？
A: 请联系系统管理员重置密码，或查看系统配置文件。

### Q: 为什么不能删除某个用户？
A: 不能删除当前登录的用户。请使用其他管理员账户删除该用户。

### Q: 操作员能看到用户管理页面吗？
A: 不能。只有管理员和工程师角色才能访问用户管理功能。

### Q: 如何批量管理用户？
A: 当前版本只支持单个用户操作。批量操作功能将在后续版本中提供。

### Q: 用户数据存储在哪里？
A: 用户数据以JSON格式存储在系统的Users文件夹中。

## 十、安全提醒

1. **定期更换密码**：建议定期更换管理员密码
2. **最小权限原则**：根据实际需要分配用户角色
3. **及时禁用账户**：对于不再使用的账户，及时禁用而不是删除
4. **密码强度**：确保所有用户使用强密码

## 十一、后续优化建议

1. **多因素认证**：为管理员账户添加多因素认证
2. **密码策略**：实现密码复杂度要求和定期更换策略
3. **登录尝试限制**：添加登录失败次数限制，防止暴力破解
4. **操作审计**：记录用户的关键操作，便于安全审计
5. **权限细分**：进一步细化权限控制，实现更精细的功能访问控制 