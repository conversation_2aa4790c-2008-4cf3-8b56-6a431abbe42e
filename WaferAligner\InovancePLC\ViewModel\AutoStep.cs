﻿using CommunityToolkit.Mvvm.ComponentModel;
using System.Windows;

namespace AlignerUI
{
    public partial class MainWindowViewModel
    {

        [ObservableProperty]
        string autoStepName = string.Empty;
        [ObservableProperty]
        string initStepName = string.Empty;
        [ObservableProperty]
        string visionCalStepName = string.Empty;
        private string GetCraftDescription(int step) 
        {
            var desc = autoStepDescriptions.Find(x => x.Step == step);
            if (desc == null)
            {
                return string.Empty;
            }
            else
            {
                return desc.Description;
            }
        }
        private string GetInitSteptDescription(int step)
        {
            var desc = initStepDescriptions.Find(x => x.Step == step);
            if (desc == null)
            {
                return string.Empty;
            }
            else
            {
                return desc.Description;
            }
        }
        private string GetVisionCalStepDescription(int step)
        {
            var desc = visionCalStepDescriptions.Find(x => x.Step == step);
            if (desc == null)
            {
                return string.Empty;
            }
            else
            {
                return desc.Description;
            }
        }

        async void ManualCheckAction(int step) 
        {
            Func<Task> action = step switch
            {
                10 => async () => await ShowDescription("请放置卡盘，并在界面确认卡盘放置OK"),
                100 => async () => await ShowDescription("请放置下晶圆，并开启下晶圆真空吸附，并在界面确认下晶圆放置OK"),
                1000 => async () => await ShowDescription("请放置上晶圆，并开启上晶圆真空吸附，并在界面确认上晶圆放置OK"),
                3000 => async () => await ResetAll(),
                _ => () => Task.CompletedTask,
            };
            await action();
        }
        Task ResetAll() 
        {
            AutoStepNumber = 0;
            AutoStepName = string.Empty;
            return Task.CompletedTask;
        }
        Task ShowDescription(string message) 
        {
            MessageBox.Show(message,"操作提示", MessageBoxButtons.OK);
            return Task.CompletedTask;
        }
    }
}
