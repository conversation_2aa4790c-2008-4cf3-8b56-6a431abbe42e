using AlignerUI;
using System.Threading.Tasks;

namespace WaferAligner.Services
{
    /// <summary>
    /// 对准机参数服务接口
    /// 提供对AlignerPara的访问和操作，替代对ConstValue.ALIGNERPARA的直接访问
    /// </summary>
    public interface IAlignerParaService
    {
        /// <summary>
        /// 获取当前AlignerPara实例
        /// </summary>
        AlignerPara Current { get; }

        /// <summary>
        /// 更新AlignerPara实例
        /// </summary>
        /// <param name="alignerPara">新的AlignerPara实例</param>
        Task UpdateAsync(AlignerPara alignerPara);

        /// <summary>
        /// 从配方服务同步参数
        /// </summary>
        /// <param name="recipeService">配方服务</param>
        Task SyncFromRecipeServiceAsync(IRecipeService recipeService);

        /// <summary>
        /// 同步参数到配方服务
        /// </summary>
        /// <param name="recipeService">配方服务</param>
        Task SyncToRecipeServiceAsync(IRecipeService recipeService);
    }
} 