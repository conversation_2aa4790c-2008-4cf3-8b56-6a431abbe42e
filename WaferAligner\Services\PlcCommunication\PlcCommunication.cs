﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Aya.PLC.Base;
using JYJ001.App.Services.Common.Interfaces;
using JYJ001.App.Services.Common.Extension;
using WaferAligner.Interfaces;
using WaferAligner.EventIds;

namespace WaferAligner.Services.PlcCommunication
{
    /// <summary>
    /// PLC通信实现，使用已有的IPlcConnectionManager完成与PLC的交互
    /// </summary>
    public class PlcCommunication : IPlcCommunication
    {
        private readonly IPlcConnectionManager _plcConnectionManager;
        private readonly ILoggingService _loggingService;
        
        // PLC变量名映射表
        private readonly Dictionary<string, AxisPlcVariables> _axisVarMap;
        
        /// <summary>
        /// 获取PLC是否已连接
        /// </summary>
        public bool IsConnected => _plcConnectionManager?.IsConnected("ZAxis") ?? false;
        
        public PlcCommunication(
            IPlcConnectionManager plcConnectionManager,
            ILoggingService loggingService)
        {
            _plcConnectionManager = plcConnectionManager ?? throw new ArgumentNullException(nameof(plcConnectionManager));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            
            // 初始化PLC变量映射表
            _axisVarMap = InitializeAxisVariableMap();
        }
        
        /// <summary>
        /// 初始化轴PLC变量映射表
        /// </summary>
        private Dictionary<string, AxisPlcVariables> InitializeAxisVariableMap()
        {
            // 加载日志
            _loggingService.LogInformation("初始化轴PLC变量映射表", WaferAligner.EventIds.EventIds.Resource_Registered);
            
            var map = new Dictionary<string, AxisPlcVariables>
            {
                // Z轴
                ["Z"] = new AxisPlcVariables
                {
                    ConnectionName = "ZAxis",
                    PositionVar = $"{AxisConstants.AXIS_GVL}.ZRealDistance", // 实际位置
                    TargetPositionVar = $"{AxisConstants.AXIS_GVL}.ZTarget", // 目标位置
                    MoveExecuteVar = $"{AxisConstants.AXIS_GVL}.ZExecute", // 执行移动
                    MoveActionVar = $"{AxisConstants.AXIS_GVL}.ZAxisActions", // 移动动作类型
                    HomeVar = $"{AxisConstants.AXIS_GVL}.ZHomeExecute", // 回零
                    StopVar = $"{AxisConstants.AXIS_GVL}.ZAxisActions", // 停止动作
                    ResetVar = $"{AxisConstants.AXIS_GVL}.ZAxisActions", // 复位动作
                    StatusVar = $"{AxisConstants.AXIS_GVL}.ZRealStatus", // 状态
                    HomedVar = $"{AxisConstants.AXIS_GVL}.ZIsHomed", // 是否已回零
                    EnabledVar = $"{AxisConstants.AXIS_GVL}.ZAxisEnabled", // 是否已使能
                    AlarmVar = $"{AxisConstants.AXIS_GVL}.ZAxisAlarm", // 是否有报警
                    JogSpeedVar = $"{AxisConstants.AXIS_GVL}.ZJogVelo", // 点动速度
                    RunSpeedVar = $"{AxisConstants.AXIS_GVL}.ZVelo", // 运行速度
                    PositionStateVar = $"{AxisConstants.AXIS_GVL}.ZPositionState", // 位置状态
                },
                
                // 也添加ZAxis别名以确保向后兼容
                ["ZAxis"] = new AxisPlcVariables
                {
                    ConnectionName = "ZAxis",
                    PositionVar = $"{AxisConstants.AXIS_GVL}.ZRealDistance",
                    TargetPositionVar = $"{AxisConstants.AXIS_GVL}.ZTarget",
                    MoveExecuteVar = $"{AxisConstants.AXIS_GVL}.ZExecute",
                    MoveActionVar = $"{AxisConstants.AXIS_GVL}.ZAxisActions",
                    HomeVar = $"{AxisConstants.AXIS_GVL}.ZHomeExecute",
                    StopVar = $"{AxisConstants.AXIS_GVL}.ZAxisActions",
                    ResetVar = $"{AxisConstants.AXIS_GVL}.ZAxisActions",
                    StatusVar = $"{AxisConstants.AXIS_GVL}.ZRealStatus",
                    HomedVar = $"{AxisConstants.AXIS_GVL}.ZIsHomed",
                    EnabledVar = $"{AxisConstants.AXIS_GVL}.ZAxisEnabled",
                    AlarmVar = $"{AxisConstants.AXIS_GVL}.ZAxisAlarm",
                    JogSpeedVar = $"{AxisConstants.AXIS_GVL}.ZJogVelo",
                    RunSpeedVar = $"{AxisConstants.AXIS_GVL}.ZVelo",
                    PositionStateVar = $"{AxisConstants.AXIS_GVL}.ZPositionState",
                },
                
                // 左相机X轴
                ["LX"] = new AxisPlcVariables
                {
                    ConnectionName = "LeftX",
                    PositionVar = $"{AxisConstants.AXIS_GVL}.LXRealDistance",
                    TargetPositionVar = $"{AxisConstants.AXIS_GVL}.LXTarget",
                    MoveExecuteVar = $"{AxisConstants.AXIS_GVL}.LXExecute",
                    MoveActionVar = $"{AxisConstants.AXIS_GVL}.LXAxisActions",
                    HomeVar = $"{AxisConstants.AXIS_GVL}.LXHomeExecute",
                    StopVar = $"{AxisConstants.AXIS_GVL}.LXAxisActions",
                    ResetVar = $"{AxisConstants.AXIS_GVL}.LXAxisActions",
                    StatusVar = $"{AxisConstants.AXIS_GVL}.LXRealStatus",
                    HomedVar = $"{AxisConstants.AXIS_GVL}.LXIsHomed",
                    EnabledVar = $"{AxisConstants.AXIS_GVL}.LXAxisEnabled",
                    AlarmVar = $"{AxisConstants.AXIS_GVL}.LXAxisAlarm",
                    JogSpeedVar = $"{AxisConstants.AXIS_GVL}.LXJogVelo",
                    RunSpeedVar = $"{AxisConstants.AXIS_GVL}.LXVelo",
                    PositionStateVar = $"{AxisConstants.AXIS_GVL}.LXPositionState",
                },
                
                // 左相机Y轴
                ["LY"] = new AxisPlcVariables
                {
                    ConnectionName = "LeftY",
                    PositionVar = $"{AxisConstants.AXIS_GVL}.LYRealDistance",
                    TargetPositionVar = $"{AxisConstants.AXIS_GVL}.LYTarget",
                    MoveExecuteVar = $"{AxisConstants.AXIS_GVL}.LYExecute",
                    MoveActionVar = $"{AxisConstants.AXIS_GVL}.LYAxisActions",
                    HomeVar = $"{AxisConstants.AXIS_GVL}.LYHomeExecute",
                    StopVar = $"{AxisConstants.AXIS_GVL}.LYAxisActions",
                    ResetVar = $"{AxisConstants.AXIS_GVL}.LYAxisActions",
                    StatusVar = $"{AxisConstants.AXIS_GVL}.LYRealStatus",
                    HomedVar = $"{AxisConstants.AXIS_GVL}.LYIsHomed",
                    EnabledVar = $"{AxisConstants.AXIS_GVL}.LYAxisEnabled",
                    AlarmVar = $"{AxisConstants.AXIS_GVL}.LYAxisAlarm",
                    JogSpeedVar = $"{AxisConstants.AXIS_GVL}.LYJogVelo",
                    RunSpeedVar = $"{AxisConstants.AXIS_GVL}.LYVelo",
                    PositionStateVar = $"{AxisConstants.AXIS_GVL}.LYPositionState",
                },
                
                // 左相机Z轴
                ["LZ"] = new AxisPlcVariables
                {
                    ConnectionName = "LeftZ",
                    PositionVar = $"{AxisConstants.AXIS_GVL}.LZRealDistance",
                    TargetPositionVar = $"{AxisConstants.AXIS_GVL}.LZTarget",
                    MoveExecuteVar = $"{AxisConstants.AXIS_GVL}.LZExecute",
                    MoveActionVar = $"{AxisConstants.AXIS_GVL}.LZAxisActions",
                    HomeVar = $"{AxisConstants.AXIS_GVL}.LZHomeExecute",
                    StopVar = $"{AxisConstants.AXIS_GVL}.LZAxisActions",
                    ResetVar = $"{AxisConstants.AXIS_GVL}.LZAxisActions",
                    StatusVar = $"{AxisConstants.AXIS_GVL}.LZRealStatus",
                    HomedVar = $"{AxisConstants.AXIS_GVL}.LZIsHomed",
                    EnabledVar = $"{AxisConstants.AXIS_GVL}.LZAxisEnabled",
                    AlarmVar = $"{AxisConstants.AXIS_GVL}.LZAxisAlarm",
                    JogSpeedVar = $"{AxisConstants.AXIS_GVL}.LZJogVelo",
                    RunSpeedVar = $"{AxisConstants.AXIS_GVL}.LZVelo",
                    PositionStateVar = $"{AxisConstants.AXIS_GVL}.LZPositionState",
                },
                
                // 右相机X轴
                ["RX"] = new AxisPlcVariables
                {
                    ConnectionName = "RightX",
                    PositionVar = $"{AxisConstants.AXIS_GVL}.RXRealDistance",
                    TargetPositionVar = $"{AxisConstants.AXIS_GVL}.RXTarget",
                    MoveExecuteVar = $"{AxisConstants.AXIS_GVL}.RXExecute",
                    MoveActionVar = $"{AxisConstants.AXIS_GVL}.RXAxisActions",
                    HomeVar = $"{AxisConstants.AXIS_GVL}.RXHomeExecute",
                    StopVar = $"{AxisConstants.AXIS_GVL}.RXAxisActions",
                    ResetVar = $"{AxisConstants.AXIS_GVL}.RXAxisActions",
                    StatusVar = $"{AxisConstants.AXIS_GVL}.RXRealStatus",
                    HomedVar = $"{AxisConstants.AXIS_GVL}.RXIsHomed",
                    EnabledVar = $"{AxisConstants.AXIS_GVL}.RXAxisEnabled",
                    AlarmVar = $"{AxisConstants.AXIS_GVL}.RXAxisAlarm",
                    JogSpeedVar = $"{AxisConstants.AXIS_GVL}.RXJogVelo",
                    RunSpeedVar = $"{AxisConstants.AXIS_GVL}.RXVelo",
                    PositionStateVar = $"{AxisConstants.AXIS_GVL}.RXPositionState",
                },
                
                // 右相机Y轴
                ["RY"] = new AxisPlcVariables
                {
                    ConnectionName = "RightY",
                    PositionVar = $"{AxisConstants.AXIS_GVL}.RYRealDistance",
                    TargetPositionVar = $"{AxisConstants.AXIS_GVL}.RYTarget",
                    MoveExecuteVar = $"{AxisConstants.AXIS_GVL}.RYExecute",
                    MoveActionVar = $"{AxisConstants.AXIS_GVL}.RYAxisActions",
                    HomeVar = $"{AxisConstants.AXIS_GVL}.RYHomeExecute",
                    StopVar = $"{AxisConstants.AXIS_GVL}.RYAxisActions",
                    ResetVar = $"{AxisConstants.AXIS_GVL}.RYAxisActions",
                    StatusVar = $"{AxisConstants.AXIS_GVL}.RYRealStatus",
                    HomedVar = $"{AxisConstants.AXIS_GVL}.RYIsHomed",
                    EnabledVar = $"{AxisConstants.AXIS_GVL}.RYAxisEnabled",
                    AlarmVar = $"{AxisConstants.AXIS_GVL}.RYAxisAlarm",
                    JogSpeedVar = $"{AxisConstants.AXIS_GVL}.RYJogVelo",
                    RunSpeedVar = $"{AxisConstants.AXIS_GVL}.RYVelo",
                    PositionStateVar = $"{AxisConstants.AXIS_GVL}.RYPositionState",
                },
                
                // 右相机Z轴
                ["RZ"] = new AxisPlcVariables
                {
                    ConnectionName = "RightZ",
                    PositionVar = $"{AxisConstants.AXIS_GVL}.RZRealDistance",
                    TargetPositionVar = $"{AxisConstants.AXIS_GVL}.RZTarget",
                    MoveExecuteVar = $"{AxisConstants.AXIS_GVL}.RZExecute",
                    MoveActionVar = $"{AxisConstants.AXIS_GVL}.RZAxisActions",
                    HomeVar = $"{AxisConstants.AXIS_GVL}.RZHomeExecute",
                    StopVar = $"{AxisConstants.AXIS_GVL}.RZAxisActions",
                    ResetVar = $"{AxisConstants.AXIS_GVL}.RZAxisActions",
                    StatusVar = $"{AxisConstants.AXIS_GVL}.RZRealStatus",
                    HomedVar = $"{AxisConstants.AXIS_GVL}.RZIsHomed",
                    EnabledVar = $"{AxisConstants.AXIS_GVL}.RZAxisEnabled",
                    AlarmVar = $"{AxisConstants.AXIS_GVL}.RZAxisAlarm",
                    JogSpeedVar = $"{AxisConstants.AXIS_GVL}.RZJogVelo",
                    RunSpeedVar = $"{AxisConstants.AXIS_GVL}.RZVelo",
                    PositionStateVar = $"{AxisConstants.AXIS_GVL}.RZPositionState",
                }
            };
            
            // 验证映射表的完整性
            _loggingService.LogInformation($"已配置{map.Count}个轴的PLC变量映射", WaferAligner.EventIds.EventIds.Resource_Registered);
            
            return map;
        }
        
        public async Task<int> GetPositionAsync(string axisName, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || !_axisVarMap.TryGetValue(axisName, out var axisVars))
            {
                _loggingService.LogWarning($"PLC未连接或找不到轴{axisName}的配置，无法获取位置", WaferAligner.EventIds.EventIds.Plc_Not_Connected);
                return 0;
            }
            
            var plc = _plcConnectionManager.GetPlcInstance(axisVars.ConnectionName);
            if (plc == null)
            {
                _loggingService.LogWarning($"获取{axisName}的PLC实例失败", WaferAligner.EventIds.EventIds.Plc_Instance_Null);
                return 0;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var posInfo = new PLCVarReadInfo { Name = axisVars.PositionVar, Type = typeof(double) };
                var result = await Task.Run(() => plc.ReadVariableAsync(posInfo, cancellationToken), cancellationToken);
                
                if (result is double doublePos)
                {
                    return (int)doublePos;
                }
                else if (result is int intPos)
                {
                    return intPos;
                }
                
                _loggingService.LogWarning($"获取{axisName}位置类型转换失败，返回值类型: {result?.GetType().Name ?? "null"}", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                return 0;
            }
            catch (OperationCanceledException)
            {
                _loggingService.LogWarning($"{axisName} 获取位置操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"读取轴{axisName}位置失败: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                return 0;
            }
        }
        
        public async Task<bool> MoveToPositionAsync(string axisName, int position, bool isRelative = false, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || !_axisVarMap.TryGetValue(axisName, out var axisVars))
            {
                _loggingService.LogWarning($"PLC未连接或找不到轴{axisName}的配置，无法移动", WaferAligner.EventIds.EventIds.Plc_Not_Connected);
                return false;
            }
            
            var plc = _plcConnectionManager.GetPlcInstance(axisVars.ConnectionName);
            if (plc == null)
            {
                _loggingService.LogWarning($"获取{axisName}的PLC实例失败", WaferAligner.EventIds.EventIds.Plc_Instance_Null);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                // 如果是相对运动，需要先获取当前位置
                if (isRelative)
                {
                    var currentPos = await GetPositionAsync(axisName, cancellationToken);
                    position = currentPos + position;
                }
                
                // 设置目标位置
                await Task.Run(() => plc.WriteVariableAsync(
                    new PLCVarWriteInfo { Name = axisVars.TargetPositionVar, Value = position },
                    cancellationToken), cancellationToken);
                
                // 短暂延迟确保目标位置写入，支持取消
                using var delayTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                await Task.Delay(100, delayTokenSource.Token);
                
                cancellationToken.ThrowIfCancellationRequested();
                // 设置移动类型（绝对移动）
                await Task.Run(() => plc.WriteVariableAsync(
                    new PLCVarWriteInfo { Name = axisVars.MoveActionVar, Value = 4 }, // 4表示绝对移动
                    cancellationToken), cancellationToken);
                
                // 短暂延迟确保移动类型写入，支持取消
                await Task.Delay(100, delayTokenSource.Token);
                
                cancellationToken.ThrowIfCancellationRequested();
                // 设置执行位为true，开始移动
                await Task.Run(() => plc.WriteVariableAsync(
                    new PLCVarWriteInfo { Name = axisVars.MoveExecuteVar, Value = true },
                    cancellationToken), cancellationToken);
                
                _loggingService.LogInformation($"{axisName}轴移动到位置{position}命令已发送", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                return true;
            }
            catch (OperationCanceledException)
            {
                _loggingService.LogWarning($"{axisName} 移动到位置 {position} 操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"轴{axisName}移动失败: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variable_Write_Failed);
                return false;
            }
        }
        
        public async Task<bool> HomeAsync(string axisName, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || !_axisVarMap.TryGetValue(axisName, out var axisVars))
            {
                _loggingService.LogWarning($"PLC未连接或找不到轴{axisName}的配置，无法回零", WaferAligner.EventIds.EventIds.Plc_Not_Connected);
                return false;
            }
            
            var plc = _plcConnectionManager.GetPlcInstance(axisVars.ConnectionName);
            if (plc == null)
            {
                _loggingService.LogWarning($"获取{axisName}的PLC实例失败", WaferAligner.EventIds.EventIds.Plc_Instance_Null);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                // 检查是Z轴还是其他轴，Z轴有特殊的回零逻辑
                if (axisName == "ZAxis")
                {
                    // Z轴特殊回零逻辑
                    await Task.Run(() => plc.WriteVariableAsync(
                        new PLCVarWriteInfo { Name = axisVars.HomeVar, Value = true },
                        cancellationToken), cancellationToken);
                    
                    // Z轴回零需要确认，创建链接的取消令牌
                    using var delayTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                    await Task.Delay(100, delayTokenSource.Token);
                    
                    PLCVarReadInfo homeConfirmInfo = new() { Name = $"{AxisConstants.AXIS_GVL}.ZHomeConfirmChuck", Type = typeof(bool) };
                    bool homeConfirmed = false;
                    int timeout = 0;
                    
                    // 等待回零确认信号
                    while (!homeConfirmed && timeout < 300) // 最多等待30秒
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        var result = await Task.Run(() => plc.ReadVariableAsync(homeConfirmInfo, cancellationToken), cancellationToken);
                        if (result is bool confirmed && confirmed)
                        {
                            homeConfirmed = true;
                            break;
                        }
                        
                        await Task.Delay(100, delayTokenSource.Token);
                        timeout++;
                    }
                    
                    if (homeConfirmed)
                    {
                        // 确认回零
                        cancellationToken.ThrowIfCancellationRequested();
                        await Task.Run(() => plc.WriteVariableAsync(
                            new PLCVarWriteInfo { Name = $"{AxisConstants.AXIS_GVL}.ZHomeConfirmedChuck", Value = true },
                            cancellationToken), cancellationToken);
                        _loggingService.LogInformation($"{axisName}轴回零确认完成", WaferAligner.EventIds.EventIds.Axis_Home_Completed);
                        return true;
                    }
                    else
                    {
                        _loggingService.LogWarning($"{axisName}轴回零确认超时", WaferAligner.EventIds.EventIds.Z_Home_Confirm_Chuck_Timeout);
                        return false;
                    }
                }
                else
                {
                    // 其他轴的回零逻辑
                    cancellationToken.ThrowIfCancellationRequested();
                    await Task.Run(() => plc.WriteVariableAsync(
                        new PLCVarWriteInfo { Name = axisVars.MoveActionVar, Value = 3 }, // 3表示回零动作
                        cancellationToken), cancellationToken);
                    
                    // 短暂延迟确保动作类型写入，支持取消
                    using var delayTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                    await Task.Delay(100, delayTokenSource.Token);
                    
                    cancellationToken.ThrowIfCancellationRequested();
                    await Task.Run(() => plc.WriteVariableAsync(
                        new PLCVarWriteInfo { Name = axisVars.MoveExecuteVar, Value = true },
                        cancellationToken), cancellationToken);
                    
                    _loggingService.LogInformation($"{axisName}轴回零命令已发送", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                    return true;
                }
            }
            catch (OperationCanceledException)
            {
                _loggingService.LogWarning($"{axisName} 回零操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"轴{axisName}回零失败: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                return false;
            }
        }
        
        public async Task<bool> StopAsync(string axisName, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || !_axisVarMap.TryGetValue(axisName, out var axisVars))
            {
                _loggingService.LogWarning($"PLC未连接或找不到轴{axisName}的配置，无法停止", WaferAligner.EventIds.EventIds.Plc_Not_Connected);
                return false;
            }
            
            var plc = _plcConnectionManager.GetPlcInstance(axisVars.ConnectionName);
            if (plc == null)
            {
                _loggingService.LogWarning($"获取{axisName}的PLC实例失败", WaferAligner.EventIds.EventIds.Plc_Instance_Null);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                await Task.Run(() => plc.WriteVariableAsync(
                    new PLCVarWriteInfo { Name = axisVars.MoveActionVar, Value = 5 }, // 5表示停止动作
                    cancellationToken), cancellationToken);
                
                // 短暂延迟确保动作类型写入，支持取消
                using var delayTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                await Task.Delay(100, delayTokenSource.Token);
                
                cancellationToken.ThrowIfCancellationRequested();
                await Task.Run(() => plc.WriteVariableAsync(
                    new PLCVarWriteInfo { Name = axisVars.MoveExecuteVar, Value = true },
                    cancellationToken), cancellationToken);
                
                _loggingService.LogInformation($"{axisName}轴停止命令已发送", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                return true;
            }
            catch (OperationCanceledException)
            {
                _loggingService.LogWarning($"{axisName} 停止操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"轴{axisName}停止失败: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variable_Write_Failed);
                return false;
            }
        }
        
        public async Task<bool> ResetAsync(string axisName, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || !_axisVarMap.TryGetValue(axisName, out var axisVars))
            {
                _loggingService.LogWarning($"PLC未连接或找不到轴{axisName}的配置，无法复位", WaferAligner.EventIds.EventIds.Plc_Not_Connected);
                return false;
            }
            
            var plc = _plcConnectionManager.GetPlcInstance(axisVars.ConnectionName);
            if (plc == null)
            {
                _loggingService.LogWarning($"获取{axisName}的PLC实例失败", WaferAligner.EventIds.EventIds.Plc_Instance_Null);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                await Task.Run(() => plc.WriteVariableAsync(
                    new PLCVarWriteInfo { Name = axisVars.MoveActionVar, Value = 6 }, // 6表示复位动作
                    cancellationToken), cancellationToken);
                
                // 短暂延迟确保动作类型写入，支持取消
                using var delayTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                await Task.Delay(100, delayTokenSource.Token);
                
                cancellationToken.ThrowIfCancellationRequested();
                await Task.Run(() => plc.WriteVariableAsync(
                    new PLCVarWriteInfo { Name = axisVars.MoveExecuteVar, Value = true },
                    cancellationToken), cancellationToken);
                
                _loggingService.LogInformation($"{axisName}轴复位命令已发送", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                return true;
            }
            catch (OperationCanceledException)
            {
                _loggingService.LogWarning($"{axisName} 复位操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"轴{axisName}复位失败: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variable_Write_Failed);
                return false;
            }
        }
        
        public async Task<bool> SetJogSpeedAsync(string axisName, int speed, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || !_axisVarMap.TryGetValue(axisName, out var axisVars))
            {
                _loggingService.LogWarning($"PLC未连接或找不到轴{axisName}的配置，无法设置点动速度", WaferAligner.EventIds.EventIds.Plc_Not_Connected);
                return false;
            }
            
            var plc = _plcConnectionManager.GetPlcInstance(axisVars.ConnectionName);
            if (plc == null)
            {
                _loggingService.LogWarning($"获取{axisName}的PLC实例失败", WaferAligner.EventIds.EventIds.Plc_Instance_Null);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                await Task.Run(() => plc.WriteVariableAsync(
                    new PLCVarWriteInfo { Name = axisVars.JogSpeedVar, Value = (double)speed },
                    cancellationToken), cancellationToken);
                
                _loggingService.LogInformation($"设置{axisName}轴点动速度为{speed}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                return true;
            }
            catch (OperationCanceledException)
            {
                _loggingService.LogWarning($"{axisName} 设置点动速度操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置{axisName}轴点动速度失败: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variable_Write_Failed);
                return false;
            }
        }
        
        public async Task<bool> SetRunSpeedAsync(string axisName, int speed, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || !_axisVarMap.TryGetValue(axisName, out var axisVars))
            {
                _loggingService.LogWarning($"PLC未连接或找不到轴{axisName}的配置，无法设置运行速度", WaferAligner.EventIds.EventIds.Plc_Not_Connected);
                return false;
            }
            
            var plc = _plcConnectionManager.GetPlcInstance(axisVars.ConnectionName);
            if (plc == null)
            {
                _loggingService.LogWarning($"获取{axisName}的PLC实例失败", WaferAligner.EventIds.EventIds.Plc_Instance_Null);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                await Task.Run(() => plc.WriteVariableAsync(
                    new PLCVarWriteInfo { Name = axisVars.RunSpeedVar, Value = (float)speed },
                    cancellationToken), cancellationToken);
                
                _loggingService.LogInformation($"设置{axisName}轴运行速度为{speed}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                return true;
            }
            catch (OperationCanceledException)
            {
                _loggingService.LogWarning($"{axisName} 设置运行速度操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置{axisName}轴运行速度失败: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variable_Write_Failed);
                return false;
            }
        }
        
        public async Task<bool> IsHomedAsync(string axisName, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || !_axisVarMap.TryGetValue(axisName, out var axisVars))
            {
                _loggingService.LogWarning($"PLC未连接或找不到轴{axisName}的配置，无法获取回零状态", WaferAligner.EventIds.EventIds.Plc_Not_Connected);
                return false;
            }
            
            var plc = _plcConnectionManager.GetPlcInstance(axisVars.ConnectionName);
            if (plc == null)
            {
                _loggingService.LogWarning($"获取{axisName}的PLC实例失败", WaferAligner.EventIds.EventIds.Plc_Instance_Null);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var homedInfo = new PLCVarReadInfo { Name = axisVars.HomedVar, Type = typeof(bool) };
                var result = await Task.Run(() => plc.ReadVariableAsync(homedInfo, cancellationToken), cancellationToken);
                
                if (result is bool isHomed)
                {
                    return isHomed;
                }
                
                _loggingService.LogWarning($"获取{axisName}回零状态类型转换失败，返回值类型: {result?.GetType().Name ?? "null"}", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                return false;
            }
            catch (OperationCanceledException)
            {
                _loggingService.LogWarning($"{axisName} 获取回零状态操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"读取轴{axisName}回零状态失败: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                return false;
            }
        }
        
        public async Task<bool> IsEnabledAsync(string axisName, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || !_axisVarMap.TryGetValue(axisName, out var axisVars))
            {
                _loggingService.LogWarning($"PLC未连接或找不到轴{axisName}的配置，无法获取使能状态", WaferAligner.EventIds.EventIds.Plc_Not_Connected);
                return false;
            }
            
            var plc = _plcConnectionManager.GetPlcInstance(axisVars.ConnectionName);
            if (plc == null)
            {
                _loggingService.LogWarning($"获取{axisName}的PLC实例失败", WaferAligner.EventIds.EventIds.Plc_Instance_Null);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var enabledInfo = new PLCVarReadInfo { Name = axisVars.EnabledVar, Type = typeof(bool) };
                var result = await Task.Run(() => plc.ReadVariableAsync(enabledInfo, cancellationToken), cancellationToken);
                
                if (result is bool isEnabled)
                {
                    return isEnabled;
                }
                
                _loggingService.LogWarning($"获取{axisName}使能状态类型转换失败，返回值类型: {result?.GetType().Name ?? "null"}", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                return false;
            }
            catch (OperationCanceledException)
            {
                _loggingService.LogWarning($"{axisName} 获取使能状态操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"读取轴{axisName}使能状态失败: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                return false;
            }
        }
        
        public async Task<bool> HasErrorAsync(string axisName, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || !_axisVarMap.TryGetValue(axisName, out var axisVars))
            {
                _loggingService.LogWarning($"PLC未连接或找不到轴{axisName}的配置，无法获取报警状态", WaferAligner.EventIds.EventIds.Plc_Not_Connected);
                return false;
            }
            
            var plc = _plcConnectionManager.GetPlcInstance(axisVars.ConnectionName);
            if (plc == null)
            {
                _loggingService.LogWarning($"获取{axisName}的PLC实例失败", WaferAligner.EventIds.EventIds.Plc_Instance_Null);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var alarmInfo = new PLCVarReadInfo { Name = axisVars.AlarmVar, Type = typeof(bool) };
                var result = await Task.Run(() => plc.ReadVariableAsync(alarmInfo, cancellationToken), cancellationToken);
                
                if (result is bool hasError)
                {
                    return hasError;
                }
                
                _loggingService.LogWarning($"获取{axisName}报警状态类型转换失败，返回值类型: {result?.GetType().Name ?? "null"}", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                return false;
            }
            catch (OperationCanceledException)
            {
                _loggingService.LogWarning($"{axisName} 获取报警状态操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"读取轴{axisName}报警状态失败: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                return false;
            }
        }
        
        /// <summary>
        /// 向PLC写入变量值
        /// </summary>
        /// <typeparam name="T">变量类型</typeparam>
        /// <param name="axisName">轴名称</param>
        /// <param name="variableName">变量名称</param>
        /// <param name="value">要写入的值</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>写入是否成功</returns>
        public async Task<bool> WriteVariableAsync<T>(string axisName, string variableName, T value, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || !_axisVarMap.TryGetValue(axisName, out var axisVars))
            {
                _loggingService.LogWarning($"PLC未连接或找不到轴{axisName}的配置，无法写入变量 {variableName}", WaferAligner.EventIds.EventIds.Plc_Not_Connected);
                return false;
            }
            
            var plc = _plcConnectionManager.GetPlcInstance(axisVars.ConnectionName);
            if (plc == null)
            {
                _loggingService.LogWarning($"获取{axisName}的PLC实例失败", WaferAligner.EventIds.EventIds.Plc_Instance_Null);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                // 处理变量名，构建完整的PLC变量路径
                string fullVariableName = variableName;
                if (!variableName.StartsWith(AxisConstants.AXIS_GVL))
                {
                    fullVariableName = $"{AxisConstants.AXIS_GVL}.{axisName}{variableName}";
                }
                
                var result = await Task.Run(() => plc.WriteVariableAsync(
                    new PLCVarWriteInfo { Name = fullVariableName, Value = value },
                    cancellationToken), cancellationToken);
                
                if (result)
                {
                    _loggingService.LogDebug($"写入{axisName}轴变量 {variableName}={value} 成功", WaferAligner.EventIds.EventIds.Plc_Variable_Write_Succeeded);
                }
                else
                {
                    _loggingService.LogWarning($"写入{axisName}轴变量 {variableName}={value} 返回失败", WaferAligner.EventIds.EventIds.Plc_Variable_Write_Failed);
                }
                
                return result;
            }
            catch (OperationCanceledException)
            {
                _loggingService.LogWarning($"写入{axisName}轴变量 {variableName} 操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"写入{axisName}轴变量 {variableName}={value} 失败: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variable_Write_Failed);
                return false;
            }
        }
        
        /// <summary>
        /// 从PLC读取变量值
        /// </summary>
        /// <typeparam name="T">变量类型</typeparam>
        /// <param name="axisName">轴名称</param>
        /// <param name="variableName">变量名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>读取的变量值</returns>
        public async Task<T> ReadVariableAsync<T>(string axisName, string variableName, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || !_axisVarMap.TryGetValue(axisName, out var axisVars))
            {
                _loggingService.LogWarning($"PLC未连接或找不到轴{axisName}的配置，无法读取变量 {variableName}", WaferAligner.EventIds.EventIds.Plc_Not_Connected);
                // 返回有意义的默认值而不是default，让调用者知道操作失败
                return GetDefaultValueForType<T>();
            }
            
            var plc = _plcConnectionManager.GetPlcInstance(axisVars.ConnectionName);
            if (plc == null)
            {
                _loggingService.LogWarning($"获取{axisName}的PLC实例失败", WaferAligner.EventIds.EventIds.Plc_Instance_Null);
                return GetDefaultValueForType<T>();
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                // 处理变量名，构建完整的PLC变量路径
                string fullVariableName = variableName;
                if (!variableName.StartsWith(AxisConstants.AXIS_GVL))
                {
                    fullVariableName = $"{AxisConstants.AXIS_GVL}.{axisName}{variableName}";
                }
                
                var readInfo = new PLCVarReadInfo { Name = fullVariableName, Type = typeof(T) };
                var result = await Task.Run(() => plc.ReadVariableAsync(readInfo, cancellationToken), cancellationToken);
                
                if (result == null)
                {
                    _loggingService.LogWarning($"读取{axisName}轴变量 {variableName} 返回null", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                    return GetDefaultValueForType<T>();
                }
                
                try
                {
                    if (result is T typedResult)
                    {
                        return typedResult;
                    }
                    
                    // 尝试转换类型
                    return (T)Convert.ChangeType(result, typeof(T));
                }
                catch (InvalidCastException ex)
                {
                    _loggingService.LogError(ex, $"读取{axisName}轴变量 {variableName} 类型转换失败，结果类型: {result.GetType().Name}，目标类型: {typeof(T).Name}", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                    return GetDefaultValueForType<T>();
                }
            }
            catch (OperationCanceledException)
            {
                _loggingService.LogWarning($"读取{axisName}轴变量 {variableName} 操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"读取{axisName}轴变量 {variableName} 失败: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                return GetDefaultValueForType<T>();
            }
        }
        
        /// <summary>
        /// 根据类型返回有意义的默认值
        /// </summary>
        private static T GetDefaultValueForType<T>()
        {
            if (typeof(T) == typeof(bool))
                return (T)(object)false;
            if (typeof(T) == typeof(int))
                return (T)(object)(-1); // 使用-1表示无效值
            if (typeof(T) == typeof(double))
                return (T)(object)(-1.0);
            if (typeof(T) == typeof(string))
                return (T)(object)string.Empty;
            if (typeof(T) == typeof(short))
                return (T)(object)(short)(-1);
            if (typeof(T) == typeof(ushort))
                return (T)(object)(ushort)0;
            if (typeof(T) == typeof(uint))
                return (T)(object)0u;
            if (typeof(T) == typeof(long))
                return (T)(object)(-1L);
            if (typeof(T) == typeof(ulong))
                return (T)(object)0uL;
            if (typeof(T) == typeof(float))
                return (T)(object)(-1.0f);
            
            // 对于其他类型，返回default
            return default(T);
        }
    }
    
    /// <summary>
    /// 轴PLC变量映射
    /// </summary>
    internal class AxisPlcVariables
    {
        public string ConnectionName { get; set; } // PLC连接名称
        public string PositionVar { get; set; } // 位置变量
        public string TargetPositionVar { get; set; } // 目标位置变量
        public string MoveExecuteVar { get; set; } // 移动执行变量
        public string MoveActionVar { get; set; } // 移动动作类型变量
        public string HomeVar { get; set; } // 回零变量
        public string StopVar { get; set; } // 停止变量
        public string ResetVar { get; set; } // 复位变量
        public string StatusVar { get; set; } // 状态变量
        public string HomedVar { get; set; } // 是否已回零变量
        public string EnabledVar { get; set; } // 是否已使能变量
        public string AlarmVar { get; set; } // 是否有报警变量
        public string JogSpeedVar { get; set; } // 点动速度变量
        public string RunSpeedVar { get; set; } // 运行速度变量
        public string PositionStateVar { get; set; } // 位置状态变量
    }
}
