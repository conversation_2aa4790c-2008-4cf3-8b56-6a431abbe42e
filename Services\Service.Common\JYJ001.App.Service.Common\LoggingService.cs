﻿using Aya.Extensions;
using Aya.Log;
using JYJ001.App.Business;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reactive.Subjects;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Threading.Channels;
using JYJ001.App.Services.Common.Interfaces;
using Microsoft.Extensions.Logging;

namespace JYJ001.App.Services.Common
{
    /// <summary>
    /// 增强的日志服务实现 - 重构版本
    /// 支持按模块日志级别控制、结构化日志、性能监控等新功能
    /// </summary>
    public class LoggingService : ILoggingService
    {
        #region 现有字段 - 保持兼容性

        public IObservable<ILogger> LogFeed { get; }
        IObservable<LogEntry<string>> ILoggingService.LogFeed => subject;

        private readonly Subject<Business.LogEntry<string>> subject = new Subject<LogEntry<string>>();
        private LogLevel _minimumLogLevel = LogLevel.Information;
        private readonly List<ILogger> loggers = new List<ILogger>();

        #endregion

        #region 新增字段

        // 按模块的日志级别配置
        private readonly ConcurrentDictionary<string, LogLevel> _moduleLogLevels = new ConcurrentDictionary<string, LogLevel>();

        // 日志配置
        private LoggingConfiguration _configuration;

        #endregion

        #region 构造函数

        public LoggingService() : this(LoggingConfiguration.CreateDefaultConfiguration())
        {
        }

        public LoggingService(LoggingConfiguration configuration)
        {
            _configuration = configuration ?? LoggingConfiguration.CreateDefaultConfiguration();
            _minimumLogLevel = _configuration.GlobalMinimumLevel;

            // 初始化模块级别配置
            foreach (var kvp in _configuration.ModuleLevels)
            {
                _moduleLogLevels.TryAdd(kvp.Key, kvp.Value);
            }

            // 初始化日志记录器
            var fileLogger = new FileLogger(this);
            //var pglogger = new PgLogger(this);
            loggers.Add(fileLogger);
            //loggers.Add(pglogger);
        }

        #endregion

        #region 现有方法 - 保持兼容性

        public void SetMinimumLogLevel(LogLevel level)
        {
            _minimumLogLevel = level;
            _configuration.GlobalMinimumLevel = level;

            // 更新所有日志记录器的日志级别
            foreach (var logger in loggers)
            {
                if (logger is FileLogger fileLogger)
                {
                    fileLogger.SetMinimumLogLevel(level);
                }
            }
        }

        public LogLevel GetMinimumLogLevel()
        {
            return _minimumLogLevel;
        }

        public void Log(LogLevel logLevel, EventId eventId, [CallerMemberName] string message = null)
        {
            // 检查日志级别是否应该被记录
            if (!ShouldLog(null, logLevel))
                return;

            subject.OnNext(new LogEntry<string>
            {
                LogLevel = logLevel,
                Id = eventId,
                LogMessage = message,
                TimeStamp = DateTime.Now
            });
        }

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {
            // 检查日志级别是否应该被记录
            if (!ShouldLog(null, logLevel))
                return;

            string formattedMessage = formatter != null ? formatter(state, exception) : state?.ToString();

            subject.OnNext(new LogEntry<string>
            {
                LogLevel = logLevel,
                Id = eventId,
                LogMessage = formattedMessage,
                Exception = exception,
                TimeStamp = DateTime.Now
            });
        }

        public ChannelReader<LogEntry<string>> StreamLog()
        {
            return ((IObservable<LogEntry<string>>)subject).AsChannelReader();
        }

        #endregion

        #region 新增方法实现

        public void SetModuleLogLevel(string moduleName, LogLevel level)
        {
            if (string.IsNullOrEmpty(moduleName))
                throw new ArgumentException("模块名称不能为空", nameof(moduleName));

            _moduleLogLevels.AddOrUpdate(moduleName, level, (key, oldValue) => level);
            _configuration.ModuleLevels[moduleName] = level;
        }

        public LogLevel GetModuleLogLevel(string moduleName)
        {
            if (string.IsNullOrEmpty(moduleName))
                return _minimumLogLevel;

            return _moduleLogLevels.GetValueOrDefault(moduleName, _minimumLogLevel);
        }

        public void LogStructured<T>(LogLevel logLevel, EventId eventId, string message, T data)
        {
            if (!ShouldLog(null, logLevel))
                return;

            string structuredMessage;
            if (_configuration.EnableStructuredLogging)
            {
                try
                {
                    var serializedData = JsonSerializer.Serialize(data, new JsonSerializerOptions
                    {
                        WriteIndented = false
                    });
                    structuredMessage = $"{message} | Data: {serializedData}";
                }
                catch (Exception ex)
                {
                    // 序列化失败时降级处理
                    structuredMessage = $"{message} | Data: [序列化失败: {ex.Message}]";
                }
            }
            else
            {
                structuredMessage = $"{message} | Data: {data?.ToString() ?? "null"}";
            }

            subject.OnNext(new LogEntry<string>
            {
                LogLevel = logLevel,
                Id = eventId,
                LogMessage = structuredMessage,
                TimeStamp = DateTime.Now
            });
        }

        public IDisposable BeginPerformanceScope(string operationName, EventId eventId)
        {
            if (!_configuration.EnablePerformanceLogging)
            {
                return new DisposableAction(() => { }); // 空操作
            }

            return new PerformanceScope(this, operationName, eventId);
        }

        public bool IsEnabled(string moduleName, LogLevel logLevel)
        {
            return ShouldLog(moduleName, logLevel);
        }

        #endregion

        #region 私有辅助方法

        private bool ShouldLog(string moduleName, LogLevel logLevel)
        {
            LogLevel effectiveLevel;

            if (!string.IsNullOrEmpty(moduleName) && _moduleLogLevels.TryGetValue(moduleName, out var moduleLevel))
            {
                effectiveLevel = moduleLevel;
            }
            else
            {
                effectiveLevel = _minimumLogLevel;
            }

            return logLevel >= effectiveLevel;
        }

        #endregion
    }

    /// <summary>
    /// 性能监控作用域
    /// </summary>
    internal class PerformanceScope : IDisposable
    {
        private readonly LoggingService _loggingService;
        private readonly string _operationName;
        private readonly EventId _eventId;
        private readonly Stopwatch _stopwatch;
        private bool _disposed = false;

        public PerformanceScope(LoggingService loggingService, string operationName, EventId eventId)
        {
            _loggingService = loggingService;
            _operationName = operationName;
            _eventId = eventId;
            _stopwatch = Stopwatch.StartNew();

            // 记录开始日志
            _loggingService.Log(LogLevel.Debug, _eventId, $"开始操作: {_operationName}");
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _stopwatch.Stop();

                // 记录完成日志，包含耗时信息
                var message = $"完成操作: {_operationName} | 耗时: {_stopwatch.ElapsedMilliseconds}ms";
                _loggingService.Log(LogLevel.Debug, _eventId, message);

                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 简单的可释放操作包装器
    /// </summary>
    internal class DisposableAction : IDisposable
    {
        private readonly Action _action;
        private bool _disposed = false;

        public DisposableAction(Action action)
        {
            _action = action;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _action?.Invoke();
                _disposed = true;
            }
        }
    }
}
