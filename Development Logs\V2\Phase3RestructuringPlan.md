# WaferAligner项目 Phase 3 重构计划

## 计划概述

在完成了日志统一和异步编程优化后，Phase 3重构计划将继续按照以下优先级进行：

1. **优化共用组件**（中优先级）
2. **清理剩余静态耦合**（低优先级）
3. **创建独立测试项目**（高优先级）
4. **性能监控框架**（中优先级）
5. **PLC通信批处理优化**（中优先级）

## 1. 优化共用组件（中优先级）

### 背景与目标

已有的TimerWrapper等组件证明了其价值，我们需要将类似的优化模式推广到更多共用组件，提高代码一致性和性能。

### 实施内容

#### 1.1 TimerWrapper推广与强化
- 扫描所有直接使用System.Timers.Timer和System.Windows.Forms.Timer的地方
- 确保所有定时器操作都使用TimerWrapper类，包括创建、启动、停止和释放
- 为TimerWrapper添加更多功能，如一次性定时器、自动重试机制等

#### 1.2 对象池实现
- 设计通用对象池接口`IObjectPool<T>`和基础实现
- 为频繁创建的小对象实现对象池，优先考虑：
  - PLC数据容器对象
  - UI更新时使用的临时对象
  - 配方参数对象
- 实现自动扩展和收缩的池大小管理

#### 1.3 异步操作扩展
- 创建`AsyncUtility`静态类，提供常用异步操作辅助方法
- 实现常用异步模式的简化方法，如：
  - 带超时的异步操作
  - 可取消的重试机制
  - 批量并行操作
- 为UI操作提供专门的异步扩展方法

#### 1.4 资源管理增强
- 扩展`ResourceManager`，添加资源使用监控
- 实现资源泄漏检测机制
- 添加资源使用统计和报告功能

### 预期成果
- 所有定时器使用统一的TimerWrapper管理
- 高频创建的对象通过对象池优化，减少GC压力
- 异步操作更加标准化和简洁
- 资源管理更加完善，能够及时发现和处理资源泄漏

### 验收标准
- 所有定时器使用TimerWrapper
- 对象池实现并应用于至少3个高频创建对象类型
- 异步操作工具类完成并投入使用
- 资源使用监控功能可用并能生成报告

## 2. 清理剩余静态耦合（低优先级）

### 背景与目标

尽管已经消除了大部分静态耦合，但仍有约5%的遗留问题需要解决，特别是对Phase2MigrationHelper的依赖。

### 实施内容

#### 2.1 Phase2MigrationHelper依赖消除
- 审查所有对Phase2MigrationHelper的调用点
- 为每个使用场景创建专用服务接口和实现
- 将依赖从迁移助手逐步迁移到新服务
- 最终移除或弃用Phase2MigrationHelper

#### 2.2 PublicFunc和静态工具类重构
- 分析PublicFunc中的方法，按功能分类
- 创建专门的服务类承接相关功能
- 通过依赖注入提供这些服务
- 添加弃用警告到静态方法，引导使用新服务

#### 2.3 ConstValue替换完成
- 确保所有硬编码常量都已迁移到AxisConstants
- 对于配置性质的常量，移至配置系统
- 创建常量管理服务，处理动态常量

#### 2.4 静态事件替换
- 识别所有静态事件
- 设计基于发布-订阅模式的事件总线
- 通过依赖注入提供事件总线
- 将静态事件迁移到事件总线

### 预期成果
- Phase2MigrationHelper依赖减少至最低
- 静态工具类方法迁移到服务类
- 硬编码常量完全替换
- 静态事件替换为事件总线或实例事件

### 验收标准
- Phase2MigrationHelper依赖减少80%以上
- PublicFunc静态方法迁移至少70%到服务
- 静态事件替换率达到90%以上
- 静态耦合率降低到2%以下

## 3. 创建独立测试项目（高优先级）

### 背景与目标

当前的测试代码分散在主项目中，需要创建独立的测试项目，建立完整的测试框架，确保代码质量和验证重构成果。

### 实施内容

#### 3.1 测试项目建立
- 创建完整的WaferAligner.Tests项目
- 配置NUnit/xUnit测试框架
- 设置Moq和其他测试辅助库
- 建立测试项目的文件夹结构，遵循以下划分：
  ```
  WaferAligner.Tests/
  ├── Unit/                          # 单元测试
  │   ├── Adapters/                  # 适配器测试
  │   ├── Services/                  # 服务测试
  │   ├── Migration/                 # 迁移助手测试
  │   └── Factories/                 # 工厂测试
  ├── Integration/                   # 集成测试
  │   ├── PLC/                       # PLC通信测试
  │   ├── UI/                        # UI集成测试
  │   └── Workflows/                 # 业务流程测试
  ├── Performance/                   # 性能测试
  │   ├── Benchmarks/                # 基准测试
  │   └── LoadTests/                 # 负载测试
  └── Helpers/                       # 测试辅助类
      ├── Mocks/                     # Mock对象
      ├── Fixtures/                  # 测试夹具
      └── TestData/                  # 测试数据
  ```

#### 3.2 单元测试实现
- 迁移现有Tests目录中的测试类到新项目
- 为核心服务和组件编写单元测试:
  - 轴控制服务
  - PLC通信层
  - 资源管理服务
  - UI交互
- 添加测试特性和断言

#### 3.3 集成测试框架
- 设计集成测试基础类
- 实现测试夹具和测试数据生成器
- 创建模拟PLC和硬件接口
- 编写关键业务流程的集成测试

#### 3.4 测试自动化
- 设置持续集成流程
- 创建测试运行脚本
- 配置测试报告生成
- 实现自动化测试运行

### 预期成果
- 完整的测试项目结构
- 核心组件的单元测试
- 关键业务流程的集成测试
- 自动化测试流程

### 验收标准
- 完整的测试项目结构建立
- 核心组件测试覆盖率达到70%以上
- 自动化测试流程可运行
- 测试报告生成功能可用

## 4. 性能监控框架（中优先级）

### 背景与目标

缺乏系统性能监控手段，难以识别性能瓶颈和验证优化效果。性能监控框架将帮助我们量化评估系统性能并指导后续优化。

### 实施内容

#### 4.1 性能计数器实现
- 创建`PerformanceCounter`类记录操作耗时
- 实现计时器上下文管理器模式
- 添加内存和CPU使用监控
- 定义关键性能指标和基准线

#### 4.2 关键操作监控
- 为PLC通信操作添加性能监控
- 监控页面加载和切换性能
- 跟踪UI响应时间
- 监控资源使用和清理

#### 4.3 性能数据收集与分析
- 实现性能数据记录机制
- 创建性能数据导出功能
- 设计简单的性能报告界面
- 提供趋势分析功能

#### 4.4 基准测试工具
- 实现标准化的基准测试工具
- 创建关键操作的基准测试套件
- 设置自动化基准测试流程
- 实现性能退化检测

### 预期成果
- 关键操作性能数据的收集和分析
- 性能瓶颈的识别和定位
- 优化效果的量化评估
- 性能退化的早期发现

### 验收标准
- 关键操作性能数据收集完整
- 性能报告生成功能可用
- 至少3个基准测试套件建立
- 性能监控对系统性能影响不超过5%

## 5. PLC通信批处理优化（中优先级）

### 背景与目标

当前PLC通信采用变量级别的单次读写，造成通信效率低下。批处理优化将提高PLC通信效率，减少系统资源消耗。

### 实施内容

#### 5.1 变量组机制
- 设计`PLCVariableGroup`类定义变量组
- 实现变量依赖关系管理
- 创建变量组注册和管理接口
- 支持变量组的按需加载和卸载

#### 5.2 批量读写接口
- 扩展`IPlcInstance`接口，添加批量读写方法
- 实现对应的批量操作逻辑
- 优化批量操作的错误处理和重试机制
- 提供批量操作的性能统计

#### 5.3 缓存层实现
- 设计PLC变量缓存策略
- 实现基于时间和变化检测的缓存机制
- 添加缓存一致性保证
- 提供缓存状态监控和统计

#### 5.4 通信优化应用
- 分析应用中高频PLC访问点
- 优先改造FTitlePage3等关键页面
- 重构PLC数据观察者模式实现
- 实现更高效的变量监控机制

### 预期成果
- PLC通信效率提升
- 系统资源消耗减少
- UI响应性提高
- 数据一致性保持

### 验收标准
- 批量读写接口实现并可用
- 高频访问点批处理优化完成80%
- 通信次数减少40%以上
- 数据一致性测试通过率100%

## 实施时间表

| 阶段 | 时间 | 主要任务 | 次要任务 |
|------|------|----------|----------|
| 第1-2周 | 优化共用组件 | TimerWrapper推广、对象池设计实现 | 测试项目初步搭建 |
| 第3-4周 | 清理静态耦合 | Phase2MigrationHelper依赖消除 | 继续共用组件优化 |
| 第5-6周 | 创建独立测试项目 | 单元测试实现、集成测试框架 | 开始性能监控框架 |
| 第7-8周 | 性能监控框架 | 性能计数器实现、关键操作监控 | 完善测试自动化 |
| 第9-10周 | PLC通信批处理优化 | 变量组机制、批量读写接口 | 综合测试与验证 |

## 风险与缓解策略

1. **组件改造风险**：
   - 风险：改造可能影响现有功能
   - 缓解：采用增量式改造，每个组件改造后立即测试

2. **静态耦合清理风险**：
   - 风险：可能引入新的问题或遗漏依赖
   - 缓解：建立完整依赖图，逐步替换并验证

3. **测试覆盖风险**：
   - 风险：测试可能无法覆盖所有边缘情况
   - 缓解：优先测试核心功能和已知问题点

4. **性能监控开销**：
   - 风险：监控本身可能引入性能开销
   - 缓解：设计轻量级监控框架，支持可配置级别

5. **PLC通信改造风险**：
   - 风险：批处理可能引入时序问题
   - 缓解：先在非关键路径测试，保留原接口作为回退

## 总结

Phase 3重构计划将通过上述五个方面的工作，进一步提升WaferAligner的代码质量、性能和可维护性。优先级顺序的调整确保了最关键的任务能够及时完成，为整个项目奠定更坚实的基础。 