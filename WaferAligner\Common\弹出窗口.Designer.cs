﻿namespace WaferAligner.Common
{
    partial class Message_弹出窗口
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // 移除Dispose方法定义，因为它已在弹出窗口.cs文件中重写
        // protected override void Dispose(bool disposing)
        // {
        //     if (disposing && (components != null))
        //     {
        //         components.Dispose();
        //     }
        //     base.Dispose(disposing);
        // }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            Message_Panel = new Panel();
            label1_提示框 = new Label();
            BTN_Yes = new Button();
            BTN_No_Save = new Button();
            Message_Tip = new TextBox();
            BTN_Cancel = new Button();
            // 移除Timer_自动关闭弹窗的声明，已使用TimerWrapper替代
            Message_Panel.SuspendLayout();
            SuspendLayout();
            // 
            // Message_Panel
            // 
            Message_Panel.BackColor = Color.WhiteSmoke;
            Message_Panel.Controls.Add(label1_提示框);
            Message_Panel.Location = new Point(0, 0);
            Message_Panel.Margin = new Padding(5, 4, 5, 4);
            Message_Panel.Name = "Message_Panel";
            Message_Panel.Size = new Size(842, 76);
            Message_Panel.TabIndex = 4;
            // 
            // label1_提示框
            // 
            label1_提示框.AutoSize = true;
            label1_提示框.Font = new Font("宋体", 23F, FontStyle.Bold, GraphicsUnit.Point);
            label1_提示框.Location = new Point(394, 14);
            label1_提示框.Margin = new Padding(4, 0, 4, 0);
            label1_提示框.Name = "label1_提示框";
            label1_提示框.Size = new Size(0, 31);
            label1_提示框.TabIndex = 310;
            // 
            // BTN_Yes
            // 
            BTN_Yes.BackColor = Color.FromArgb(224, 224, 224);
            BTN_Yes.FlatAppearance.BorderSize = 0;
            BTN_Yes.Font = new Font("宋体", 15F, FontStyle.Bold, GraphicsUnit.Point);
            BTN_Yes.ForeColor = SystemColors.WindowText;
            BTN_Yes.Location = new Point(13, 482);
            BTN_Yes.Margin = new Padding(5, 4, 5, 4);
            BTN_Yes.Name = "BTN_Yes";
            BTN_Yes.Size = new Size(131, 39);
            BTN_Yes.TabIndex = 34;
            BTN_Yes.Text = "是";
            BTN_Yes.UseVisualStyleBackColor = true;
            BTN_Yes.Click += BTN_Yes_Click;
            // 
            // BTN_No_Save
            // 
            BTN_No_Save.BackColor = Color.FromArgb(224, 224, 224);
            BTN_No_Save.FlatAppearance.BorderSize = 0;
            BTN_No_Save.Font = new Font("宋体", 15F, FontStyle.Bold, GraphicsUnit.Point);
            BTN_No_Save.ForeColor = Color.Black;
            BTN_No_Save.Location = new Point(239, 482);
            BTN_No_Save.Margin = new Padding(5, 4, 5, 4);
            BTN_No_Save.Name = "BTN_No_Save";
            BTN_No_Save.Size = new Size(131, 39);
            BTN_No_Save.TabIndex = 35;
            BTN_No_Save.Text = "否";
            BTN_No_Save.UseVisualStyleBackColor = true;
            BTN_No_Save.Click += BTN_No_Save_Click;
            // 
            // Message_Tip
            // 
            Message_Tip.BackColor = Color.White;
            Message_Tip.BorderStyle = BorderStyle.None;
            Message_Tip.Font = new Font("宋体", 24.75F, FontStyle.Bold, GraphicsUnit.Point);
            Message_Tip.ForeColor = Color.Black;
            Message_Tip.Location = new Point(13, 83);
            Message_Tip.Margin = new Padding(4);
            Message_Tip.Multiline = true;
            Message_Tip.Name = "Message_Tip";
            Message_Tip.ReadOnly = true;
            Message_Tip.Size = new Size(584, 386);
            Message_Tip.TabIndex = 308;
            Message_Tip.TabStop = false;
            // 
            // BTN_Cancel
            // 
            BTN_Cancel.BackColor = Color.FromArgb(224, 224, 224);
            BTN_Cancel.FlatAppearance.BorderSize = 0;
            BTN_Cancel.Font = new Font("宋体", 15F, FontStyle.Bold, GraphicsUnit.Point);
            BTN_Cancel.ForeColor = Color.Black;
            BTN_Cancel.Location = new Point(465, 482);
            BTN_Cancel.Margin = new Padding(5, 4, 5, 4);
            BTN_Cancel.Name = "BTN_Cancel";
            BTN_Cancel.Size = new Size(131, 39);
            BTN_Cancel.TabIndex = 309;
            BTN_Cancel.Text = "取消";
            BTN_Cancel.UseVisualStyleBackColor = true;
            BTN_Cancel.Click += BTN_Cancel_Click;
            // 
            // Message_弹出窗口
            // 
            AutoScaleDimensions = new SizeF(7F, 17F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(248, 248, 248);
            ClientSize = new Size(610, 528);
            ControlBox = false;
            Controls.Add(BTN_Cancel);
            Controls.Add(BTN_No_Save);
            Controls.Add(BTN_Yes);
            Controls.Add(Message_Panel);
            Controls.Add(Message_Tip);
            FormBorderStyle = FormBorderStyle.FixedSingle;
            Margin = new Padding(4);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "Message_弹出窗口";
            ShowIcon = false;
            StartPosition = FormStartPosition.CenterParent;
            Text = "  ";
            TopMost = true;
            Load += Message_弹出窗口_Load;
            Message_Panel.ResumeLayout(false);
            Message_Panel.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion
        private Panel Message_Panel;
        private Button BTN_Yes;
        private Button BTN_No_Save;
        public TextBox Message_Tip;
        private Button BTN_Cancel;
        private Label label1_提示框;
        // 移除Timer_自动关闭弹窗的声明，已使用TimerWrapper替代
    }
}