using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WaferAligner.Services
{
    /// <summary>
    /// PLC相关常量定义
    /// </summary>
    public static class PlcConstants
    {
        /// <summary>
        /// PLC连接名称
        /// </summary>
        public static class ConnectionNames
        {
            public const string MAIN_PLC = "XYRAxis";
            public const string Z_AXIS_PLC = "ZAxis";
            public const string LX_AXIS_PLC = "LeftX";
            public const string LY_AXIS_PLC = "LeftY";
            public const string LZ_AXIS_PLC = "LeftZ";
            public const string RX_AXIS_PLC = "RightX";
            public const string RY_AXIS_PLC = "RightY";
            public const string RZ_AXIS_PLC = "RightZ";
        }

        /// <summary>
        /// PLC变量类型前缀
        /// </summary>
        public static class VariablePrefixes
        {
            public const string BOOL_PREFIX = "hb_";
            public const string INT_PREFIX = "hi_";
            public const string REAL_PREFIX = "hr_";
        }

        /// <summary>
        /// PLC通信相关常量
        /// </summary>
        public static class Communication
        {
            public const int DEFAULT_TIMEOUT_MS = 5000;
            public const int DEFAULT_RETRY_COUNT = 3;
        }

        /// <summary>
        /// PLC变量地址
        /// </summary>
        public static class Addresses
        {
            public const string MSG_DATA_BASE = "AMW1000";
            public const string MSG_RESPONSE_BASE = "AMW1020";
        }

        /// <summary>
        /// 气缸状态定义
        /// </summary>
        public enum CylinderStatus
        {
            /// <summary>
            /// 关闭状态
            /// </summary>
            Close = 0,
            
            /// <summary>
            /// 打开状态
            /// </summary>
            Open = 1,
            
            /// <summary>
            /// 未知状态
            /// </summary>
            None = 2
        }
    }
} 