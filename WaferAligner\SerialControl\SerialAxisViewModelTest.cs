using JYJ001.App.Services.Common.Interfaces;
using System;
using System.Threading.Tasks;
using WaferAligner.Common;
using WaferAligner.EventIds;
using WaferAligner.SerialControl.Interfaces;
using WaferAligner.SerialControl.Models;
using JYJ001.App.Services.Common.Extension;
using System.Windows.Forms;
using static WaferAligner.EventIds.EventIds;

namespace WaferAligner.SerialControl
{
    /// <summary>
    /// SerialAxisViewModel测试类
    /// 用于验证SerialAxisViewModel功能是否符合预期
    /// </summary>
    public class SerialAxisViewModelTest
    {
        private readonly ILoggingService _loggingService;
        private readonly ISerialAxisControllerFactory _axisControllerFactory;
        private readonly WaferAligner.Common.ResourceManager _resourceManager;
        
        // 轴控制器和视图模型
        private ISerialAxisController _xAxisController;
        private ISerialAxisController _yAxisController;
        private ISerialAxisController _rAxisController;
        private SerialAxisViewModel _xAxisViewModel;
        private SerialAxisViewModel _yAxisViewModel;
        private SerialAxisViewModel _rAxisViewModel;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SerialAxisViewModelTest(
            ILoggingService loggingService,
            ISerialAxisControllerFactory axisControllerFactory,
            WaferAligner.Common.ResourceManager resourceManager)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _axisControllerFactory = axisControllerFactory ?? throw new ArgumentNullException(nameof(axisControllerFactory));
            _resourceManager = resourceManager ?? throw new ArgumentNullException(nameof(resourceManager));
        }
        
        /// <summary>
        /// 初始化测试环境
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                _loggingService.LogInformation("开始初始化SerialAxisViewModel测试环境", Axis_Move_Started);
                
                // 创建轴控制器
                _xAxisController = await _axisControllerFactory.CreateAxisControllerAsync("X");
                _yAxisController = await _axisControllerFactory.CreateAxisControllerAsync("Y");
                _rAxisController = await _axisControllerFactory.CreateAxisControllerAsync("R");
                
                // 注册资源
                _resourceManager.RegisterResource("XAxisController", _xAxisController);
                _resourceManager.RegisterResource("YAxisController", _yAxisController);
                _resourceManager.RegisterResource("RAxisController", _rAxisController);
                
                // 创建视图模型
                _xAxisViewModel = new SerialAxisViewModel(_xAxisController, _loggingService);
                _yAxisViewModel = new SerialAxisViewModel(_yAxisController, _loggingService);
                _rAxisViewModel = new SerialAxisViewModel(_rAxisController, _loggingService);
                
                _loggingService.LogInformation("SerialAxisViewModel测试环境初始化完成", Axis_Move_Started);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "初始化SerialAxisViewModel测试环境失败", Axis_Move_Error);
                throw;
            }
        }
        
        /// <summary>
        /// 运行功能测试
        /// </summary>
        public async Task RunTest()
        {
            try
            {
                await InitializeAsync();
                
                _loggingService.LogInformation("开始SerialAxisViewModel功能测试", Axis_Move_Started);
                
                // 测试：检查连接状态
                bool xConnected = _xAxisViewModel.IsConnected;
                bool yConnected = _yAxisViewModel.IsConnected;
                bool rConnected = _rAxisViewModel.IsConnected;
                
                _loggingService.LogInformation($"连接状态：X={xConnected}, Y={yConnected}, R={rConnected}", Axis_Move_Started);
                
                if (!(xConnected && yConnected && rConnected))
                {
                    _loggingService.LogWarning("至少有一个轴未连接，测试可能无法全部完成", Axis_Move_Error);
                }
                
                // 测试：初始化轴
                await TestInitAxis();
                
                // 测试：设置位置
                await TestSetPosition();
                
                // 测试：移动
                await TestMoveTo();
                
                // 测试：停止
                await TestStop();
                
                // 测试：XyrAxisViewModel兼容方法
                await TestCompatibilityMethods();
                
                _loggingService.LogInformation("SerialAxisViewModel功能测试完成", Axis_Move_Started);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "SerialAxisViewModel功能测试异常", Axis_Move_Error);
            }
        }
        
        /// <summary>
        /// 测试初始化轴
        /// </summary>
        private async Task TestInitAxis()
        {
            try
            {
                _loggingService.LogInformation("测试轴初始化", Axis_Move_Started);
                
                bool xResult = await _xAxisViewModel.InitAxisAsync();
                _loggingService.LogInformation($"X轴初始化结果：{xResult}", Axis_Move_Started);
                
                bool yResult = await _yAxisViewModel.InitAxisAsync();
                _loggingService.LogInformation($"Y轴初始化结果：{yResult}", Axis_Move_Started);
                
                bool rResult = await _rAxisViewModel.InitAxisAsync();
                _loggingService.LogInformation($"R轴初始化结果：{rResult}", Axis_Move_Started);
                
                // 等待一下，确保使能状态更新
                await Task.Delay(500);
                
                // 检查使能状态
                bool xEnabled = _xAxisViewModel.IsEnabled;
                bool yEnabled = _yAxisViewModel.IsEnabled;
                bool rEnabled = _rAxisViewModel.IsEnabled;
                
                _loggingService.LogInformation($"使能状态：X={xEnabled}, Y={yEnabled}, R={rEnabled}", Axis_Move_Started);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "测试轴初始化异常", Axis_Move_Error);
            }
        }
        
        /// <summary>
        /// 测试设置位置
        /// </summary>
        private async Task TestSetPosition()
        {
            try
            {
                _loggingService.LogInformation("测试设置位置", Axis_Move_Started);
                
                // 设置目标位置
                int xResult = _xAxisViewModel.SetPosition(1000);
                int yResult = _yAxisViewModel.SetPosition(2000);
                int rResult = _rAxisViewModel.SetPosition(3000);
                
                _loggingService.LogInformation($"设置位置结果：X={xResult}, Y={yResult}, R={rResult}", Axis_Move_Started);
                
                // 异步方法
                bool xAsyncResult = await _xAxisViewModel.SetPositionAsync(1500);
                bool yAsyncResult = await _yAxisViewModel.SetPositionAsync(2500);
                bool rAsyncResult = await _rAxisViewModel.SetPositionAsync(3500);
                
                _loggingService.LogInformation($"异步设置位置结果：X={xAsyncResult}, Y={yAsyncResult}, R={rAsyncResult}", Axis_Move_Started);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "测试设置位置异常", Axis_Move_Error);
            }
        }
        
        /// <summary>
        /// 测试移动到位置
        /// </summary>
        private async Task TestMoveTo()
        {
            try
            {
                _loggingService.LogInformation("测试移动到位置", Axis_Move_Started);
                
                // 获取当前位置
                int xPosition = _xAxisViewModel.GetPosition();
                int yPosition = _yAxisViewModel.GetPosition();
                int rPosition = _rAxisViewModel.GetPosition();
                
                _loggingService.LogInformation($"当前位置：X={xPosition}, Y={yPosition}, R={rPosition}", Axis_Move_Started);
                
                // 设置相对移动小距离
                int xMove = xPosition + 100;
                int yMove = yPosition + 100;
                int rMove = rPosition + 100;
                
                // 设置目标位置
                _xAxisViewModel.SetPosition(xMove);
                _yAxisViewModel.SetPosition(yMove);
                _rAxisViewModel.SetPosition(rMove);
                
                // 执行移动
                int xResult = _xAxisViewModel.GoPosition();
                _loggingService.LogInformation($"X轴移动到{xMove}结果：{xResult}", Axis_Move_Started);
                
                // 等待运动完成
                await Task.Delay(1000);
                
                // 检查是否到位
                xPosition = _xAxisViewModel.GetPosition();
                _loggingService.LogInformation($"移动后X轴位置：{xPosition}", Axis_Move_Started);
                
                // 使用异步方法
                bool yResult = await _yAxisViewModel.MoveToPositionAsync(yMove / 10500.0);
                _loggingService.LogInformation($"Y轴异步移动结果：{yResult}", Axis_Move_Started);
                
                // 等待运动完成
                await Task.Delay(1000);
                
                // 检查是否到位
                var yCurrentPos = await _yAxisViewModel.GetCurrentPositionAsync();
                _loggingService.LogInformation($"移动后Y轴位置：{yCurrentPos}", Axis_Move_Started);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "测试移动到位置异常", Axis_Move_Error);
            }
        }
        
        /// <summary>
        /// 测试停止
        /// </summary>
        private async Task TestStop()
        {
            try
            {
                _loggingService.LogInformation("测试停止", Axis_Move_Started);
                
                // 设置较远的目标位置以便有时间停止
                _rAxisViewModel.SetPosition(_rAxisViewModel.GetPosition() + 5000);
                
                // 开始移动
                int rResult = _rAxisViewModel.GoPosition();
                _loggingService.LogInformation($"R轴开始长距离移动结果：{rResult}", Axis_Move_Started);
                
                // 等待轴开始移动
                await Task.Delay(500);
                
                // 停止
                int stopResult = _rAxisViewModel.Stop();
                _loggingService.LogInformation($"R轴停止结果：{stopResult}", Axis_Move_Started);
                
                // 等待处理完成
                await Task.Delay(500);
                
                // 检查运行状态
                int runState = _rAxisViewModel.GetRunState();
                _loggingService.LogInformation($"停止后R轴运行状态：{runState}", Axis_Move_Started);
                
                // 检查是否停止
                bool isArrived = _rAxisViewModel.Arrive_position;
                _loggingService.LogInformation($"停止后R轴是否到位状态：{isArrived}", Axis_Move_Started);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "测试停止异常", Axis_Move_Error);
            }
        }
        
        /// <summary>
        /// 测试XyrAxisViewModel兼容方法
        /// </summary>
        private async Task TestCompatibilityMethods()
        {
            try
            {
                _loggingService.LogInformation("测试XyrAxisViewModel兼容方法", Axis_Move_Started);
                
                // 获取报警状态
                int xAlarm = _xAxisViewModel.GetAlarmState();
                int yAlarm = _yAxisViewModel.GetAlarmState();
                int rAlarm = _rAxisViewModel.GetAlarmState();
                
                _loggingService.LogInformation($"报警状态：X={xAlarm}, Y={yAlarm}, R={rAlarm}", Axis_Move_Started);
                
                // 获取使能状态
                int xEnable = _xAxisViewModel.GetEnableState();
                int yEnable = _yAxisViewModel.GetEnableState();
                int rEnable = _rAxisViewModel.GetEnableState();
                
                _loggingService.LogInformation($"使能状态：X={xEnable}, Y={yEnable}, R={rEnable}", Axis_Move_Started);
                
                // 异步获取报警状态
                int xAlarmAsync = await _xAxisViewModel.GetAlarmStateAsync();
                _loggingService.LogInformation($"异步获取X轴报警状态：{xAlarmAsync}", Axis_Move_Started);
                
                // 异步获取使能状态
                int yEnableAsync = await _yAxisViewModel.GetEnableStateAsync();
                _loggingService.LogInformation($"异步获取Y轴使能状态：{yEnableAsync}", Axis_Move_Started);
                
                // 异步获取运行状态
                int rRunStateAsync = await _rAxisViewModel.GetRunStateAsync();
                _loggingService.LogInformation($"异步获取R轴运行状态：{rRunStateAsync}", Axis_Move_Started);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "测试XyrAxisViewModel兼容方法异常", Axis_Move_Error);
            }
        }
        
        /// <summary>
        /// 显示测试结果
        /// </summary>
        public void ShowResults()
        {
            MessageBox.Show(
                $"X轴: {(_xAxisViewModel.IsConnected ? "已连接" : "未连接")}, 位置: {_xAxisViewModel.GetPosition()}\n" +
                $"Y轴: {(_yAxisViewModel.IsConnected ? "已连接" : "未连接")}, 位置: {_yAxisViewModel.GetPosition()}\n" +
                $"R轴: {(_rAxisViewModel.IsConnected ? "已连接" : "未连接")}, 位置: {_rAxisViewModel.GetPosition()}\n\n" +
                "详细测试结果请查看日志。",
                "SerialAxisViewModel测试结果",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }
    }
} 