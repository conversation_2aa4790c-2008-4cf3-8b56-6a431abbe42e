﻿namespace Sunny.UI.Demo
{
    partial class FTitlePage3
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            gbxMain = new GroupBox();
            uiTabControlMenu1 = new UITabControlMenu();
            tPX = new TabPage();
            步进电机调试X = new WaferAligner.AxisControl(components);
            tPY = new TabPage();
            步进电机调试Y = new WaferAligner.AxisControl(components);
            tPR = new TabPage();
            步进电机调试R = new WaferAligner.AxisControl(components);
            tPZ = new TabPage();
            步进电机调试Z = new WaferAligner.AxisControl(components);
            tPLX = new TabPage();
            步进电机调试LX = new WaferAligner.AxisControl(components);
            tPLY = new TabPage();
            步进电机调试LY = new WaferAligner.AxisControl(components);
            tPLZ = new TabPage();
            步进电机调试LZ = new WaferAligner.AxisControl(components);
            tPRX = new TabPage();
            步进电机调试RX = new WaferAligner.AxisControl(components);
            tPRY = new TabPage();
            步进电机调试RY = new WaferAligner.AxisControl(components);
            tPRZ = new TabPage();
            步进电机调试RZ = new WaferAligner.AxisControl(components);
            LalChuckLockState = new Label();
            groupBox1 = new GroupBox();
            tableLayoutPanel5 = new TableLayoutPanel();
            groupBox2 = new GroupBox();
            tableLayoutPanel3 = new TableLayoutPanel();
            gbxVisual = new GroupBox();
            tableLayoutPanel4 = new TableLayoutPanel();
            panel41 = new Panel();
            BtnPlat标定 = new Button();
            groupBox4 = new GroupBox();
            tableLayoutPanel8 = new TableLayoutPanel();
            panel15 = new Panel();
            lalCameraOffset = new Label();
            panel47 = new Panel();
            LalGap = new Label();
            panel48 = new Panel();
            label10 = new Label();
            TxtTopGap = new UITextBox();
            panel36 = new Panel();
            label3 = new Label();
            TxtZOffset = new UITextBox();
            panel40 = new Panel();
            LalOffset = new Label();
            panel12 = new Panel();
            label11 = new Label();
            TxtBottomGap = new UITextBox();
            panel9 = new Panel();
            label9 = new Label();
            panel8 = new Panel();
            label20 = new Label();
            TxtCameraOffset = new UITextBox();
            panel7 = new Panel();
            lalBottomPhotoOffset = new Label();
            panel13 = new Panel();
            label19 = new Label();
            TxtBottomPhotoOffset = new UITextBox();
            LalTrayState = new Label();
            LalTopWaferState = new Label();
            LalTrayWaferInnerState = new Label();
            panel18 = new Panel();
            LightTrayWaferInner = new UILight();
            panel19 = new Panel();
            LightTray = new UILight();
            panel17 = new Panel();
            LightTopWafer = new UILight();
            groupBox3 = new GroupBox();
            tableLayoutPanel6 = new TableLayoutPanel();
            panel31 = new Panel();
            LalTrayWaferOuterState = new Label();
            panel30 = new Panel();
            LightTrayWaferOuter = new UILight();
            panel26 = new Panel();
            BtnTopWafer_Release = new Button();
            BtnTopWafer = new Button();
            panel27 = new Panel();
            panel29 = new Panel();
            BtnTrayWaferOuter = new Button();
            panel28 = new Panel();
            BtnTrayWaferInner = new Button();
            panel32 = new Panel();
            panel24 = new Panel();
            BtnTray = new Button();
            panel33 = new Panel();
            BtnChuckLock = new Button();
            panel37 = new Panel();
            BtnHorizontalAdjustLock = new Button();
            panel1 = new Panel();
            LightChuckLock = new UILight();
            panel39 = new Panel();
            LightHorizontalAdjustLock = new UILight();
            panel25 = new Panel();
            panel34 = new Panel();
            panel42 = new Panel();
            LalHorizontalAdjustLockState = new Label();
            groupBox5 = new GroupBox();
            tableLayoutPanel7 = new TableLayoutPanel();
            panel14 = new Panel();
            BtnCalPos = new Button();
            panel35 = new Panel();
            BtnFiveStop = new Button();
            panel10 = new Panel();
            BtnCal = new Button();
            tableLayoutPanel1 = new TableLayoutPanel();
            panel6 = new Panel();
            label12 = new Label();
            TxtRX = new UITextBox();
            label6 = new Label();
            panel23 = new Panel();
            label18 = new Label();
            TxtRZ = new UITextBox();
            label7 = new Label();
            panel2 = new Panel();
            label13 = new Label();
            TxtLX = new UITextBox();
            label4 = new Label();
            panel4 = new Panel();
            label17 = new Label();
            TxtLZ = new UITextBox();
            label5 = new Label();
            panel21 = new Panel();
            label16 = new Label();
            TxtRY = new UITextBox();
            label2 = new Label();
            panel20 = new Panel();
            label15 = new Label();
            TxtLY = new UITextBox();
            label1 = new Label();
            panel3 = new Panel();
            BtnSave = new Button();
            BtnOpen = new Button();
            tmrInput = new System.Windows.Forms.Timer(components);
            // timer1 = new System.Windows.Forms.Timer(components); // 已被_CalTimer替代
            gbxMain.SuspendLayout();
            uiTabControlMenu1.SuspendLayout();
            tPX.SuspendLayout();
            tPY.SuspendLayout();
            tPR.SuspendLayout();
            tPZ.SuspendLayout();
            tPLX.SuspendLayout();
            tPLY.SuspendLayout();
            tPLZ.SuspendLayout();
            tPRX.SuspendLayout();
            tPRY.SuspendLayout();
            tPRZ.SuspendLayout();
            panel41.SuspendLayout();
            groupBox4.SuspendLayout();
            tableLayoutPanel8.SuspendLayout();
            panel15.SuspendLayout();
            panel47.SuspendLayout();
            panel48.SuspendLayout();
            panel36.SuspendLayout();
            panel40.SuspendLayout();
            panel12.SuspendLayout();
            panel9.SuspendLayout();
            panel8.SuspendLayout();
            panel7.SuspendLayout();
            panel13.SuspendLayout();
            panel18.SuspendLayout();
            panel19.SuspendLayout();
            panel17.SuspendLayout();
            groupBox3.SuspendLayout();
            tableLayoutPanel6.SuspendLayout();
            panel31.SuspendLayout();
            panel30.SuspendLayout();
            panel26.SuspendLayout();
            panel27.SuspendLayout();
            panel29.SuspendLayout();
            panel28.SuspendLayout();
            panel32.SuspendLayout();
            panel24.SuspendLayout();
            panel33.SuspendLayout();
            panel37.SuspendLayout();
            panel1.SuspendLayout();
            panel39.SuspendLayout();
            panel25.SuspendLayout();
            panel34.SuspendLayout();
            panel42.SuspendLayout();
            groupBox5.SuspendLayout();
            tableLayoutPanel7.SuspendLayout();
            panel14.SuspendLayout();
            panel35.SuspendLayout();
            panel10.SuspendLayout();
            tableLayoutPanel1.SuspendLayout();
            panel6.SuspendLayout();
            panel23.SuspendLayout();
            panel2.SuspendLayout();
            panel4.SuspendLayout();
            panel21.SuspendLayout();
            panel20.SuspendLayout();
            panel3.SuspendLayout();
            SuspendLayout();
            // 
            // gbxMain
            // 
            gbxMain.Controls.Add(uiTabControlMenu1);
            gbxMain.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            gbxMain.Location = new Point(42, 54);
            gbxMain.Name = "gbxMain";
            gbxMain.Size = new Size(993, 820);
            gbxMain.TabIndex = 83;
            gbxMain.TabStop = false;
            gbxMain.Text = "运动调试";
            // 
            // uiTabControlMenu1
            // 
            uiTabControlMenu1.Alignment = TabAlignment.Left;
            uiTabControlMenu1.Controls.Add(tPX);
            uiTabControlMenu1.Controls.Add(tPY);
            uiTabControlMenu1.Controls.Add(tPR);
            uiTabControlMenu1.Controls.Add(tPZ);
            uiTabControlMenu1.Controls.Add(tPLX);
            uiTabControlMenu1.Controls.Add(tPLY);
            uiTabControlMenu1.Controls.Add(tPLZ);
            uiTabControlMenu1.Controls.Add(tPRX);
            uiTabControlMenu1.Controls.Add(tPRY);
            uiTabControlMenu1.Controls.Add(tPRZ);
            uiTabControlMenu1.DrawMode = TabDrawMode.OwnerDrawFixed;
            uiTabControlMenu1.FillColor = Color.FromArgb(248, 248, 248);
            uiTabControlMenu1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTabControlMenu1.ItemSize = new Size(50, 40);
            uiTabControlMenu1.Location = new Point(35, 37);
            uiTabControlMenu1.MenuStyle = UIMenuStyle.Custom;
            uiTabControlMenu1.Multiline = true;
            uiTabControlMenu1.Name = "uiTabControlMenu1";
            uiTabControlMenu1.SelectedIndex = 0;
            uiTabControlMenu1.Size = new Size(922, 767);
            uiTabControlMenu1.SizeMode = TabSizeMode.Fixed;
            uiTabControlMenu1.Style = UIStyle.Custom;
            uiTabControlMenu1.TabBackColor = Color.FromArgb(76, 76, 76);
            uiTabControlMenu1.TabIndex = 72;
            uiTabControlMenu1.TabSelectedForeColor = Color.FromArgb(140, 140, 140);
            uiTabControlMenu1.TabSelectedHighColor = Color.FromArgb(140, 140, 140);
            uiTabControlMenu1.TextAlignment = HorizontalAlignment.Left;
            // 
            // tPX
            // 
            tPX.BackColor = Color.FromArgb(248, 248, 248);
            tPX.Controls.Add(步进电机调试X);
            tPX.Location = new Point(51, 0);
            tPX.Name = "tPX";
            tPX.Size = new Size(871, 767);
            tPX.TabIndex = 0;
            tPX.Text = "X";
            // 
            // 步进电机调试X
            // 
            步进电机调试X.Alarm_Color = Color.Green;
            步进电机调试X.AlarmState = "";
            步进电机调试X.BtnZeroEnable = true;
            步进电机调试X.CurenttPos = "";
            步进电机调试X.CurenttVel = "";
            步进电机调试X.CurrentPos = "";
            步进电机调试X.CurrentSpeed = "";
            步进电机调试X.Enable_Color = Color.Green;
            步进电机调试X.EnableAddress = "";
            步进电机调试X.EnableState = "";
            步进电机调试X.ID = 0;
            步进电机调试X.JOGBack = "";
            步进电机调试X.JOGFront = "";
            步进电机调试X.JogSpeed = 0D;
            步进电机调试X.JogSpeedMax = 30D;
            步进电机调试X.JogSpeedMin = 0D;
            步进电机调试X.JogVel = "";
            步进电机调试X.JogVelAddress = "";
            步进电机调试X.Location = new Point(78, 96);
            步进电机调试X.MoveAbsAddress = "";
            步进电机调试X.MoveAbsState = "";
            步进电机调试X.Name = "步进电机调试X";
            步进电机调试X.Pos = 0D;
            步进电机调试X.PosComplete_Bool = false;
            步进电机调试X.PosComplete_Color = Color.Green;
            步进电机调试X.PosMax = 0D;
            步进电机调试X.PosMin = 0D;
            步进电机调试X.Ready_Color = Color.Green;
            步进电机调试X.ReadyState = "";
            步进电机调试X.Size = new Size(714, 575);
            步进电机调试X.Speed = 0D;
            步进电机调试X.SpeedMax = 0D;
            步进电机调试X.SpeedMin = 0D;
            步进电机调试X.StopAddress = "";
            步进电机调试X.TabIndex = 0;
            步进电机调试X.TargetPos = "";
            步进电机调试X.V_Test = "X（步进）参数";
            步进电机调试X.VeloAddress = "";
            步进电机调试X.Zero_Color = Color.Green;
            步进电机调试X.ZeroAddress = "";
            步进电机调试X.ZeroState = "";
            // 
            // tPY
            // 
            tPY.BackColor = Color.FromArgb(248, 248, 248);
            tPY.Controls.Add(步进电机调试Y);
            tPY.Location = new Point(51, 0);
            tPY.Name = "tPY";
            tPY.Size = new Size(871, 767);
            tPY.TabIndex = 1;
            tPY.Text = "Y";
            // 
            // 步进电机调试Y
            // 
            步进电机调试Y.Alarm_Color = Color.Green;
            步进电机调试Y.AlarmState = "";
            步进电机调试Y.BtnZeroEnable = true;
            步进电机调试Y.CurenttPos = "";
            步进电机调试Y.CurenttVel = "";
            步进电机调试Y.CurrentPos = "";
            步进电机调试Y.CurrentSpeed = "";
            步进电机调试Y.Enable_Color = Color.Green;
            步进电机调试Y.EnableAddress = "";
            步进电机调试Y.EnableState = "";
            步进电机调试Y.ID = 1;
            步进电机调试Y.JOGBack = "";
            步进电机调试Y.JOGFront = "";
            步进电机调试Y.JogSpeed = 0D;
            步进电机调试Y.JogSpeedMax = 10D;
            步进电机调试Y.JogSpeedMin = 0D;
            步进电机调试Y.JogVel = "";
            步进电机调试Y.JogVelAddress = "";
            步进电机调试Y.Location = new Point(78, 96);
            步进电机调试Y.MoveAbsAddress = "";
            步进电机调试Y.MoveAbsState = "";
            步进电机调试Y.Name = "步进电机调试Y";
            步进电机调试Y.Pos = 0D;
            步进电机调试Y.PosComplete_Bool = false;
            步进电机调试Y.PosComplete_Color = Color.Green;
            步进电机调试Y.PosMax = 0D;
            步进电机调试Y.PosMin = 0D;
            步进电机调试Y.Ready_Color = Color.Green;
            步进电机调试Y.ReadyState = "";
            步进电机调试Y.Size = new Size(714, 575);
            步进电机调试Y.Speed = 0D;
            步进电机调试Y.SpeedMax = 0D;
            步进电机调试Y.SpeedMin = 0D;
            步进电机调试Y.StopAddress = "";
            步进电机调试Y.TabIndex = 0;
            步进电机调试Y.TargetPos = "";
            步进电机调试Y.V_Test = "Y（步进）参数";
            步进电机调试Y.VeloAddress = "";
            步进电机调试Y.Zero_Color = Color.Green;
            步进电机调试Y.ZeroAddress = "";
            步进电机调试Y.ZeroState = "";
            // 
            // tPR
            // 
            tPR.BackColor = Color.FromArgb(248, 248, 248);
            tPR.Controls.Add(步进电机调试R);
            tPR.Location = new Point(51, 0);
            tPR.Name = "tPR";
            tPR.Size = new Size(871, 767);
            tPR.TabIndex = 2;
            tPR.Text = "R";
            // 
            // 步进电机调试R
            // 
            步进电机调试R.Alarm_Color = Color.Green;
            步进电机调试R.AlarmState = "";
            步进电机调试R.BtnZeroEnable = true;
            步进电机调试R.CurenttPos = "";
            步进电机调试R.CurenttVel = "";
            步进电机调试R.CurrentPos = "";
            步进电机调试R.CurrentSpeed = "";
            步进电机调试R.Enable_Color = Color.Green;
            步进电机调试R.EnableAddress = "";
            步进电机调试R.EnableState = "";
            步进电机调试R.ID = 2;
            步进电机调试R.JOGBack = "";
            步进电机调试R.JOGFront = "";
            步进电机调试R.JogSpeed = 0D;
            步进电机调试R.JogSpeedMax = 10D;
            步进电机调试R.JogSpeedMin = 0D;
            步进电机调试R.JogVel = "";
            步进电机调试R.JogVelAddress = "";
            步进电机调试R.Location = new Point(78, 96);
            步进电机调试R.MoveAbsAddress = "";
            步进电机调试R.MoveAbsState = "";
            步进电机调试R.Name = "步进电机调试R";
            步进电机调试R.Pos = 0D;
            步进电机调试R.PosComplete_Bool = false;
            步进电机调试R.PosComplete_Color = Color.Green;
            步进电机调试R.PosMax = 0D;
            步进电机调试R.PosMin = 0D;
            步进电机调试R.Ready_Color = Color.Green;
            步进电机调试R.ReadyState = "";
            步进电机调试R.Size = new Size(714, 575);
            步进电机调试R.Speed = 0D;
            步进电机调试R.SpeedMax = 0D;
            步进电机调试R.SpeedMin = 0D;
            步进电机调试R.StopAddress = "";
            步进电机调试R.TabIndex = 0;
            步进电机调试R.TargetPos = "";
            步进电机调试R.V_Test = "R（步进）参数";
            步进电机调试R.VeloAddress = "";
            步进电机调试R.Zero_Color = Color.Green;
            步进电机调试R.ZeroAddress = "";
            步进电机调试R.ZeroState = "";
            // 
            // tPZ
            // 
            tPZ.BackColor = Color.FromArgb(248, 248, 248);
            tPZ.Controls.Add(步进电机调试Z);
            tPZ.Location = new Point(51, 0);
            tPZ.Name = "tPZ";
            tPZ.Size = new Size(871, 767);
            tPZ.TabIndex = 3;
            tPZ.Text = "Z";
            // 
            // 步进电机调试Z
            // 
            步进电机调试Z.Alarm_Color = Color.Green;
            步进电机调试Z.AlarmState = "ZAxisError";
            步进电机调试Z.BtnZeroEnable = false;
            步进电机调试Z.CurenttPos = "";
            步进电机调试Z.CurenttVel = "";
            步进电机调试Z.CurrentPos = "ZRealDistance";
            步进电机调试Z.CurrentSpeed = "ZRealVelo";
            步进电机调试Z.Enable_Color = Color.Green;
            步进电机调试Z.EnableAddress = "";
            步进电机调试Z.EnableState = "ZIsEnable";
            步进电机调试Z.ID = 3;
            步进电机调试Z.JOGBack = "ZAxisActions";
            步进电机调试Z.JOGFront = "ZAxisActions";
            步进电机调试Z.JogSpeed = 0D;
            步进电机调试Z.JogSpeedMax = 30D;
            步进电机调试Z.JogSpeedMin = 0D;
            步进电机调试Z.JogVel = "0";
            步进电机调试Z.JogVelAddress = "ZJogVelo";
            步进电机调试Z.Location = new Point(78, 96);
            步进电机调试Z.MoveAbsAddress = "ZAxisActions";
            步进电机调试Z.MoveAbsState = "ZAbsDone";
            步进电机调试Z.Name = "步进电机调试Z";
            步进电机调试Z.Pos = 0D;
            步进电机调试Z.PosComplete_Bool = false;
            步进电机调试Z.PosComplete_Color = Color.Green;
            步进电机调试Z.PosMax = 0D;
            步进电机调试Z.PosMin = 0D;
            步进电机调试Z.Ready_Color = Color.Green;
            步进电机调试Z.ReadyState = "ZIsReady";
            步进电机调试Z.Size = new Size(714, 575);
            步进电机调试Z.Speed = 0D;
            步进电机调试Z.SpeedMax = 0D;
            步进电机调试Z.SpeedMin = 0D;
            步进电机调试Z.StopAddress = "ZAxisActions";
            步进电机调试Z.TabIndex = 0;
            步进电机调试Z.TargetPos = "ZTarget";
            步进电机调试Z.V_Test = "Z（伺服）参数";
            步进电机调试Z.VeloAddress = "ZVelo";
            步进电机调试Z.Zero_Color = Color.Green;
            步进电机调试Z.ZeroAddress = "ZAxisActions";
            步进电机调试Z.ZeroState = "ZHomeSetDone";
            // 
            // tPLX
            // 
            tPLX.BackColor = Color.FromArgb(248, 248, 248);
            tPLX.Controls.Add(步进电机调试LX);
            tPLX.Location = new Point(51, 0);
            tPLX.Name = "tPLX";
            tPLX.Size = new Size(871, 767);
            tPLX.TabIndex = 4;
            tPLX.Text = "LX";
            // 
            // 步进电机调试LX
            // 
            步进电机调试LX.Alarm_Color = Color.Green;
            步进电机调试LX.AlarmState = "LXAxisError";
            步进电机调试LX.BtnZeroEnable = false;
            步进电机调试LX.CurenttPos = "";
            步进电机调试LX.CurenttVel = "";
            步进电机调试LX.CurrentPos = "LXRealDistance";
            步进电机调试LX.CurrentSpeed = "LXRealVelo";
            步进电机调试LX.Enable_Color = Color.Green;
            步进电机调试LX.EnableAddress = "";
            步进电机调试LX.EnableState = "LXIsEnable";
            步进电机调试LX.ID = 4;
            步进电机调试LX.JOGBack = "LXAxisActions";
            步进电机调试LX.JOGFront = "LXAxisActions";
            步进电机调试LX.JogSpeed = 0D;
            步进电机调试LX.JogSpeedMax = 30D;
            步进电机调试LX.JogSpeedMin = 0D;
            步进电机调试LX.JogVel = "0";
            步进电机调试LX.JogVelAddress = "LXJogVelo";
            步进电机调试LX.Location = new Point(78, 96);
            步进电机调试LX.MoveAbsAddress = "LXAxisActions";
            步进电机调试LX.MoveAbsState = "LXAbsDone";
            步进电机调试LX.Name = "步进电机调试LX";
            步进电机调试LX.Pos = 0D;
            步进电机调试LX.PosComplete_Bool = false;
            步进电机调试LX.PosComplete_Color = Color.Green;
            步进电机调试LX.PosMax = 0D;
            步进电机调试LX.PosMin = 0D;
            步进电机调试LX.Ready_Color = Color.Green;
            步进电机调试LX.ReadyState = "LXIsReady";
            步进电机调试LX.Size = new Size(714, 575);
            步进电机调试LX.Speed = 0D;
            步进电机调试LX.SpeedMax = 0D;
            步进电机调试LX.SpeedMin = 0D;
            步进电机调试LX.StopAddress = "LXAxisActions";
            步进电机调试LX.TabIndex = 0;
            步进电机调试LX.TargetPos = "LXTarget";
            步进电机调试LX.V_Test = "LX（伺服）参数";
            步进电机调试LX.VeloAddress = "LXVelo";
            步进电机调试LX.Zero_Color = Color.Green;
            步进电机调试LX.ZeroAddress = "LXAxisActions";
            步进电机调试LX.ZeroState = "LXHomeSetDone";
            // 
            // tPLY
            // 
            tPLY.BackColor = Color.FromArgb(248, 248, 248);
            tPLY.Controls.Add(步进电机调试LY);
            tPLY.Location = new Point(51, 0);
            tPLY.Name = "tPLY";
            tPLY.Size = new Size(871, 767);
            tPLY.TabIndex = 5;
            tPLY.Text = "LY";
            // 
            // 步进电机调试LY
            // 
            步进电机调试LY.Alarm_Color = Color.Green;
            步进电机调试LY.AlarmState = "LYAxisError";
            步进电机调试LY.BtnZeroEnable = false;
            步进电机调试LY.CurenttPos = "";
            步进电机调试LY.CurenttVel = "";
            步进电机调试LY.CurrentPos = "LYRealDistance";
            步进电机调试LY.CurrentSpeed = "LYRealVelo";
            步进电机调试LY.Enable_Color = Color.Green;
            步进电机调试LY.EnableAddress = "";
            步进电机调试LY.EnableState = "LYIsEnable";
            步进电机调试LY.ID = 5;
            步进电机调试LY.JOGBack = "LYAxisActions";
            步进电机调试LY.JOGFront = "LYAxisActions";
            步进电机调试LY.JogSpeed = 0D;
            步进电机调试LY.JogSpeedMax = 30D;
            步进电机调试LY.JogSpeedMin = 0D;
            步进电机调试LY.JogVel = "0";
            步进电机调试LY.JogVelAddress = "LYJogVelo";
            步进电机调试LY.Location = new Point(78, 96);
            步进电机调试LY.MoveAbsAddress = "LYAxisActions";
            步进电机调试LY.MoveAbsState = "LYAbsDone";
            步进电机调试LY.Name = "步进电机调试LY";
            步进电机调试LY.Pos = 0D;
            步进电机调试LY.PosComplete_Bool = false;
            步进电机调试LY.PosComplete_Color = Color.Green;
            步进电机调试LY.PosMax = 0D;
            步进电机调试LY.PosMin = 0D;
            步进电机调试LY.Ready_Color = Color.Green;
            步进电机调试LY.ReadyState = "LYIsReady";
            步进电机调试LY.Size = new Size(714, 575);
            步进电机调试LY.Speed = 0D;
            步进电机调试LY.SpeedMax = 0D;
            步进电机调试LY.SpeedMin = 0D;
            步进电机调试LY.StopAddress = "LYAxisActions";
            步进电机调试LY.TabIndex = 0;
            步进电机调试LY.TargetPos = "LYTarget";
            步进电机调试LY.V_Test = "LY（伺服）参数";
            步进电机调试LY.VeloAddress = "LYVelo";
            步进电机调试LY.Zero_Color = Color.Green;
            步进电机调试LY.ZeroAddress = "LYAxisActions";
            步进电机调试LY.ZeroState = "LYHomeSetDone";
            // 
            // tPLZ
            // 
            tPLZ.BackColor = Color.FromArgb(248, 248, 248);
            tPLZ.Controls.Add(步进电机调试LZ);
            tPLZ.Location = new Point(51, 0);
            tPLZ.Name = "tPLZ";
            tPLZ.Size = new Size(871, 767);
            tPLZ.TabIndex = 6;
            tPLZ.Text = "LZ";
            // 
            // 步进电机调试LZ
            // 
            步进电机调试LZ.Alarm_Color = Color.Green;
            步进电机调试LZ.AlarmState = "LZAxisError";
            步进电机调试LZ.BtnZeroEnable = true;
            步进电机调试LZ.CurenttPos = "";
            步进电机调试LZ.CurenttVel = "";
            步进电机调试LZ.CurrentPos = "LZRealDistance";
            步进电机调试LZ.CurrentSpeed = "LZRealVelo";
            步进电机调试LZ.Enable_Color = Color.Green;
            步进电机调试LZ.EnableAddress = "";
            步进电机调试LZ.EnableState = "LZIsEnable";
            步进电机调试LZ.ID = 6;
            步进电机调试LZ.JOGBack = "LZAxisActions";
            步进电机调试LZ.JOGFront = "LZAxisActions";
            步进电机调试LZ.JogSpeed = 0D;
            步进电机调试LZ.JogSpeedMax = 30D;
            步进电机调试LZ.JogSpeedMin = 0D;
            步进电机调试LZ.JogVel = "0";
            步进电机调试LZ.JogVelAddress = "LZJogVelo";
            步进电机调试LZ.Location = new Point(78, 96);
            步进电机调试LZ.MoveAbsAddress = "LZAxisActions";
            步进电机调试LZ.MoveAbsState = "LZAbsDone";
            步进电机调试LZ.Name = "步进电机调试LZ";
            步进电机调试LZ.Pos = 0D;
            步进电机调试LZ.PosComplete_Bool = false;
            步进电机调试LZ.PosComplete_Color = Color.Green;
            步进电机调试LZ.PosMax = 0D;
            步进电机调试LZ.PosMin = 0D;
            步进电机调试LZ.Ready_Color = Color.Green;
            步进电机调试LZ.ReadyState = "LZIsReady";
            步进电机调试LZ.Size = new Size(714, 575);
            步进电机调试LZ.Speed = 0D;
            步进电机调试LZ.SpeedMax = 0D;
            步进电机调试LZ.SpeedMin = 0D;
            步进电机调试LZ.StopAddress = "LZAxisActions";
            步进电机调试LZ.TabIndex = 0;
            步进电机调试LZ.TargetPos = "LZTarget";
            步进电机调试LZ.V_Test = "LZ（步进）参数";
            步进电机调试LZ.VeloAddress = "LZVelo";
            步进电机调试LZ.Zero_Color = Color.Green;
            步进电机调试LZ.ZeroAddress = "LZAxisActions";
            步进电机调试LZ.ZeroState = "LZHomeSetDone";
            // 
            // tPRX
            // 
            tPRX.BackColor = Color.FromArgb(248, 248, 248);
            tPRX.Controls.Add(步进电机调试RX);
            tPRX.Location = new Point(51, 0);
            tPRX.Name = "tPRX";
            tPRX.Size = new Size(871, 767);
            tPRX.TabIndex = 7;
            tPRX.Text = "RX";
            // 
            // 步进电机调试RX
            // 
            步进电机调试RX.Alarm_Color = Color.Green;
            步进电机调试RX.AlarmState = "RXAxisError";
            步进电机调试RX.BtnZeroEnable = false;
            步进电机调试RX.CurenttPos = "";
            步进电机调试RX.CurenttVel = "";
            步进电机调试RX.CurrentPos = "RXRealDistance";
            步进电机调试RX.CurrentSpeed = "RXRealVelo";
            步进电机调试RX.Enable_Color = Color.Green;
            步进电机调试RX.EnableAddress = "";
            步进电机调试RX.EnableState = "RXIsEnable";
            步进电机调试RX.ID = 7;
            步进电机调试RX.JOGBack = "RXAxisActions";
            步进电机调试RX.JOGFront = "RXAxisActions";
            步进电机调试RX.JogSpeed = 0D;
            步进电机调试RX.JogSpeedMax = 30D;
            步进电机调试RX.JogSpeedMin = 0D;
            步进电机调试RX.JogVel = "0";
            步进电机调试RX.JogVelAddress = "RXJogVelo";
            步进电机调试RX.Location = new Point(78, 96);
            步进电机调试RX.MoveAbsAddress = "RXAxisActions";
            步进电机调试RX.MoveAbsState = "RXAbsDone";
            步进电机调试RX.Name = "步进电机调试RX";
            步进电机调试RX.Pos = 0D;
            步进电机调试RX.PosComplete_Bool = false;
            步进电机调试RX.PosComplete_Color = Color.Green;
            步进电机调试RX.PosMax = 0D;
            步进电机调试RX.PosMin = 0D;
            步进电机调试RX.Ready_Color = Color.Green;
            步进电机调试RX.ReadyState = "RXIsReady";
            步进电机调试RX.Size = new Size(714, 575);
            步进电机调试RX.Speed = 0D;
            步进电机调试RX.SpeedMax = 0D;
            步进电机调试RX.SpeedMin = 0D;
            步进电机调试RX.StopAddress = "RXAxisActions";
            步进电机调试RX.TabIndex = 0;
            步进电机调试RX.TargetPos = "RXTarget";
            步进电机调试RX.V_Test = "RX（伺服）参数";
            步进电机调试RX.VeloAddress = "RXVelo";
            步进电机调试RX.Zero_Color = Color.Green;
            步进电机调试RX.ZeroAddress = "RXAxisActions";
            步进电机调试RX.ZeroState = "RXHomeSetDone";
            // 
            // tPRY
            // 
            tPRY.BackColor = Color.FromArgb(248, 248, 248);
            tPRY.Controls.Add(步进电机调试RY);
            tPRY.Location = new Point(51, 0);
            tPRY.Name = "tPRY";
            tPRY.Size = new Size(871, 767);
            tPRY.TabIndex = 8;
            tPRY.Text = "RY";
            // 
            // 步进电机调试RY
            // 
            步进电机调试RY.Alarm_Color = Color.Green;
            步进电机调试RY.AlarmState = "RYAxisError";
            步进电机调试RY.BtnZeroEnable = false;
            步进电机调试RY.CurenttPos = "";
            步进电机调试RY.CurenttVel = "";
            步进电机调试RY.CurrentPos = "RYRealDistance";
            步进电机调试RY.CurrentSpeed = "RYRealVelo";
            步进电机调试RY.Enable_Color = Color.Green;
            步进电机调试RY.EnableAddress = "";
            步进电机调试RY.EnableState = "RYIsEnable";
            步进电机调试RY.ID = 8;
            步进电机调试RY.JOGBack = "RYAxisActions";
            步进电机调试RY.JOGFront = "RYAxisActions";
            步进电机调试RY.JogSpeed = 0D;
            步进电机调试RY.JogSpeedMax = 30D;
            步进电机调试RY.JogSpeedMin = 0D;
            步进电机调试RY.JogVel = "0";
            步进电机调试RY.JogVelAddress = "RYJogVelo";
            步进电机调试RY.Location = new Point(78, 96);
            步进电机调试RY.MoveAbsAddress = "RYAxisActions";
            步进电机调试RY.MoveAbsState = "RYAbsDone";
            步进电机调试RY.Name = "步进电机调试RY";
            步进电机调试RY.Pos = 0D;
            步进电机调试RY.PosComplete_Bool = false;
            步进电机调试RY.PosComplete_Color = Color.Green;
            步进电机调试RY.PosMax = 0D;
            步进电机调试RY.PosMin = 0D;
            步进电机调试RY.Ready_Color = Color.Green;
            步进电机调试RY.ReadyState = "RYIsReady";
            步进电机调试RY.Size = new Size(714, 575);
            步进电机调试RY.Speed = 0D;
            步进电机调试RY.SpeedMax = 0D;
            步进电机调试RY.SpeedMin = 0D;
            步进电机调试RY.StopAddress = "RYAxisActions";
            步进电机调试RY.TabIndex = 0;
            步进电机调试RY.TargetPos = "RYTarget";
            步进电机调试RY.V_Test = "RY（步进）参数";
            步进电机调试RY.VeloAddress = "RYVelo";
            步进电机调试RY.Zero_Color = Color.Green;
            步进电机调试RY.ZeroAddress = "RYAxisActions";
            步进电机调试RY.ZeroState = "RYHomeSetDone";
            // 
            // tPRZ
            // 
            tPRZ.BackColor = Color.FromArgb(248, 248, 248);
            tPRZ.Controls.Add(步进电机调试RZ);
            tPRZ.Location = new Point(51, 0);
            tPRZ.Name = "tPRZ";
            tPRZ.Size = new Size(871, 767);
            tPRZ.TabIndex = 9;
            tPRZ.Text = "RZ";
            // 
            // 步进电机调试RZ
            // 
            步进电机调试RZ.Alarm_Color = Color.Green;
            步进电机调试RZ.AlarmState = "RZAxisError";
            步进电机调试RZ.BtnZeroEnable = true;
            步进电机调试RZ.CurenttPos = "";
            步进电机调试RZ.CurenttVel = "";
            步进电机调试RZ.CurrentPos = "RZRealDistance";
            步进电机调试RZ.CurrentSpeed = "RZRealVelo";
            步进电机调试RZ.Enable_Color = Color.Green;
            步进电机调试RZ.EnableAddress = "";
            步进电机调试RZ.EnableState = "RZIsEnable";
            步进电机调试RZ.ID = 9;
            步进电机调试RZ.JOGBack = "RZAxisActions";
            步进电机调试RZ.JOGFront = "RZAxisActions";
            步进电机调试RZ.JogSpeed = 0D;
            步进电机调试RZ.JogSpeedMax = 30D;
            步进电机调试RZ.JogSpeedMin = 0D;
            步进电机调试RZ.JogVel = "0";
            步进电机调试RZ.JogVelAddress = "RZJogVelo";
            步进电机调试RZ.Location = new Point(78, 96);
            步进电机调试RZ.MoveAbsAddress = "RZAxisActions";
            步进电机调试RZ.MoveAbsState = "RZAbsDone";
            步进电机调试RZ.Name = "步进电机调试RZ";
            步进电机调试RZ.Pos = 0D;
            步进电机调试RZ.PosComplete_Bool = false;
            步进电机调试RZ.PosComplete_Color = Color.Green;
            步进电机调试RZ.PosMax = 0D;
            步进电机调试RZ.PosMin = 0D;
            步进电机调试RZ.Ready_Color = Color.Green;
            步进电机调试RZ.ReadyState = "RZIsReady";
            步进电机调试RZ.Size = new Size(714, 575);
            步进电机调试RZ.Speed = 0D;
            步进电机调试RZ.SpeedMax = 0D;
            步进电机调试RZ.SpeedMin = 0D;
            步进电机调试RZ.StopAddress = "RZAxisActions";
            步进电机调试RZ.TabIndex = 0;
            步进电机调试RZ.TargetPos = "RZTarget";
            步进电机调试RZ.V_Test = "RZ（步进）参数";
            步进电机调试RZ.VeloAddress = "RZVelo";
            步进电机调试RZ.Zero_Color = Color.Green;
            步进电机调试RZ.ZeroAddress = "RZAxisActions";
            步进电机调试RZ.ZeroState = "RZHomeSetDone";
            // 
            // LalChuckLockState
            // 
            LalChuckLockState.AutoSize = true;
            LalChuckLockState.Location = new Point(5, 18);
            LalChuckLockState.Name = "LalChuckLockState";
            LalChuckLockState.Size = new Size(82, 24);
            LalChuckLockState.TabIndex = 2;
            LalChuckLockState.Text = "label1";
            // 
            // groupBox1
            // 
            groupBox1.Location = new Point(0, 0);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(200, 100);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            // 
            // tableLayoutPanel5
            // 
            tableLayoutPanel5.ColumnCount = 2;
            tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tableLayoutPanel5.Location = new Point(0, 0);
            tableLayoutPanel5.Name = "tableLayoutPanel5";
            tableLayoutPanel5.RowCount = 1;
            tableLayoutPanel5.Size = new Size(200, 100);
            tableLayoutPanel5.TabIndex = 0;
            // 
            // groupBox2
            // 
            groupBox2.Location = new Point(0, 0);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new Size(200, 100);
            groupBox2.TabIndex = 0;
            groupBox2.TabStop = false;
            // 
            // tableLayoutPanel3
            // 
            tableLayoutPanel3.ColumnCount = 2;
            tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tableLayoutPanel3.Location = new Point(0, 0);
            tableLayoutPanel3.Name = "tableLayoutPanel3";
            tableLayoutPanel3.RowCount = 1;
            tableLayoutPanel3.Size = new Size(200, 100);
            tableLayoutPanel3.TabIndex = 0;
            // 
            // gbxVisual
            // 
            gbxVisual.Location = new Point(0, 0);
            gbxVisual.Name = "gbxVisual";
            gbxVisual.Size = new Size(200, 100);
            gbxVisual.TabIndex = 0;
            gbxVisual.TabStop = false;
            // 
            // tableLayoutPanel4
            // 
            tableLayoutPanel4.ColumnCount = 2;
            tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tableLayoutPanel4.Location = new Point(0, 0);
            tableLayoutPanel4.Name = "tableLayoutPanel4";
            tableLayoutPanel4.RowCount = 1;
            tableLayoutPanel4.Size = new Size(200, 100);
            tableLayoutPanel4.TabIndex = 0;
            // 
            // panel41
            // 
            panel41.Controls.Add(BtnPlat标定);
            panel41.Location = new Point(3, 3);
            panel41.Name = "panel41";
            panel41.Size = new Size(94, 39);
            panel41.TabIndex = 12;
            // 
            // BtnPlat标定
            // 
            BtnPlat标定.BackColor = Color.FromArgb(224, 224, 224);
            BtnPlat标定.Dock = DockStyle.Fill;
            BtnPlat标定.Location = new Point(0, 0);
            BtnPlat标定.Name = "BtnPlat标定";
            BtnPlat标定.Size = new Size(94, 39);
            BtnPlat标定.TabIndex = 88;
            BtnPlat标定.Text = "平台标定";
            BtnPlat标定.UseVisualStyleBackColor = false;
            // 
            // groupBox4
            // 
            groupBox4.Controls.Add(tableLayoutPanel8);
            groupBox4.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            groupBox4.Location = new Point(1091, 361);
            groupBox4.Name = "groupBox4";
            groupBox4.Size = new Size(790, 249);
            groupBox4.TabIndex = 91;
            groupBox4.TabStop = false;
            groupBox4.Text = "系统参数设置";
            // 
            // tableLayoutPanel8
            // 
            tableLayoutPanel8.ColumnCount = 4;
            tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            tableLayoutPanel8.Controls.Add(panel15, 0, 1);
            tableLayoutPanel8.Controls.Add(panel47, 2, 0);
            tableLayoutPanel8.Controls.Add(panel48, 3, 0);
            tableLayoutPanel8.Controls.Add(panel36, 1, 0);
            tableLayoutPanel8.Controls.Add(panel40, 0, 0);
            tableLayoutPanel8.Controls.Add(panel12, 3, 1);
            tableLayoutPanel8.Controls.Add(panel9, 2, 1);
            tableLayoutPanel8.Controls.Add(panel8, 1, 1);
            tableLayoutPanel8.Controls.Add(panel7, 0, 2);
            tableLayoutPanel8.Controls.Add(panel13, 1, 2);
            tableLayoutPanel8.Location = new Point(49, 43);
            tableLayoutPanel8.Name = "tableLayoutPanel8";
            tableLayoutPanel8.RowCount = 3;
            tableLayoutPanel8.RowStyles.Add(new RowStyle(SizeType.Percent, 33.3333321F));
            tableLayoutPanel8.RowStyles.Add(new RowStyle(SizeType.Percent, 33.3333321F));
            tableLayoutPanel8.RowStyles.Add(new RowStyle(SizeType.Percent, 33.3333321F));
            tableLayoutPanel8.Size = new Size(692, 172);
            tableLayoutPanel8.TabIndex = 3;
            // 
            // panel15
            // 
            panel15.Controls.Add(lalCameraOffset);
            panel15.Location = new Point(3, 60);
            panel15.Name = "panel15";
            panel15.Size = new Size(167, 51);
            panel15.TabIndex = 16;
            // 
            // lalCameraOffset
            // 
            lalCameraOffset.AutoSize = true;
            lalCameraOffset.Location = new Point(40, 17);
            lalCameraOffset.Name = "lalCameraOffset";
            lalCameraOffset.Size = new Size(130, 24);
            lalCameraOffset.TabIndex = 8;
            lalCameraOffset.Text = "相机偏移量";
            // 
            // panel47
            // 
            panel47.Controls.Add(LalGap);
            panel47.Location = new Point(349, 3);
            panel47.Name = "panel47";
            panel47.Size = new Size(167, 51);
            panel47.TabIndex = 9;
            // 
            // LalGap
            // 
            LalGap.AutoSize = true;
            LalGap.Location = new Point(24, 17);
            LalGap.Name = "LalGap";
            LalGap.Size = new Size(178, 24);
            LalGap.TabIndex = 8;
            LalGap.Text = "上晶圆吸附间隙";
            // 
            // panel48
            // 
            panel48.Controls.Add(label10);
            panel48.Controls.Add(TxtTopGap);
            panel48.Location = new Point(522, 3);
            panel48.Name = "panel48";
            panel48.Size = new Size(167, 51);
            panel48.TabIndex = 10;
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Location = new Point(120, 17);
            label10.Name = "label10";
            label10.Size = new Size(34, 24);
            label10.TabIndex = 9;
            label10.Text = "mm";
            // 
            // TxtTopGap
            // 
            TxtTopGap.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtTopGap.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtTopGap.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtTopGap.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtTopGap.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtTopGap.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtTopGap.ButtonStyleInherited = false;
            TxtTopGap.ButtonSymbolOffset = new Point(0, 0);
            TxtTopGap.DecimalPlaces = 3;
            TxtTopGap.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtTopGap.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtTopGap.Location = new Point(23, 12);
            TxtTopGap.Margin = new Padding(4, 5, 4, 5);
            TxtTopGap.MinimumSize = new Size(1, 16);
            TxtTopGap.Name = "TxtTopGap";
            TxtTopGap.Padding = new Padding(5);
            TxtTopGap.RectColor = Color.FromArgb(140, 140, 140);
            TxtTopGap.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtTopGap.ScrollBarStyleInherited = false;
            TxtTopGap.ShowText = false;
            TxtTopGap.Size = new Size(95, 26);
            TxtTopGap.Style = UIStyle.Custom;
            TxtTopGap.TabIndex = 1;
            TxtTopGap.Text = "0.000";
            TxtTopGap.TextAlignment = ContentAlignment.MiddleCenter;
            TxtTopGap.Type = UITextBox.UIEditType.Double;
            TxtTopGap.Watermark = "";
            TxtTopGap.KeyUp += Txt_KeyUp;
            // 
            // panel36
            // 
            panel36.Controls.Add(label3);
            panel36.Controls.Add(TxtZOffset);
            panel36.Dock = DockStyle.Fill;
            panel36.Location = new Point(176, 3);
            panel36.Name = "panel36";
            panel36.Size = new Size(167, 51);
            panel36.TabIndex = 4;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(120, 17);
            label3.Name = "label3";
            label3.Size = new Size(34, 24);
            label3.TabIndex = 8;
            label3.Text = "mm";
            // 
            // TxtZOffset
            // 
            TxtZOffset.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtZOffset.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtZOffset.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtZOffset.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtZOffset.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtZOffset.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtZOffset.ButtonStyleInherited = false;
            TxtZOffset.ButtonSymbolOffset = new Point(0, 0);
            TxtZOffset.DecimalPlaces = 3;
            TxtZOffset.DoubleValue = 20D;
            TxtZOffset.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtZOffset.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtZOffset.Location = new Point(23, 12);
            TxtZOffset.Margin = new Padding(4, 5, 4, 5);
            TxtZOffset.MinimumSize = new Size(1, 16);
            TxtZOffset.Name = "TxtZOffset";
            TxtZOffset.Padding = new Padding(5);
            TxtZOffset.RectColor = Color.FromArgb(140, 140, 140);
            TxtZOffset.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtZOffset.ScrollBarStyleInherited = false;
            TxtZOffset.ShowText = false;
            TxtZOffset.Size = new Size(95, 26);
            TxtZOffset.Style = UIStyle.Custom;
            TxtZOffset.TabIndex = 2;
            TxtZOffset.Text = "20.000";
            TxtZOffset.TextAlignment = ContentAlignment.MiddleCenter;
            TxtZOffset.Type = UITextBox.UIEditType.Double;
            TxtZOffset.Watermark = "";
            TxtZOffset.KeyUp += Txt_KeyUp;
            // 
            // panel40
            // 
            panel40.Controls.Add(LalOffset);
            panel40.Dock = DockStyle.Fill;
            panel40.Location = new Point(3, 3);
            panel40.Name = "panel40";
            panel40.Size = new Size(167, 51);
            panel40.TabIndex = 0;
            // 
            // LalOffset
            // 
            LalOffset.AutoSize = true;
            LalOffset.Location = new Point(36, 17);
            LalOffset.Name = "LalOffset";
            LalOffset.Size = new Size(142, 24);
            LalOffset.TabIndex = 9;
            LalOffset.Text = "Z原点偏移位";
            // 
            // panel12
            // 
            panel12.Controls.Add(label11);
            panel12.Controls.Add(TxtBottomGap);
            panel12.Location = new Point(522, 60);
            panel12.Name = "panel12";
            panel12.Size = new Size(167, 51);
            panel12.TabIndex = 14;
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.Location = new Point(120, 17);
            label11.Name = "label11";
            label11.Size = new Size(34, 24);
            label11.TabIndex = 9;
            label11.Text = "mm";
            // 
            // TxtBottomGap
            // 
            TxtBottomGap.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtBottomGap.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtBottomGap.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtBottomGap.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtBottomGap.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtBottomGap.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtBottomGap.ButtonStyleInherited = false;
            TxtBottomGap.ButtonSymbolOffset = new Point(0, 0);
            TxtBottomGap.DecimalPlaces = 3;
            TxtBottomGap.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtBottomGap.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtBottomGap.Location = new Point(23, 12);
            TxtBottomGap.Margin = new Padding(4, 5, 4, 5);
            TxtBottomGap.MinimumSize = new Size(1, 16);
            TxtBottomGap.Name = "TxtBottomGap";
            TxtBottomGap.Padding = new Padding(5);
            TxtBottomGap.RectColor = Color.FromArgb(140, 140, 140);
            TxtBottomGap.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtBottomGap.ScrollBarStyleInherited = false;
            TxtBottomGap.ShowText = false;
            TxtBottomGap.Size = new Size(95, 26);
            TxtBottomGap.Style = UIStyle.Custom;
            TxtBottomGap.TabIndex = 1;
            TxtBottomGap.Text = "0.000";
            TxtBottomGap.TextAlignment = ContentAlignment.MiddleCenter;
            TxtBottomGap.Type = UITextBox.UIEditType.Double;
            TxtBottomGap.Watermark = "";
            TxtBottomGap.KeyUp += Txt_KeyUp;
            // 
            // panel9
            // 
            panel9.Controls.Add(label9);
            panel9.Location = new Point(349, 60);
            panel9.Name = "panel9";
            panel9.Size = new Size(167, 51);
            panel9.TabIndex = 13;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Location = new Point(24, 17);
            label9.Name = "label9";
            label9.Size = new Size(178, 24);
            label9.TabIndex = 8;
            label9.Text = "下晶圆吸附间隙";
            // 
            // panel8
            // 
            panel8.Controls.Add(label20);
            panel8.Controls.Add(TxtCameraOffset);
            panel8.Location = new Point(176, 60);
            panel8.Name = "panel8";
            panel8.Size = new Size(161, 51);
            panel8.TabIndex = 12;
            // 
            // label20
            // 
            label20.AutoSize = true;
            label20.Location = new Point(117, 17);
            label20.Name = "label20";
            label20.Size = new Size(34, 24);
            label20.TabIndex = 9;
            label20.Text = "mm";
            // 
            // TxtCameraOffset
            // 
            TxtCameraOffset.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtCameraOffset.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtCameraOffset.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtCameraOffset.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtCameraOffset.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtCameraOffset.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtCameraOffset.ButtonStyleInherited = false;
            TxtCameraOffset.ButtonSymbolOffset = new Point(0, 0);
            TxtCameraOffset.DecimalPlaces = 3;
            TxtCameraOffset.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtCameraOffset.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtCameraOffset.Location = new Point(20, 12);
            TxtCameraOffset.Margin = new Padding(4, 5, 4, 5);
            TxtCameraOffset.MinimumSize = new Size(1, 16);
            TxtCameraOffset.Name = "TxtCameraOffset";
            TxtCameraOffset.Padding = new Padding(5);
            TxtCameraOffset.RectColor = Color.FromArgb(140, 140, 140);
            TxtCameraOffset.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtCameraOffset.ScrollBarStyleInherited = false;
            TxtCameraOffset.ShowText = false;
            TxtCameraOffset.Size = new Size(95, 26);
            TxtCameraOffset.Style = UIStyle.Custom;
            TxtCameraOffset.TabIndex = 1;
            TxtCameraOffset.Text = "0.000";
            TxtCameraOffset.TextAlignment = ContentAlignment.MiddleCenter;
            TxtCameraOffset.Type = UITextBox.UIEditType.Double;
            TxtCameraOffset.Watermark = "";
            TxtCameraOffset.KeyUp += Txt_KeyUp;
            // 
            // panel7
            // 
            panel7.Controls.Add(lalBottomPhotoOffset);
            panel7.Location = new Point(3, 117);
            panel7.Name = "panel7";
            panel7.Size = new Size(167, 51);
            panel7.TabIndex = 11;
            // 
            // lalBottomPhotoOffset
            // 
            lalBottomPhotoOffset.AutoSize = true;
            lalBottomPhotoOffset.Location = new Point(7, 17);
            lalBottomPhotoOffset.Name = "lalBottomPhotoOffset";
            lalBottomPhotoOffset.Size = new Size(226, 24);
            lalBottomPhotoOffset.TabIndex = 8;
            lalBottomPhotoOffset.Text = "下透明片拍照偏移量";
            // 
            // panel13
            // 
            panel13.Controls.Add(label19);
            panel13.Controls.Add(TxtBottomPhotoOffset);
            panel13.Location = new Point(176, 117);
            panel13.Name = "panel13";
            panel13.Size = new Size(167, 51);
            panel13.TabIndex = 15;
            // 
            // label19
            // 
            label19.AutoSize = true;
            label19.Location = new Point(120, 17);
            label19.Name = "label19";
            label19.Size = new Size(34, 24);
            label19.TabIndex = 9;
            label19.Text = "mm";
            // 
            // TxtBottomPhotoOffset
            // 
            TxtBottomPhotoOffset.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtBottomPhotoOffset.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtBottomPhotoOffset.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtBottomPhotoOffset.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtBottomPhotoOffset.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtBottomPhotoOffset.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtBottomPhotoOffset.ButtonStyleInherited = false;
            TxtBottomPhotoOffset.ButtonSymbolOffset = new Point(0, 0);
            TxtBottomPhotoOffset.DecimalPlaces = 3;
            TxtBottomPhotoOffset.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtBottomPhotoOffset.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtBottomPhotoOffset.Location = new Point(23, 12);
            TxtBottomPhotoOffset.Margin = new Padding(4, 5, 4, 5);
            TxtBottomPhotoOffset.MinimumSize = new Size(1, 16);
            TxtBottomPhotoOffset.Name = "TxtBottomPhotoOffset";
            TxtBottomPhotoOffset.Padding = new Padding(5);
            TxtBottomPhotoOffset.RectColor = Color.FromArgb(140, 140, 140);
            TxtBottomPhotoOffset.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtBottomPhotoOffset.ScrollBarStyleInherited = false;
            TxtBottomPhotoOffset.ShowText = false;
            TxtBottomPhotoOffset.Size = new Size(95, 26);
            TxtBottomPhotoOffset.Style = UIStyle.Custom;
            TxtBottomPhotoOffset.TabIndex = 1;
            TxtBottomPhotoOffset.Text = "0.000";
            TxtBottomPhotoOffset.TextAlignment = ContentAlignment.MiddleCenter;
            TxtBottomPhotoOffset.Type = UITextBox.UIEditType.Double;
            TxtBottomPhotoOffset.Watermark = "";
            TxtBottomPhotoOffset.KeyUp += Txt_KeyUp;
            // 
            // LalTrayState
            // 
            LalTrayState.AutoSize = true;
            LalTrayState.Location = new Point(5, 50);
            LalTrayState.Name = "LalTrayState";
            LalTrayState.Size = new Size(82, 24);
            LalTrayState.TabIndex = 0;
            LalTrayState.Text = "label1";
            // 
            // LalTopWaferState
            // 
            LalTopWaferState.AutoSize = true;
            LalTopWaferState.Location = new Point(5, 50);
            LalTopWaferState.Name = "LalTopWaferState";
            LalTopWaferState.Size = new Size(82, 24);
            LalTopWaferState.TabIndex = 0;
            LalTopWaferState.Text = "label1";
            // 
            // LalTrayWaferInnerState
            // 
            LalTrayWaferInnerState.AutoSize = true;
            LalTrayWaferInnerState.Location = new Point(5, 17);
            LalTrayWaferInnerState.Name = "LalTrayWaferInnerState";
            LalTrayWaferInnerState.Size = new Size(82, 24);
            LalTrayWaferInnerState.TabIndex = 0;
            LalTrayWaferInnerState.Text = "label1";
            // 
            // panel18
            // 
            panel18.Controls.Add(LightTrayWaferInner);
            panel18.Dock = DockStyle.Fill;
            panel18.Location = new Point(150, 184);
            panel18.Name = "panel18";
            panel18.Size = new Size(49, 60);
            panel18.TabIndex = 1;
            // 
            // LightTrayWaferInner
            // 
            LightTrayWaferInner.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LightTrayWaferInner.Location = new Point(9, 11);
            LightTrayWaferInner.MinimumSize = new Size(1, 1);
            LightTrayWaferInner.Name = "LightTrayWaferInner";
            LightTrayWaferInner.Radius = 30;
            LightTrayWaferInner.Size = new Size(30, 30);
            LightTrayWaferInner.Style = UIStyle.Custom;
            LightTrayWaferInner.TabIndex = 97;
            LightTrayWaferInner.Text = "uiLight3";
            // 
            // panel19
            // 
            panel19.Controls.Add(LightTray);
            panel19.Dock = DockStyle.Fill;
            panel19.Location = new Point(524, 3);
            panel19.Name = "panel19";
            panel19.Size = new Size(49, 114);
            panel19.TabIndex = 2;
            // 
            // LightTray
            // 
            LightTray.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LightTray.Location = new Point(9, 43);
            LightTray.MinimumSize = new Size(1, 1);
            LightTray.Name = "LightTray";
            LightTray.Radius = 30;
            LightTray.Size = new Size(30, 30);
            LightTray.Style = UIStyle.Custom;
            LightTray.TabIndex = 98;
            LightTray.Text = "uiLight3";
            // 
            // panel17
            // 
            panel17.Controls.Add(LightTopWafer);
            panel17.Dock = DockStyle.Fill;
            panel17.Location = new Point(150, 3);
            panel17.Name = "panel17";
            panel17.Size = new Size(49, 114);
            panel17.TabIndex = 0;
            // 
            // LightTopWafer
            // 
            LightTopWafer.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LightTopWafer.Location = new Point(9, 44);
            LightTopWafer.MinimumSize = new Size(1, 1);
            LightTopWafer.Name = "LightTopWafer";
            LightTopWafer.Radius = 30;
            LightTopWafer.Size = new Size(30, 30);
            LightTopWafer.Style = UIStyle.Custom;
            LightTopWafer.TabIndex = 95;
            LightTopWafer.Text = "uiLight3";
            // 
            // groupBox3
            // 
            groupBox3.Controls.Add(tableLayoutPanel6);
            groupBox3.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            groupBox3.Location = new Point(1091, 54);
            groupBox3.Name = "groupBox3";
            groupBox3.Size = new Size(790, 289);
            groupBox3.TabIndex = 92;
            groupBox3.TabStop = false;
            groupBox3.Text = "输入/出";
            // 
            // tableLayoutPanel6
            // 
            tableLayoutPanel6.ColumnCount = 6;
            tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 19.6434288F));
            tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 7.370512F));
            tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 22.98508F));
            tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 19.6434288F));
            tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 7.372465F));
            tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 22.98508F));
            tableLayoutPanel6.Controls.Add(panel31, 2, 1);
            tableLayoutPanel6.Controls.Add(panel30, 1, 1);
            tableLayoutPanel6.Controls.Add(panel18, 1, 2);
            tableLayoutPanel6.Controls.Add(panel17, 1, 0);
            tableLayoutPanel6.Controls.Add(panel26, 0, 0);
            tableLayoutPanel6.Controls.Add(panel27, 2, 0);
            tableLayoutPanel6.Controls.Add(panel29, 0, 1);
            tableLayoutPanel6.Controls.Add(panel28, 0, 2);
            tableLayoutPanel6.Controls.Add(panel32, 2, 2);
            tableLayoutPanel6.Controls.Add(panel24, 3, 0);
            tableLayoutPanel6.Controls.Add(panel33, 3, 1);
            tableLayoutPanel6.Controls.Add(panel37, 3, 2);
            tableLayoutPanel6.Controls.Add(panel19, 4, 0);
            tableLayoutPanel6.Controls.Add(panel1, 4, 1);
            tableLayoutPanel6.Controls.Add(panel39, 4, 2);
            tableLayoutPanel6.Controls.Add(panel25, 5, 0);
            tableLayoutPanel6.Controls.Add(panel34, 5, 1);
            tableLayoutPanel6.Controls.Add(panel42, 5, 2);
            tableLayoutPanel6.Location = new Point(20, 25);
            tableLayoutPanel6.Name = "tableLayoutPanel6";
            tableLayoutPanel6.RowCount = 3;
            tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Percent, 48.5829964F));
            tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Percent, 24.6963558F));
            tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Percent, 26.31579F));
            tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            tableLayoutPanel6.Size = new Size(750, 247);
            tableLayoutPanel6.TabIndex = 0;
            // 
            // panel31
            // 
            panel31.Controls.Add(LalTrayWaferOuterState);
            panel31.Dock = DockStyle.Fill;
            panel31.Location = new Point(205, 123);
            panel31.Name = "panel31";
            panel31.Size = new Size(166, 55);
            panel31.TabIndex = 4;
            // 
            // LalTrayWaferOuterState
            // 
            LalTrayWaferOuterState.AutoSize = true;
            LalTrayWaferOuterState.Location = new Point(5, 17);
            LalTrayWaferOuterState.Name = "LalTrayWaferOuterState";
            LalTrayWaferOuterState.Size = new Size(82, 24);
            LalTrayWaferOuterState.TabIndex = 0;
            LalTrayWaferOuterState.Text = "label1";
            // 
            // panel30
            // 
            panel30.Controls.Add(LightTrayWaferOuter);
            panel30.Dock = DockStyle.Fill;
            panel30.Location = new Point(150, 123);
            panel30.Name = "panel30";
            panel30.Size = new Size(49, 55);
            panel30.TabIndex = 14;
            // 
            // LightTrayWaferOuter
            // 
            LightTrayWaferOuter.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LightTrayWaferOuter.Location = new Point(9, 11);
            LightTrayWaferOuter.MinimumSize = new Size(1, 1);
            LightTrayWaferOuter.Name = "LightTrayWaferOuter";
            LightTrayWaferOuter.Radius = 30;
            LightTrayWaferOuter.Size = new Size(30, 30);
            LightTrayWaferOuter.Style = UIStyle.Custom;
            LightTrayWaferOuter.TabIndex = 96;
            LightTrayWaferOuter.Text = "uiLight3";
            // 
            // panel26
            // 
            panel26.Controls.Add(BtnTopWafer_Release);
            panel26.Controls.Add(BtnTopWafer);
            panel26.Dock = DockStyle.Fill;
            panel26.Location = new Point(3, 3);
            panel26.Name = "panel26";
            panel26.Size = new Size(141, 114);
            panel26.TabIndex = 6;
            // 
            // BtnTopWafer_Release
            // 
            BtnTopWafer_Release.BackColor = Color.FromArgb(248, 248, 248);
            BtnTopWafer_Release.Location = new Point(5, 63);
            BtnTopWafer_Release.Name = "BtnTopWafer_Release";
            BtnTopWafer_Release.Size = new Size(131, 39);
            BtnTopWafer_Release.TabIndex = 91;
            BtnTopWafer_Release.Text = "上晶圆释放";
            BtnTopWafer_Release.UseVisualStyleBackColor = true;
            BtnTopWafer_Release.Click += BtnTopWafer_Release_Click;
            // 
            // BtnTopWafer
            // 
            BtnTopWafer.BackColor = Color.FromArgb(248, 248, 248);
            BtnTopWafer.Location = new Point(5, 14);
            BtnTopWafer.Name = "BtnTopWafer";
            BtnTopWafer.Size = new Size(131, 39);
            BtnTopWafer.TabIndex = 90;
            BtnTopWafer.Text = "上晶圆吸附";
            BtnTopWafer.UseVisualStyleBackColor = true;
            BtnTopWafer.Click += BtnTopWafer_Click;
            // 
            // panel27
            // 
            panel27.Controls.Add(LalTopWaferState);
            panel27.Dock = DockStyle.Fill;
            panel27.Location = new Point(205, 3);
            panel27.Name = "panel27";
            panel27.Size = new Size(166, 114);
            panel27.TabIndex = 5;
            // 
            // panel29
            // 
            panel29.Controls.Add(BtnTrayWaferOuter);
            panel29.Dock = DockStyle.Fill;
            panel29.Location = new Point(3, 123);
            panel29.Name = "panel29";
            panel29.Size = new Size(141, 55);
            panel29.TabIndex = 13;
            // 
            // BtnTrayWaferOuter
            // 
            BtnTrayWaferOuter.BackColor = Color.FromArgb(224, 224, 224);
            BtnTrayWaferOuter.Location = new Point(5, 5);
            BtnTrayWaferOuter.Name = "BtnTrayWaferOuter";
            BtnTrayWaferOuter.Size = new Size(131, 39);
            BtnTrayWaferOuter.TabIndex = 90;
            BtnTrayWaferOuter.Text = "托盘晶圆外吸附";
            BtnTrayWaferOuter.UseVisualStyleBackColor = true;
            BtnTrayWaferOuter.Click += BtnTrayWaferOuter_Click;
            // 
            // panel28
            // 
            panel28.Controls.Add(BtnTrayWaferInner);
            panel28.Dock = DockStyle.Fill;
            panel28.Location = new Point(3, 184);
            panel28.Name = "panel28";
            panel28.Size = new Size(141, 60);
            panel28.TabIndex = 4;
            // 
            // BtnTrayWaferInner
            // 
            BtnTrayWaferInner.Location = new Point(5, 6);
            BtnTrayWaferInner.Name = "BtnTrayWaferInner";
            BtnTrayWaferInner.Size = new Size(131, 39);
            BtnTrayWaferInner.TabIndex = 0;
            BtnTrayWaferInner.Text = "托盘晶圆内吸附";
            BtnTrayWaferInner.UseVisualStyleBackColor = true;
            BtnTrayWaferInner.Click += BtnTrayWaferInner_Click;
            // 
            // panel32
            // 
            panel32.Controls.Add(LalTrayWaferInnerState);
            panel32.Dock = DockStyle.Fill;
            panel32.Location = new Point(205, 184);
            panel32.Name = "panel32";
            panel32.Size = new Size(166, 60);
            panel32.TabIndex = 3;
            // 
            // panel24
            // 
            panel24.Controls.Add(BtnTray);
            panel24.Dock = DockStyle.Fill;
            panel24.Location = new Point(377, 3);
            panel24.Name = "panel24";
            panel24.Size = new Size(141, 114);
            panel24.TabIndex = 8;
            // 
            // BtnTray
            // 
            BtnTray.BackColor = Color.FromArgb(224, 224, 224);
            BtnTray.Location = new Point(5, 39);
            BtnTray.Name = "BtnTray";
            BtnTray.Size = new Size(131, 39);
            BtnTray.TabIndex = 90;
            BtnTray.Text = "托盘吸附";
            BtnTray.UseVisualStyleBackColor = true;
            BtnTray.Click += BtnTray_Click;
            // 
            // panel33
            // 
            panel33.Controls.Add(BtnChuckLock);
            panel33.Dock = DockStyle.Fill;
            panel33.Location = new Point(377, 123);
            panel33.Name = "panel33";
            panel33.Size = new Size(141, 55);
            panel33.TabIndex = 10;
            // 
            // BtnChuckLock
            // 
            BtnChuckLock.BackColor = Color.FromArgb(224, 224, 224);
            BtnChuckLock.Location = new Point(5, 7);
            BtnChuckLock.Name = "BtnChuckLock";
            BtnChuckLock.Size = new Size(131, 39);
            BtnChuckLock.TabIndex = 87;
            BtnChuckLock.Text = "卡盘锁紧";
            BtnChuckLock.UseVisualStyleBackColor = true;
            BtnChuckLock.Click += BtnChuckLock_Click;
            // 
            // panel37
            // 
            panel37.Controls.Add(BtnHorizontalAdjustLock);
            panel37.Dock = DockStyle.Fill;
            panel37.Location = new Point(377, 184);
            panel37.Name = "panel37";
            panel37.Size = new Size(141, 60);
            panel37.TabIndex = 15;
            // 
            // BtnHorizontalAdjustLock
            // 
            BtnHorizontalAdjustLock.BackColor = Color.FromArgb(224, 224, 224);
            BtnHorizontalAdjustLock.Location = new Point(5, 7);
            BtnHorizontalAdjustLock.Name = "BtnHorizontalAdjustLock";
            BtnHorizontalAdjustLock.Size = new Size(131, 39);
            BtnHorizontalAdjustLock.TabIndex = 87;
            BtnHorizontalAdjustLock.Text = "调平锁紧";
            BtnHorizontalAdjustLock.UseVisualStyleBackColor = true;
            BtnHorizontalAdjustLock.Click += BtnHorizontalAdjustLock_Click;
            // 
            // panel1
            // 
            panel1.Controls.Add(LightChuckLock);
            panel1.Dock = DockStyle.Fill;
            panel1.Location = new Point(524, 123);
            panel1.Name = "panel1";
            panel1.Size = new Size(49, 55);
            panel1.TabIndex = 12;
            // 
            // LightChuckLock
            // 
            LightChuckLock.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LightChuckLock.Location = new Point(9, 10);
            LightChuckLock.MinimumSize = new Size(1, 1);
            LightChuckLock.Name = "LightChuckLock";
            LightChuckLock.Radius = 30;
            LightChuckLock.Size = new Size(30, 30);
            LightChuckLock.Style = UIStyle.Custom;
            LightChuckLock.TabIndex = 99;
            LightChuckLock.Text = "uiLight3";
            // 
            // panel39
            // 
            panel39.Controls.Add(LightHorizontalAdjustLock);
            panel39.Dock = DockStyle.Fill;
            panel39.Location = new Point(524, 184);
            panel39.Name = "panel39";
            panel39.Size = new Size(49, 60);
            panel39.TabIndex = 16;
            // 
            // LightHorizontalAdjustLock
            // 
            LightHorizontalAdjustLock.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LightHorizontalAdjustLock.Location = new Point(9, 11);
            LightHorizontalAdjustLock.MinimumSize = new Size(1, 1);
            LightHorizontalAdjustLock.Name = "LightHorizontalAdjustLock";
            LightHorizontalAdjustLock.Radius = 30;
            LightHorizontalAdjustLock.Size = new Size(30, 30);
            LightHorizontalAdjustLock.Style = UIStyle.Custom;
            LightHorizontalAdjustLock.TabIndex = 99;
            LightHorizontalAdjustLock.Text = "uiLight3";
            // 
            // panel25
            // 
            panel25.Controls.Add(LalTrayState);
            panel25.Dock = DockStyle.Fill;
            panel25.Location = new Point(579, 3);
            panel25.Name = "panel25";
            panel25.Size = new Size(168, 114);
            panel25.TabIndex = 7;
            // 
            // panel34
            // 
            panel34.Controls.Add(LalChuckLockState);
            panel34.Dock = DockStyle.Fill;
            panel34.Location = new Point(579, 123);
            panel34.Name = "panel34";
            panel34.Size = new Size(168, 55);
            panel34.TabIndex = 11;
            // 
            // panel42
            // 
            panel42.Controls.Add(LalHorizontalAdjustLockState);
            panel42.Dock = DockStyle.Fill;
            panel42.Location = new Point(579, 184);
            panel42.Name = "panel42";
            panel42.Size = new Size(168, 60);
            panel42.TabIndex = 17;
            // 
            // LalHorizontalAdjustLockState
            // 
            LalHorizontalAdjustLockState.AutoSize = true;
            LalHorizontalAdjustLockState.Location = new Point(5, 18);
            LalHorizontalAdjustLockState.Name = "LalHorizontalAdjustLockState";
            LalHorizontalAdjustLockState.Size = new Size(82, 24);
            LalHorizontalAdjustLockState.TabIndex = 2;
            LalHorizontalAdjustLockState.Text = "label1";
            // 
            // groupBox5
            // 
            groupBox5.Controls.Add(tableLayoutPanel7);
            groupBox5.Controls.Add(tableLayoutPanel1);
            groupBox5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            groupBox5.Location = new Point(1091, 624);
            groupBox5.Name = "groupBox5";
            groupBox5.Size = new Size(790, 250);
            groupBox5.TabIndex = 94;
            groupBox5.TabStop = false;
            groupBox5.Text = "相机标定设置";
            // 
            // tableLayoutPanel7
            // 
            tableLayoutPanel7.ColumnCount = 1;
            tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 92.76316F));
            tableLayoutPanel7.Controls.Add(panel14, 0, 0);
            tableLayoutPanel7.Controls.Add(panel35, 0, 1);
            tableLayoutPanel7.Controls.Add(panel10, 0, 2);
            tableLayoutPanel7.Location = new Point(592, 51);
            tableLayoutPanel7.Name = "tableLayoutPanel7";
            tableLayoutPanel7.RowCount = 3;
            tableLayoutPanel7.RowStyles.Add(new RowStyle(SizeType.Percent, 33.3333321F));
            tableLayoutPanel7.RowStyles.Add(new RowStyle(SizeType.Percent, 33.3333321F));
            tableLayoutPanel7.RowStyles.Add(new RowStyle(SizeType.Percent, 33.3333321F));
            tableLayoutPanel7.Size = new Size(148, 172);
            tableLayoutPanel7.TabIndex = 4;
            // 
            // panel14
            // 
            panel14.Controls.Add(BtnCalPos);
            panel14.Dock = DockStyle.Fill;
            panel14.Location = new Point(3, 3);
            panel14.Name = "panel14";
            panel14.Size = new Size(142, 51);
            panel14.TabIndex = 12;
            // 
            // BtnCalPos
            // 
            BtnCalPos.Location = new Point(6, 6);
            BtnCalPos.Name = "BtnCalPos";
            BtnCalPos.Size = new Size(131, 39);
            BtnCalPos.TabIndex = 0;
            BtnCalPos.Text = "标定位置";
            BtnCalPos.UseVisualStyleBackColor = true;
            BtnCalPos.Click += BtnCalPos_Click;
            // 
            // panel35
            // 
            panel35.Controls.Add(BtnFiveStop);
            panel35.Dock = DockStyle.Fill;
            panel35.Location = new Point(3, 60);
            panel35.Name = "panel35";
            panel35.Size = new Size(142, 51);
            panel35.TabIndex = 15;
            // 
            // BtnFiveStop
            // 
            BtnFiveStop.BackColor = Color.FromArgb(224, 224, 224);
            BtnFiveStop.Location = new Point(6, 6);
            BtnFiveStop.Name = "BtnFiveStop";
            BtnFiveStop.Size = new Size(131, 39);
            BtnFiveStop.TabIndex = 0;
            BtnFiveStop.Text = "停止";
            BtnFiveStop.UseVisualStyleBackColor = false;
            BtnFiveStop.Click += BtnFiveStop_Click;
            // 
            // panel10
            // 
            panel10.Controls.Add(BtnCal);
            panel10.Dock = DockStyle.Fill;
            panel10.Location = new Point(3, 117);
            panel10.Name = "panel10";
            panel10.Size = new Size(142, 52);
            panel10.TabIndex = 14;
            // 
            // BtnCal
            // 
            BtnCal.Location = new Point(6, 7);
            BtnCal.Name = "BtnCal";
            BtnCal.Size = new Size(131, 39);
            BtnCal.TabIndex = 1;
            BtnCal.Text = "平台标定";
            BtnCal.UseVisualStyleBackColor = true;
            BtnCal.Click += BtnCal_Click;
            // 
            // tableLayoutPanel1
            // 
            tableLayoutPanel1.ColumnCount = 6;
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.666666F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.666666F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.666666F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.666666F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.666666F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.666666F));
            tableLayoutPanel1.Controls.Add(panel6, 0, 1);
            tableLayoutPanel1.Controls.Add(panel23, 3, 1);
            tableLayoutPanel1.Controls.Add(panel2, 0, 0);
            tableLayoutPanel1.Controls.Add(panel4, 4, 0);
            tableLayoutPanel1.Controls.Add(panel21, 2, 1);
            tableLayoutPanel1.Controls.Add(panel20, 2, 0);
            tableLayoutPanel1.Controls.Add(panel3, 0, 2);
            tableLayoutPanel1.Location = new Point(50, 51);
            tableLayoutPanel1.Name = "tableLayoutPanel1";
            tableLayoutPanel1.RowCount = 3;
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 33.3333321F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 33.3333359F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 33.3333321F));
            tableLayoutPanel1.Size = new Size(524, 172);
            tableLayoutPanel1.TabIndex = 0;
            // 
            // panel6
            // 
            tableLayoutPanel1.SetColumnSpan(panel6, 2);
            panel6.Controls.Add(label12);
            panel6.Controls.Add(TxtRX);
            panel6.Controls.Add(label6);
            panel6.Dock = DockStyle.Fill;
            panel6.Location = new Point(3, 60);
            panel6.Name = "panel6";
            panel6.Size = new Size(168, 51);
            panel6.TabIndex = 4;
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Location = new Point(134, 17);
            label12.Name = "label12";
            label12.Size = new Size(34, 24);
            label12.TabIndex = 11;
            label12.Text = "mm";
            // 
            // TxtRX
            // 
            TxtRX.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtRX.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtRX.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtRX.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtRX.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtRX.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtRX.ButtonStyleInherited = false;
            TxtRX.ButtonSymbolOffset = new Point(0, 0);
            TxtRX.DecimalPlaces = 3;
            TxtRX.DoubleValue = 107.76D;
            TxtRX.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtRX.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtRX.Location = new Point(37, 12);
            TxtRX.Margin = new Padding(4, 5, 4, 5);
            TxtRX.MinimumSize = new Size(1, 16);
            TxtRX.Name = "TxtRX";
            TxtRX.Padding = new Padding(5);
            TxtRX.RectColor = Color.FromArgb(140, 140, 140);
            TxtRX.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtRX.ScrollBarStyleInherited = false;
            TxtRX.ShowText = false;
            TxtRX.Size = new Size(95, 26);
            TxtRX.Style = UIStyle.Custom;
            TxtRX.TabIndex = 1;
            TxtRX.Text = "107.760";
            TxtRX.TextAlignment = ContentAlignment.MiddleCenter;
            TxtRX.Type = UITextBox.UIEditType.Double;
            TxtRX.Watermark = "";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new Point(11, 17);
            label6.Name = "label6";
            label6.Size = new Size(34, 24);
            label6.TabIndex = 5;
            label6.Text = "RX";
            // 
            // panel23
            // 
            tableLayoutPanel1.SetColumnSpan(panel23, 2);
            panel23.Controls.Add(label18);
            panel23.Controls.Add(TxtRZ);
            panel23.Controls.Add(label7);
            panel23.Dock = DockStyle.Fill;
            panel23.Location = new Point(351, 60);
            panel23.Name = "panel23";
            panel23.Size = new Size(170, 51);
            panel23.TabIndex = 18;
            // 
            // label18
            // 
            label18.AutoSize = true;
            label18.Location = new Point(134, 17);
            label18.Name = "label18";
            label18.Size = new Size(34, 24);
            label18.TabIndex = 11;
            label18.Text = "mm";
            // 
            // TxtRZ
            // 
            TxtRZ.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtRZ.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtRZ.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtRZ.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtRZ.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtRZ.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtRZ.ButtonStyleInherited = false;
            TxtRZ.ButtonSymbolOffset = new Point(0, 0);
            TxtRZ.DecimalPlaces = 3;
            TxtRZ.DoubleValue = 6.56D;
            TxtRZ.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtRZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtRZ.Location = new Point(37, 12);
            TxtRZ.Margin = new Padding(4, 5, 4, 5);
            TxtRZ.MinimumSize = new Size(1, 16);
            TxtRZ.Name = "TxtRZ";
            TxtRZ.Padding = new Padding(5);
            TxtRZ.RectColor = Color.FromArgb(140, 140, 140);
            TxtRZ.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtRZ.ScrollBarStyleInherited = false;
            TxtRZ.ShowText = false;
            TxtRZ.Size = new Size(95, 26);
            TxtRZ.Style = UIStyle.Custom;
            TxtRZ.TabIndex = 1;
            TxtRZ.Text = "6.560";
            TxtRZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtRZ.Type = UITextBox.UIEditType.Double;
            TxtRZ.Watermark = "";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new Point(11, 17);
            label7.Name = "label7";
            label7.Size = new Size(34, 24);
            label7.TabIndex = 6;
            label7.Text = "RZ";
            // 
            // panel2
            // 
            tableLayoutPanel1.SetColumnSpan(panel2, 2);
            panel2.Controls.Add(label13);
            panel2.Controls.Add(TxtLX);
            panel2.Controls.Add(label4);
            panel2.Dock = DockStyle.Fill;
            panel2.Location = new Point(3, 3);
            panel2.Name = "panel2";
            panel2.Size = new Size(168, 51);
            panel2.TabIndex = 0;
            // 
            // label13
            // 
            label13.AutoSize = true;
            label13.Location = new Point(134, 17);
            label13.Name = "label13";
            label13.Size = new Size(34, 24);
            label13.TabIndex = 10;
            label13.Text = "mm";
            // 
            // TxtLX
            // 
            TxtLX.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtLX.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtLX.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtLX.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtLX.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtLX.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtLX.ButtonStyleInherited = false;
            TxtLX.ButtonSymbolOffset = new Point(0, 0);
            TxtLX.DecimalPlaces = 3;
            TxtLX.DoubleValue = 106.12D;
            TxtLX.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtLX.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtLX.Location = new Point(37, 12);
            TxtLX.Margin = new Padding(4, 5, 4, 5);
            TxtLX.MinimumSize = new Size(1, 16);
            TxtLX.Name = "TxtLX";
            TxtLX.Padding = new Padding(5);
            TxtLX.RectColor = Color.FromArgb(140, 140, 140);
            TxtLX.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtLX.ScrollBarStyleInherited = false;
            TxtLX.ShowText = false;
            TxtLX.Size = new Size(95, 26);
            TxtLX.Style = UIStyle.Custom;
            TxtLX.TabIndex = 0;
            TxtLX.Text = "106.120";
            TxtLX.TextAlignment = ContentAlignment.MiddleCenter;
            TxtLX.Type = UITextBox.UIEditType.Double;
            TxtLX.Watermark = "";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new Point(11, 17);
            label4.Name = "label4";
            label4.Size = new Size(34, 24);
            label4.TabIndex = 4;
            label4.Text = "LX";
            // 
            // panel4
            // 
            tableLayoutPanel1.SetColumnSpan(panel4, 2);
            panel4.Controls.Add(label17);
            panel4.Controls.Add(TxtLZ);
            panel4.Controls.Add(label5);
            panel4.Dock = DockStyle.Fill;
            panel4.Location = new Point(351, 3);
            panel4.Name = "panel4";
            panel4.Size = new Size(170, 51);
            panel4.TabIndex = 2;
            // 
            // label17
            // 
            label17.AutoSize = true;
            label17.Location = new Point(134, 17);
            label17.Name = "label17";
            label17.Size = new Size(34, 24);
            label17.TabIndex = 11;
            label17.Text = "mm";
            // 
            // TxtLZ
            // 
            TxtLZ.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtLZ.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtLZ.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtLZ.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtLZ.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtLZ.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtLZ.ButtonStyleInherited = false;
            TxtLZ.ButtonSymbolOffset = new Point(0, 0);
            TxtLZ.DecimalPlaces = 3;
            TxtLZ.DoubleValue = 5.61D;
            TxtLZ.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtLZ.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtLZ.Location = new Point(37, 12);
            TxtLZ.Margin = new Padding(4, 5, 4, 5);
            TxtLZ.MinimumSize = new Size(1, 16);
            TxtLZ.Name = "TxtLZ";
            TxtLZ.Padding = new Padding(5);
            TxtLZ.RectColor = Color.FromArgb(140, 140, 140);
            TxtLZ.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtLZ.ScrollBarStyleInherited = false;
            TxtLZ.ShowText = false;
            TxtLZ.Size = new Size(95, 26);
            TxtLZ.Style = UIStyle.Custom;
            TxtLZ.TabIndex = 1;
            TxtLZ.Text = "5.610";
            TxtLZ.TextAlignment = ContentAlignment.MiddleCenter;
            TxtLZ.Type = UITextBox.UIEditType.Double;
            TxtLZ.Watermark = "";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new Point(11, 17);
            label5.Name = "label5";
            label5.Size = new Size(34, 24);
            label5.TabIndex = 5;
            label5.Text = "LZ";
            // 
            // panel21
            // 
            tableLayoutPanel1.SetColumnSpan(panel21, 2);
            panel21.Controls.Add(label16);
            panel21.Controls.Add(TxtRY);
            panel21.Controls.Add(label2);
            panel21.Dock = DockStyle.Fill;
            panel21.Location = new Point(177, 60);
            panel21.Name = "panel21";
            panel21.Size = new Size(168, 51);
            panel21.TabIndex = 16;
            // 
            // label16
            // 
            label16.AutoSize = true;
            label16.Location = new Point(134, 17);
            label16.Name = "label16";
            label16.Size = new Size(34, 24);
            label16.TabIndex = 11;
            label16.Text = "mm";
            // 
            // TxtRY
            // 
            TxtRY.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtRY.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtRY.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtRY.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtRY.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtRY.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtRY.ButtonStyleInherited = false;
            TxtRY.ButtonSymbolOffset = new Point(0, 0);
            TxtRY.DecimalPlaces = 3;
            TxtRY.DoubleValue = 40.57D;
            TxtRY.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtRY.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtRY.Location = new Point(37, 12);
            TxtRY.Margin = new Padding(4, 5, 4, 5);
            TxtRY.MinimumSize = new Size(1, 16);
            TxtRY.Name = "TxtRY";
            TxtRY.Padding = new Padding(5);
            TxtRY.RectColor = Color.FromArgb(140, 140, 140);
            TxtRY.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtRY.ScrollBarStyleInherited = false;
            TxtRY.ShowText = false;
            TxtRY.Size = new Size(95, 26);
            TxtRY.Style = UIStyle.Custom;
            TxtRY.TabIndex = 1;
            TxtRY.Text = "40.570";
            TxtRY.TextAlignment = ContentAlignment.MiddleCenter;
            TxtRY.Type = UITextBox.UIEditType.Double;
            TxtRY.Watermark = "";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(11, 17);
            label2.Name = "label2";
            label2.Size = new Size(34, 24);
            label2.TabIndex = 5;
            label2.Text = "RY";
            // 
            // panel20
            // 
            tableLayoutPanel1.SetColumnSpan(panel20, 2);
            panel20.Controls.Add(label15);
            panel20.Controls.Add(TxtLY);
            panel20.Controls.Add(label1);
            panel20.Dock = DockStyle.Fill;
            panel20.Location = new Point(177, 3);
            panel20.Name = "panel20";
            panel20.Size = new Size(168, 51);
            panel20.TabIndex = 15;
            // 
            // label15
            // 
            label15.AutoSize = true;
            label15.Location = new Point(134, 17);
            label15.Name = "label15";
            label15.Size = new Size(34, 24);
            label15.TabIndex = 11;
            label15.Text = "mm";
            // 
            // TxtLY
            // 
            TxtLY.ButtonFillColor = Color.FromArgb(140, 140, 140);
            TxtLY.ButtonFillHoverColor = Color.FromArgb(163, 163, 163);
            TxtLY.ButtonFillPressColor = Color.FromArgb(112, 112, 112);
            TxtLY.ButtonRectColor = Color.FromArgb(140, 140, 140);
            TxtLY.ButtonRectHoverColor = Color.FromArgb(163, 163, 163);
            TxtLY.ButtonRectPressColor = Color.FromArgb(112, 112, 112);
            TxtLY.ButtonStyleInherited = false;
            TxtLY.ButtonSymbolOffset = new Point(0, 0);
            TxtLY.DecimalPlaces = 3;
            TxtLY.DoubleValue = 37.9D;
            TxtLY.FillColor2 = Color.FromArgb(248, 248, 248);
            TxtLY.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            TxtLY.Location = new Point(37, 12);
            TxtLY.Margin = new Padding(4, 5, 4, 5);
            TxtLY.MinimumSize = new Size(1, 16);
            TxtLY.Name = "TxtLY";
            TxtLY.Padding = new Padding(5);
            TxtLY.RectColor = Color.FromArgb(140, 140, 140);
            TxtLY.ScrollBarColor = Color.FromArgb(140, 140, 140);
            TxtLY.ScrollBarStyleInherited = false;
            TxtLY.ShowText = false;
            TxtLY.Size = new Size(95, 26);
            TxtLY.Style = UIStyle.Custom;
            TxtLY.TabIndex = 1;
            TxtLY.Text = "37.900";
            TxtLY.TextAlignment = ContentAlignment.MiddleCenter;
            TxtLY.Type = UITextBox.UIEditType.Double;
            TxtLY.Watermark = "";
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(11, 17);
            label1.Name = "label1";
            label1.Size = new Size(34, 24);
            label1.TabIndex = 5;
            label1.Text = "LY";
            // 
            // panel3
            // 
            tableLayoutPanel1.SetColumnSpan(panel3, 6);
            panel3.Controls.Add(BtnSave);
            panel3.Controls.Add(BtnOpen);
            panel3.Location = new Point(3, 117);
            panel3.Name = "panel3";
            panel3.Size = new Size(518, 52);
            panel3.TabIndex = 19;
            // 
            // BtnSave
            // 
            BtnSave.Location = new Point(311, 7);
            BtnSave.Name = "BtnSave";
            BtnSave.Size = new Size(131, 39);
            BtnSave.TabIndex = 1;
            BtnSave.Text = "保存标定文件";
            BtnSave.UseVisualStyleBackColor = true;
            BtnSave.Click += BtnSave_Click;
            // 
            // BtnOpen
            // 
            BtnOpen.Location = new Point(77, 7);
            BtnOpen.Name = "BtnOpen";
            BtnOpen.Size = new Size(131, 39);
            BtnOpen.TabIndex = 1;
            BtnOpen.Text = "打开标定文件";
            BtnOpen.UseVisualStyleBackColor = true;
            BtnOpen.Click += BtnOpen_Click;
            // 
            // tmrInput
            // 
            // tmrInput.Tick += TmrInput_Tick; // 已被_InputTimer.AddElapsedHandler替代
            // 
            // timer1
            // 
            // timer1.Tick += Timer1_Tick; // 已被_CalTimer替代
            // 
            // FTitlePage3
            // 
            AllowShowTitle = true;
            AutoScaleMode = AutoScaleMode.None;
            BackColor = Color.FromArgb(248, 248, 248);
            ClientSize = new Size(1900, 895);
            Controls.Add(groupBox5);
            Controls.Add(groupBox3);
            Controls.Add(groupBox4);
            Controls.Add(gbxMain);
            Name = "FTitlePage3";
            Padding = new Padding(0, 35, 0, 0);
            PageIndex = 1003;
            ShowTitle = true;
            Style = UIStyle.Custom;
            Text = "运动参数";
            FormClosing += FTitlePage3_FormClosing;
            gbxMain.ResumeLayout(false);
            uiTabControlMenu1.ResumeLayout(false);
            tPX.ResumeLayout(false);
            tPY.ResumeLayout(false);
            tPR.ResumeLayout(false);
            tPZ.ResumeLayout(false);
            tPLX.ResumeLayout(false);
            tPLY.ResumeLayout(false);
            tPLZ.ResumeLayout(false);
            tPRX.ResumeLayout(false);
            tPRY.ResumeLayout(false);
            tPRZ.ResumeLayout(false);
            panel41.ResumeLayout(false);
            groupBox4.ResumeLayout(false);
            tableLayoutPanel8.ResumeLayout(false);
            panel15.ResumeLayout(false);
            panel15.PerformLayout();
            panel47.ResumeLayout(false);
            panel47.PerformLayout();
            panel48.ResumeLayout(false);
            panel48.PerformLayout();
            panel36.ResumeLayout(false);
            panel36.PerformLayout();
            panel40.ResumeLayout(false);
            panel40.PerformLayout();
            panel12.ResumeLayout(false);
            panel12.PerformLayout();
            panel9.ResumeLayout(false);
            panel9.PerformLayout();
            panel8.ResumeLayout(false);
            panel8.PerformLayout();
            panel7.ResumeLayout(false);
            panel7.PerformLayout();
            panel13.ResumeLayout(false);
            panel13.PerformLayout();
            panel18.ResumeLayout(false);
            panel19.ResumeLayout(false);
            panel17.ResumeLayout(false);
            groupBox3.ResumeLayout(false);
            tableLayoutPanel6.ResumeLayout(false);
            panel31.ResumeLayout(false);
            panel31.PerformLayout();
            panel30.ResumeLayout(false);
            panel26.ResumeLayout(false);
            panel27.ResumeLayout(false);
            panel27.PerformLayout();
            panel29.ResumeLayout(false);
            panel28.ResumeLayout(false);
            panel32.ResumeLayout(false);
            panel32.PerformLayout();
            panel24.ResumeLayout(false);
            panel33.ResumeLayout(false);
            panel37.ResumeLayout(false);
            panel1.ResumeLayout(false);
            panel39.ResumeLayout(false);
            panel25.ResumeLayout(false);
            panel25.PerformLayout();
            panel34.ResumeLayout(false);
            panel34.PerformLayout();
            panel42.ResumeLayout(false);
            panel42.PerformLayout();
            groupBox5.ResumeLayout(false);
            tableLayoutPanel7.ResumeLayout(false);
            panel14.ResumeLayout(false);
            panel35.ResumeLayout(false);
            panel10.ResumeLayout(false);
            tableLayoutPanel1.ResumeLayout(false);
            panel6.ResumeLayout(false);
            panel6.PerformLayout();
            panel23.ResumeLayout(false);
            panel23.PerformLayout();
            panel2.ResumeLayout(false);
            panel2.PerformLayout();
            panel4.ResumeLayout(false);
            panel4.PerformLayout();
            panel21.ResumeLayout(false);
            panel21.PerformLayout();
            panel20.ResumeLayout(false);
            panel20.PerformLayout();
            panel3.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        private GroupBox gbxMain;
        private WaferAligner.电磁阀 真空卡盘;
        private WaferAligner.电磁阀 真空下晶圆;
        private WaferAligner.电磁阀 真空上晶圆;
        private WaferAligner.传感器 卡盘压力表;
        private WaferAligner.传感器 传感器2;
        private WaferAligner.传感器 传感器3;
        private WaferAligner.电磁阀 电磁阀1;
        private WaferAligner.电磁阀 电磁阀2;
        private WaferAligner.电磁阀 电磁阀3;
        private WaferAligner.传感器 传感器1;

        public Button btn回零or设零;
        private Label lbl原点信号;
        private WaferAligner.MyLED LED_ZReady;
        private Button btnCleanAlarm;
        private WaferAligner.MyLED LED_Serve;
        private Button btnServeOFF;
        private Button btnPTP;
        private Button btnSTOP;
        private Button btn继续;
        private WaferAligner.MyLED LED_PTP;

        private Button btnJOG正;
        private Button btnJOG负;
        private WaferAligner.ULine uLine1;
        private WaferAligner.MyLED myledZZero;
        private WaferAligner.MyLED myledZEnable;
        private WaferAligner.MyLED myledZAlarm;
        private WaferAligner.MyLED myledZPosComplete;

        private WaferAligner.ULine uLineZ;
        private WaferAligner.MyLED LED_ZEnable;
        private WaferAligner.MyLED LED_ZAlarm;
        private WaferAligner.MyLED LED_ZZero;
        private WaferAligner.MyLED LED_ZPosComplete;
        public TextBox textBox5;
        private Label label18;
        private Label lalCameraOffset;
        private Label label22;
        private Label label24;
        private TextBox textBox6;
        private Button button7;
        private Button button8;
        private Label label26;
        private Label label29;
        private Button button9;
        private Button button10;
        private Label label30;
        public TextBox textBox7;
        public TextBox textBox8;
        private Label label31;
        private Label label32;
        private Button button11;
        private Button button12;
        private Label label33;
        public TextBox textBox9;
        private Label label34;
        private Label label35;
        private Label label36;
        private TextBox textBox10;
        private Label label37;
        private Label label38;
        public TextBox textBox11;
        private Label label39;
        private Label label40;
        private Label label41;
        private TableLayoutPanel tableLayoutPanel4;
        public TextBox TxtZTargetPos;
        private Panel panel32;
        private Label LalZReady;
        private Panel panel33;
        private Panel panel34;
        private Button BtnZAlarmClear;
        public Button BtnZZero;
        private Button BtnZEnable;
        private Label LalZEnable;
        private Label LalZAlarm;
        private Panel panel40;
        private Panel panel41;
        private Label LalZCurenttPos;
        private Label LaltZTargetPos;
        private Label LalZTargetVel;
        private TextBox TxtZTargetVel;
        private Button BtnZPos;
        private Button BtnZPause;
        private Button BtnZContinue;
        private Label LalZPosComplete;
        private Panel panel50;
        private Panel panel51;
        private Label LalZJogVel;
        private Panel panel52;
        private Button BtnZFront;
        private Panel panel53;
        private Button BtnZBack;
        private Panel panel54;
        private Label LalZPosLimit;
        private Panel panel55;
        public TextBox TxtZPosLimit;
        private Panel panel56;
        public TextBox TxtLalZNegLimit;
        private Panel panel57;
        private Label LalZNegLimit;
        private Panel panel58;
        private Label lalZTextName;
        private Panel panel59;
        private Label lalZZero;
        private Panel panel60;
        private Panel panel61;
        public TextBox TxtZCurenttPos;
        private Panel panel62;
        private Label lalBottomPhotoOffset;
        private Panel panel63;
        private Label label15;
        private Panel panel42;
        private Label LalZCurenttVel;
        private Panel panel64;
        private TextBox TxtZCurenttVel;
        private Panel panel65;
        private Label label20;
        private Panel panel66;
        private Label label23;
        private Panel panel67;
        private Panel panel68;
        public TextBox TxtZJogVel;
        private Panel panel69;
        private Label label25;
        private Panel panel70;
        private Label label27;
        private Panel panel71;
        private Label label28;
        private Panel panel72;
        private UISymbolLabel uiSymbolLabel1;
        private WaferAligner.AxisControl 步进电机调试LZ;
        private WaferAligner.AxisControl 步进电机调试RZ;
        private WaferAligner.AxisControl 步进电机调试RX;
        private WaferAligner.AxisControl 步进电机调试LY;
        private WaferAligner.AxisControl 步进电机调试RY;
        private WaferAligner.AxisControl 步进电机调试LX;
        private GroupBox groupBox1;
        private TableLayoutPanel tableLayoutPanel5;
        private GroupBox groupBox2;
        private TableLayoutPanel tableLayoutPanel3;
        private GroupBox gbxVisual;
        private Button BtnPlat标定;
        private GroupBox groupBox4;
        private Label LalChuckLockState;
        private Label LalTrayState;
        private Label LalTopWaferState;
        private Label LalTrayWaferInnerState;
        private Panel panel18;
        private Panel panel19;
        private Panel panel17;
        private GroupBox groupBox3;
        private TableLayoutPanel tableLayoutPanel6;
        private Panel panel24;
        private Button BtnTray;
        private Panel panel25;
        private Panel panel26;
        private Button BtnTopWafer;
        private Panel panel27;
        private Panel panel28;

        private Button BtnChuckLock;
        private GroupBox groupBox5;
        private TableLayoutPanel tableLayoutPanel1;
        private Panel panel2;
        private Button BtnCalPos;
        private Panel panel4;
        private Panel panel6;
        private Label label4;
        private Label label5;
        private Label label6;
        private Label label7;
        private Panel panel14;
        private Button BtnCal;
        private TableLayoutPanel tableLayoutPanel8;
        private Panel panel36;
        private Label LalGap;
        private TableLayoutPanel tableLayoutPanel7;
        private Panel panel35;
        private Button BtnOpen;
        private Button BtnSave;
        private Panel panel1;
        private Panel panel10;
        private Button BtnFiveStop;

        private WaferAligner.AxisControl 步进电机调试Y;
        private WaferAligner.AxisControl 步进电机调试2;
        private WaferAligner.AxisControl 步进电机调试3;
        private WaferAligner.AxisControl 步进电机调试Z;
        private WaferAligner.AxisControl Y;
        private WaferAligner.AxisControl 步进电机调试R;
        private UITabControlMenu uiTabControlMenu1;
        private TabPage tPX;
        private TabPage tPY;
        private TabPage tPR;
        private TabPage tPZ;
        private TabPage tPLX;
        private TabPage tPLY;
        private TabPage tPLZ;
        private TabPage tPRX;
        private TabPage tPRY;
        private TabPage tPRZ;
        private WaferAligner.AxisControl 步进电机调试X;
        private Panel panel20;
        private Label label1;
        private Panel panel21;
        private Label label2;
        private Panel panel23;
        private Panel panel29;
        private Button BtnTrayWaferOuter;
        private Panel panel31;
        private Label LalTrayWaferOuterState;
        private Panel panel30;
        private Label LalHorizontalAdjustLockState;
        private Panel panel39;
        private Panel panel37;
        private Button BtnHorizontalAdjustLock;
        private UILight LightTopWafer;
        private UILight LightTrayWaferInner;
        private UILight LightTray;
        private UILight LightHorizontalAdjustLock;
        private UILight LightTrayWaferOuter;
        private UILight LightChuckLock;
        private UITextBox TxtRY;
        private UITextBox TxtLY;
        private UITextBox TxtLX;
        private UITextBox TxtRX;
        private UITextBox TxtLZ;
        private UITextBox TxtRZ;
        private UITextBox TxtTopGap;
        private Panel panel48;
        private UITextBox TxtZOffset;
        private Label LalOffset;
        private Panel panel47;
        private Button BtnTrayWaferInner;
        private Label label3;
        private Label label10;
        private Label label13;
        private Label label12;
        private Label label17;
        private Label label16;
        private Panel panel3;
        private Panel panel8;
        private UITextBox TxtCameraOffset;
        private Panel panel7;
        private Panel panel12;
        private Label label11;
        private UITextBox TxtBottomGap;
        private Panel panel9;
        private Label label9;
        private Panel panel15;
        private Panel panel13;
        private Label label19;
        private UITextBox TxtBottomPhotoOffset;
        private Button BtnTopWafer_Release;
    }
}