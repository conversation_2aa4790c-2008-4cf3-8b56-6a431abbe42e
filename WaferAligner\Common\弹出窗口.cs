﻿using System;
using System.Drawing;
using System.Windows.Forms;
using JYJ001.App.Services.Common.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using WaferAligner.CustomClass;
using JYJ001.App.Services.Common.Extension;
using System.Threading.Tasks; // Added for Task.Delay

namespace WaferAligner.Common
{
    public partial class Message_弹出窗口 : Form
    {
        private int count = 0;//自动关闭页面定时器计数使用
        private TimerWrapper _autoCloseTimer; // 使用TimerWrapper替代原来的Timer
        private readonly ILoggingService _loggingService;
        
        public Message_弹出窗口()
        {
            InitializeComponent();
            
            try
            {
                // 获取日志服务
                _loggingService = CommonFun.host?.Services?.GetService<ILoggingService>();
                
                // 创建TimerWrapper替代原来的Timer
                _autoCloseTimer = new TimerWrapper("MessageBox_AutoCloseTimer", 500, _loggingService);
                _autoCloseTimer.AddElapsedHandler(Timer_自动关闭弹窗_Tick);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"弹出窗口初始化TimerWrapper时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Service_Initialization_Failed);
            }
        }
        
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // 释放TimerWrapper资源
                if (_autoCloseTimer != null)
                {
                    try
                    {
                        // 先停止定时器
                        _autoCloseTimer.Stop();
                        
                        // 等待短暂时间确保回调完成
                        // 这比Thread.Sleep(150)更安全
                        var waitTask = Task.Delay(10);
                        waitTask.Wait();
                        
                        // 移除事件处理器
                        _autoCloseTimer.RemoveElapsedHandler(Timer_自动关闭弹窗_Tick);
                        
                        // 释放资源
                        _autoCloseTimer.Dispose();
                        _autoCloseTimer = null;
                        
                        _loggingService?.LogDebug("弹出窗口定时器已安全释放", WaferAligner.EventIds.EventIds.Resource_Released);
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError($"弹出窗口释放TimerWrapper时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Resource_Released);
                    }
                }
                
                // 默认的Dispose逻辑
                if (components != null)
                {
                    components.Dispose();
                }
            }
            base.Dispose(disposing);
        }

        private void BTN_No_Save_Click(object sender, EventArgs e)
        {
            CommonData.Message_ReturnNum = 2;
            CommonData.MessageBoxOpenFlag = false;
            this.Close();
        }

        private void BTN_Cancel_Click(object sender, EventArgs e)
        {
            CommonData.Message_ReturnNum = 3;
            CommonData.MessageBoxOpenFlag = false;
            this.Close();
        }

        private void BTN_Yes_Click(object sender, EventArgs e)
        {
            CommonData.Message_ReturnNum = 1;
            CommonData.MessageBoxOpenFlag = false;
            this.Close();
        }

        private void Message_弹出窗口_Load(object sender, EventArgs e)
        {
            Message_Tip.Text = CommonData.Message;
            if (1 == CommonData.MessageBoxTypeNum)
            {
                label1_提示框.Text = "提  示";
                Message_Panel.BackColor = Color.DeepSkyBlue;
                label1_提示框.BackColor = Color.DeepSkyBlue;
            }
            else if (2 == CommonData.MessageBoxTypeNum)
            {
                label1_提示框.Text = "警  告";
                Message_Panel.BackColor = Color.Yellow;
                label1_提示框.BackColor = Color.Yellow;
            }
            else if (3 == CommonData.MessageBoxTypeNum)
            {
                label1_提示框.Text = "错  误";
                Message_Panel.BackColor = Color.Red;
                label1_提示框.BackColor = Color.Red;
            }
            
            // 重置计数器
            count = 0;
            
            // 根据按钮配置设置定时器状态
            ConfigureTimerBasedOnButtonVisibility();
        }
        
        /// <summary>
        /// 根据按钮可见性配置定时器状态
        /// </summary>
        private void ConfigureTimerBasedOnButtonVisibility()
        {
            try {
                // 根据按钮数量配置
                if (CommonData.MessageBoxButtonNum == 0)
                {
                    // 无按钮模式，需要自动关闭
                    BTN_Yes.Visible = false;
                    BTN_No_Save.Visible = false;
                    BTN_Cancel.Visible = false;
                    
                    // 安全启动定时器
                    StartTimerSafely();
                }
                else if (CommonData.MessageBoxButtonNum == 1)
                {
                    // 单按钮模式
                    BTN_Yes.Visible = true;
                    BTN_No_Save.Visible = false;
                    BTN_Cancel.Visible = false;
                    
                    // 停止定时器
                    StopTimerSafely();
                }
                else if (CommonData.MessageBoxButtonNum == 2)
                {
                    // 双按钮模式
                    BTN_Yes.Visible = true;
                    BTN_No_Save.Visible = true;
                    BTN_Cancel.Visible = false;
                    
                    // 停止定时器
                    StopTimerSafely();
                }
                else if (CommonData.MessageBoxButtonNum == 3)
                {
                    // 三按钮模式
                    BTN_Yes.Visible = true;
                    BTN_No_Save.Visible = true;
                    BTN_Cancel.Visible = true;
                    
                    // 停止定时器
                    StopTimerSafely();
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"配置弹出窗口按钮和定时器时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
            }
        }
        
        /// <summary>
        /// 安全启动定时器
        /// </summary>
        private void StartTimerSafely()
        {
            try
            {
                if (_autoCloseTimer != null && !_autoCloseTimer.Enabled && !_autoCloseTimer.IsDisposed)
                {
                    _autoCloseTimer.Start();
                    _loggingService?.LogDebug("弹出窗口自动关闭定时器已启动", WaferAligner.EventIds.EventIds.Timer_Resumed);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"启动弹出窗口定时器时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Timer_Error);
            }
        }
        
        /// <summary>
        /// 安全停止定时器
        /// </summary>
        private void StopTimerSafely()
        {
            try
            {
                if (_autoCloseTimer != null && _autoCloseTimer.Enabled && !_autoCloseTimer.IsDisposed)
                {
                    _autoCloseTimer.Stop();
                    _loggingService?.LogDebug("弹出窗口自动关闭定时器已停止", WaferAligner.EventIds.EventIds.Timer_Paused);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"停止弹出窗口定时器时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Timer_Error);
            }
        }

        private void Timer_自动关闭弹窗_Tick(object sender, EventArgs e)
        {
            try
            {
                // 检查窗体状态
                if (this.IsDisposed || this.Disposing || !this.IsHandleCreated)
                {
                    StopTimerSafely();
                    return;
                }
                
                this.BeginInvoke(new Action(() => {
                    try
                    {
                        // 再次检查窗体状态
                        if (this.IsDisposed || this.Disposing)
                            return;
                            
                        // 计数器达到15，关闭窗口
                        if (15 == count)
                        {
                            StopTimerSafely();
                            CommonData.MessageBoxOpenFlag = false;
                            this.Close();
                        }
                        count++;
                    }
                    catch (ObjectDisposedException)
                    {
                        // 窗体已释放，忽略
                    }
                    catch (InvalidOperationException)
                    {
                        // 窗体句柄已释放，忽略
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError($"更新弹出窗口UI时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                    }
                }));
            }
            catch (ObjectDisposedException)
            {
                // 窗体已释放，停止定时器
                StopTimerSafely();
            }
            catch (InvalidOperationException)
            {
                // 窗体句柄可能已释放，停止定时器
                StopTimerSafely();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"弹出窗口计时器事件发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Timer_Error);
                StopTimerSafely();
            }
        }
    }
}