﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Platforms>AnyCPU;x64</Platforms>
	<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\WaferAligner.EventIds\WaferAligner.EventIds.csproj" />
    <ProjectReference Include="..\PLC.Base\PLC.Base.csproj" />
    <ProjectReference Include="..\..\Services\Service.Common\JYJ001.App.Service.Common.Interface\JYJ001.App.Service.Common.Interface.csproj" />
    <ProjectReference Include="..\..\Services\Service.Common\JYJ001.App.Service.Common.Extension\JYJ001.App.Service.Common.Extension.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
</Project>
