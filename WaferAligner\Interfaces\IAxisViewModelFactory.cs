using System;
using System.Threading.Tasks;

namespace WaferAligner.Interfaces
{
    /// <summary>
    /// 轴ViewModel工厂接口
    /// 负责创建和管理各种轴ViewModel实例
    /// </summary>
    public interface IAxisViewModelFactory
    {
        /// <summary>
        /// 创建X轴ViewModel
        /// </summary>
        Task<IXyrAxisViewModel> CreateXAxisAsync();

        /// <summary>
        /// 创建Y轴ViewModel
        /// </summary>
        Task<IXyrAxisViewModel> CreateYAxisAsync();

        /// <summary>
        /// 创建R轴ViewModel
        /// </summary>
        Task<IXyrAxisViewModel> CreateRAxisAsync();

        /// <summary>
        /// 创建Z轴ViewModel
        /// </summary>
        Task<IZAxisViewModel> CreateZAxisAsync();

        /// <summary>
        /// 创建左X相机轴ViewModel
        /// </summary>
        Task<ICameraAxisViewModel> CreateLeftXAxisAsync();

        /// <summary>
        /// 创建左Y相机轴ViewModel
        /// </summary>
        Task<ICameraAxisViewModel> CreateLeftYAxisAsync();

        /// <summary>
        /// 创建左Z相机轴ViewModel
        /// </summary>
        Task<ICameraAxisViewModel> CreateLeftZAxisAsync();

        /// <summary>
        /// 创建右X相机轴ViewModel
        /// </summary>
        Task<ICameraAxisViewModel> CreateRightXAxisAsync();

        /// <summary>
        /// 创建右Y相机轴ViewModel
        /// </summary>
        Task<ICameraAxisViewModel> CreateRightYAxisAsync();

        /// <summary>
        /// 创建右Z相机轴ViewModel
        /// </summary>
        Task<ICameraAxisViewModel> CreateRightZAxisAsync();

        /// <summary>
        /// 获取X轴ViewModel（单例）
        /// </summary>
        IXyrAxisViewModel GetXAxisViewModel();

        /// <summary>
        /// 异步获取X轴ViewModel（单例）
        /// </summary>
        Task<IXyrAxisViewModel> GetXAxisViewModelAsync();

        /// <summary>
        /// 获取Y轴ViewModel（单例）
        /// </summary>
        IXyrAxisViewModel GetYAxisViewModel();

        /// <summary>
        /// 异步获取Y轴ViewModel（单例）
        /// </summary>
        Task<IXyrAxisViewModel> GetYAxisViewModelAsync();

        /// <summary>
        /// 获取R轴ViewModel（单例）
        /// </summary>
        IXyrAxisViewModel GetRAxisViewModel();

        /// <summary>
        /// 异步获取R轴ViewModel（单例）
        /// </summary>
        Task<IXyrAxisViewModel> GetRAxisViewModelAsync();

        /// <summary>
        /// 获取Z轴ViewModel（单例）
        /// </summary>
        IZAxisViewModel GetZAxisViewModel();

        /// <summary>
        /// 异步获取Z轴ViewModel（单例）
        /// </summary>
        Task<IZAxisViewModel> GetZAxisViewModelAsync();

        /// <summary>
        /// 获取左X相机轴ViewModel（单例）
        /// </summary>
        ICameraAxisViewModel GetLXAxisViewModel();

        /// <summary>
        /// 异步获取左X相机轴ViewModel（单例）
        /// </summary>
        Task<ICameraAxisViewModel> GetLXAxisViewModelAsync();

        /// <summary>
        /// 获取左Y相机轴ViewModel（单例）
        /// </summary>
        ICameraAxisViewModel GetLYAxisViewModel();

        /// <summary>
        /// 异步获取左Y相机轴ViewModel（单例）
        /// </summary>
        Task<ICameraAxisViewModel> GetLYAxisViewModelAsync();

        /// <summary>
        /// 获取左Z相机轴ViewModel（单例）
        /// </summary>
        ICameraAxisViewModel GetLZAxisViewModel();

        /// <summary>
        /// 异步获取左Z相机轴ViewModel（单例）
        /// </summary>
        Task<ICameraAxisViewModel> GetLZAxisViewModelAsync();

        /// <summary>
        /// 获取右X相机轴ViewModel（单例）
        /// </summary>
        ICameraAxisViewModel GetRXAxisViewModel();

        /// <summary>
        /// 异步获取右X相机轴ViewModel（单例）
        /// </summary>
        Task<ICameraAxisViewModel> GetRXAxisViewModelAsync();

        /// <summary>
        /// 获取右Y相机轴ViewModel（单例）
        /// </summary>
        ICameraAxisViewModel GetRYAxisViewModel();

        /// <summary>
        /// 异步获取右Y相机轴ViewModel（单例）
        /// </summary>
        Task<ICameraAxisViewModel> GetRYAxisViewModelAsync();

        /// <summary>
        /// 获取右Z相机轴ViewModel（单例）
        /// </summary>
        ICameraAxisViewModel GetRZAxisViewModel();
        
        /// <summary>
        /// 异步获取右Z相机轴ViewModel（单例）
        /// </summary>
        Task<ICameraAxisViewModel> GetRZAxisViewModelAsync();

        /// <summary>
        /// 批量创建所有轴ViewModel
        /// </summary>
        Task<Models.AxisViewModelCollection> CreateAllAxesAsync();

        /// <summary>
        /// 清理所有创建的轴实例
        /// </summary>
        Task CleanupAsync();
    }
} 