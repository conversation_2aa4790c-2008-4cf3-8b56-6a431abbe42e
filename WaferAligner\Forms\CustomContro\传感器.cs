﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WaferAligner
{
    public partial class 传感器 : InstrumnetControl
    {
        private string m_Running;
        public 传感器()
        {
            InitializeComponent();
        }
        [Browsable(true)]
        public string 名称
        {
            get { return this.label1.Text; }
            set { this.label1.Text = value; }
        }

        [Browsable(true)]
        public string 状态地址
        {
            get { return m_Running; }
            set { m_Running = value; }
        }

        public override void UpdateState()
        {
            pictureBox1.BackColor = PLC.ReadData(m_Running).Equals("1") ? Color.Green : Color.Gray;
        }
    }
}
