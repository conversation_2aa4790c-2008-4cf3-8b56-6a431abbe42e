# Z轴和相机轴整改方案

## 一、问题分析

### 1.1 现状分析

当前WaferAligner项目中的轴控制系统存在以下不统一和问题：

1. **接口实现不一致**：
   - Z轴使用`ZAxisViewModel`类实现`IZAxisViewModel`接口，分为两个文件：
     - `ZAxisViewModel.cs`（3.7KB）：包含基本属性和特殊功能
     - `ZAxisViewModel.IZAxisViewModel.cs`（14KB）：接口实现部分
   - 相机轴（LX/LY/LZ/RX/RY/RZ）使用`CameralHoldAxisViewModel`类实现`ICameraAxisViewModel`接口，同样分为两个文件：
     - `CameralHoldAxisViewModel.cs`（1.9KB）：基本属性和构造函数
     - `CameralHoldAxisViewModel.ICameraAxisViewModel.cs`（13KB）：接口实现部分
   - XYR轴使用`SerialAxisViewModel`类实现`IXyrAxisViewModel`接口

2. **通信方式差异**：
   - XYR轴：使用串口通信
   - Z轴和相机轴：使用汇川PLC通信
   - 不同通信方式导致代码结构不同，但基本操作类似

3. **功能差异**：
   - Z轴具有特殊功能：
     ```csharp
     // Z轴特有的原点偏移设置
     public async Task<bool> HomeOffset(double value) => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.ZHomeOffset", value * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
     
     // Z轴特有的紧急停止
     public async Task<bool> ZTakeForceStop()
     {
         bool Res = false;
         Res = await WritePLCVariable($"{AxisConstants.AXIS_GVL}.ZTakeForceStop", true);
         await Task.Delay(200);
         Res = await WritePLCVariable($"{AxisConstants.AXIS_GVL}.ZTakeForceStop", false);
         return Res;
     }
     ```
   - 相机轴需要记录相机位置信息（左/右）：
     ```csharp
     public string CameraPosition => _cameraPosition;
     
     // 设置相机位置的构造函数
     public CameralHoldAxisViewModel(string axis, IPlcInstance plcInstance, string plcAddress, int port, string cameraPosition)
     ```

4. **实现方式不统一**：
   - Z轴和相机轴都使用分部类实现接口，导致代码分散
   - 部分错误处理和资源管理逻辑不一致，例如：
     ```csharp
     // 相机轴实现的RegisterAction
     public void RegisterAction(string variableName, Action<object> action)
     {
         try
         {
             this.RegistryAction(variableName, action);
             _loggingService?.LogDebug($"注册{CameraPosition}相机{AxisName}轴变量 {variableName} 的动作", EventIds.ResourceRegistered);
         }
         catch (Exception ex)
         {
             _loggingService?.LogError(ex, $"注册{CameraPosition}相机{AxisName}轴变量 {variableName} 的动作失败", EventIds.UnhandledException);
         }
     }
     ```

5. **单例模式实现不一致**：
   - XYR轴：使用严格的双检锁单例模式
   - Z轴和相机轴：在工厂类中使用不完整的单例模式：
     ```csharp
     // 相机轴获取单例的方式（有问题的实现）
     public async Task<ICameraAxisViewModel> GetLXAxisViewModelAsync()
     {
         if (_lxAxisInstance == null)
         {
             lock (_lock)
             {
                 if (_lxAxisInstance == null)
                 {
                     try
                     {
                         // 创建临时变量，避免直接赋值给单例字段
                         var task = CreateLeftXAxisAsync();
                         // 使用临时变量等待任务完成（阻塞调用）
                         task.Wait();
                         // 任务完成后，将结果赋值给单例字段
                         _lxAxisInstance = task.Result;
                     }
                     catch (Exception ex)
                     {
                         // 错误处理...
                     }
                 }
             }
         }
         return _lxAxisInstance;
     }
     ```

6. **异步方法实现不一致**：
   - 大量使用GetAwaiter().GetResult()阻塞UI线程：
     ```csharp
     // 阻塞获取轴实例的例子
     public IZAxisViewModel GetZAxisViewModel()
     {
         // 使用异步方法的结果，但保持同步调用方式兼容旧代码
         return GetZAxisViewModelAsync().ConfigureAwait(false).GetAwaiter().GetResult();
     }
     ```
   - 伪异步代码（同步代码包装为异步返回）：
     ```csharp
     // 伪异步示例
     public async Task<double> GetCurrentPositionAsync()
     {
         try
         {
             // 直接返回属性，没有真正的异步操作
             return await Task.FromResult(RealTimePosition);
         }
         catch (Exception ex)
         {
             _loggingService?.LogError(ex, $"获取当前位置失败", EventIds.AxisMoveError);
             return 0;
         }
     }
     ```
   - PLC通信使用Task.Run包装同步操作：
     ```csharp
     // 使用Task.Run包装同步PLC操作
     var ret = await Task.Run(async () =>
     {
         return await plcInstance.WriteVariableAsync(new PLCVarWriteInfo { Name = name, Value = value }, cancle: CancellationToken.None);
     });
     ```

7. **异步化程度不足**：
   - 缺少CancellationToken支持：
     ```csharp
     // 缺少取消支持的异步方法
     public async Task<bool> MoveToPositionAsync(double position)
     {
         try
         {
             await SetPosition(position);
             await GoPosition();
             return true;
         }
         catch (Exception ex)
         {
             _loggingService?.LogError(ex, $"移动轴到位置 {position} 失败", EventIds.AxisMoveError);
             return false;
         }
     }
     ```
   - 缺少进度报告机制

### 1.2 问题根源

1. **多次重构导致的不统一**：
   - 项目经历了多个阶段的重构，每次重构关注点不同
   - Phase 2重构引入了适配器模式，Phase 3重构又移除了适配器，导致代码不一致
   - 不同开发人员对接口设计理解不同，导致实现差异

2. **历史兼容性考虑**：
   - 为保持向后兼容，保留了旧代码和接口，例如：
     ```csharp
     // 保留同步方法兼容旧代码
     public IZAxisViewModel GetZAxisViewModel()
     {
         return GetZAxisViewModelAsync().ConfigureAwait(false).GetAwaiter().GetResult();
     }
     ```

3. **代码结构问题**：
   - 缺乏统一的基类，虽然有`GenericAxis`和`CommonAxis<T>`，但使用不一致
   - 接口设计过于特化，没有遵循接口隔离原则
   - 缺少统一的异步通信层

4. **功能需求差异**：
   - Z轴垂直运动需要特殊安全机制和精确控制
   - 相机轴需要与相机系统集成，保留位置信息
   - 这些差异导致接口设计和实现不同

5. **异步编程理解不一致**：
   - 大量使用Task.Run包装同步代码（伪异步）：
     ```csharp
     // PLC读取操作的伪异步实现
     var ret = await Task.Run(async () =>
     {
         return await plcInstance.ReadVariableAsync(ReadInfoJogVel, CancellationToken.None);
     });
     ```
   - 缺乏对异步/等待模式的正确应用
   - 过度使用GetAwaiter().GetResult()，可能导致死锁

## 二、整改目标

1. **统一接口实现方式**：
   - 为所有PLC控制的轴（Z轴和相机轴）提供一致的实现方式
   - 消除分部类实现，改为直接实现接口
   - **同时保留各轴的特殊功能**

2. **统一单例模式实现**：
   - 为所有轴提供一致的单例获取方式
   - 使用工厂类统一创建和管理轴实例

3. **完全异步化**：
   - 确保所有轴控制操作都是真正的异步实现
   - 消除所有伪异步代码（同步代码包装为异步返回）
   - 添加对异步操作取消的全面支持
   - 防止UI线程阻塞

4. **改进错误处理**：
   - 统一异常处理方式
   - 增强日志记录

5. **保持功能完整性**：
   - 确保原有功能不受影响
   - 保持与现有代码的兼容性
   - **确保Z轴特殊功能和安全机制完整保留**

## 三、整改方案

### 3.1 接口层整改

1. **创建分层接口结构**：
   - 创建`IPlcAxisViewModel`接口作为所有PLC控制轴的基础接口，扩展`IAxisViewModel`
   ```csharp
   /// <summary>
   /// PLC控制轴的通用接口，继承自IAxisViewModel
   /// </summary>
   public interface IPlcAxisViewModel : IAxisViewModel
   {
       // 添加PLC轴通用操作
       Task<bool> WritePlcVariableAsync<T>(string variableName, T value, CancellationToken cancellationToken = default);
       Task<T> ReadPlcVariableAsync<T>(string variableName, CancellationToken cancellationToken = default);
       Task<bool> SetSafetyCheckEnabledAsync(bool enabled, CancellationToken cancellationToken = default);
   }
   ```
   - 保留并调整`IZAxisViewModel`和`ICameraAxisViewModel`接口，继承自`IPlcAxisViewModel`：
   ```csharp
   /// <summary>
   /// Z轴ViewModel接口，继承自IPlcAxisViewModel
   /// </summary>
   public interface IZAxisViewModel : IPlcAxisViewModel
   {
       Task<bool> SetHomeOffsetAsync(double value, CancellationToken cancellationToken = default);
       Task<bool> ZHomeStopAsync(CancellationToken cancellationToken = default);
       Task<bool> ZTakeForceStopAsync(CancellationToken cancellationToken = default);
   }
   
   /// <summary>
   /// 相机支架轴ViewModel接口，继承自IPlcAxisViewModel
   /// </summary>
   public interface ICameraAxisViewModel : IPlcAxisViewModel
   {
       string CameraPosition { get; }
   }
   ```

2. **接口重构**：
   - 审查并精简接口，将共有方法提升到`IPlcAxisViewModel`中：
   ```csharp
   public interface IAxisViewModel
   {
       // 基础属性
       string AxisName { get; }
       bool IsConnected { get; }
       bool IsReady { get; }
       bool IsEnabled { get; }
       bool HasError { get; }
       bool Arrive_position { get; }
       
       // 核心异步方法
       Task<bool> MoveToPositionAsync(double position, CancellationToken cancellationToken = default);
       Task<bool> SetPositionAsync(double position, CancellationToken cancellationToken = default);
       Task<double> GetCurrentPositionAsync(CancellationToken cancellationToken = default);
       Task<bool> HomeAsync(CancellationToken cancellationToken = default);
       Task<bool> StopAsync(CancellationToken cancellationToken = default);
       Task<bool> ResetAsync(CancellationToken cancellationToken = default);
       
       // 事件注册与注销
       Task RegisterActionAsync(string variableName, Action<object> action, CancellationToken cancellationToken = default);
       Task UnregisterActionAsync(string variableName, CancellationToken cancellationToken = default);
       
       // 连接管理
       Task<bool> ConnectAsync(string address, int port, CancellationToken cancellationToken = default);
       Task DisconnectAsync(CancellationToken cancellationToken = default);
   }
   ```

3. **Z轴特殊接口保留**：
   - 确保所有Z轴特殊功能都保留在`IZAxisViewModel`接口中，但添加取消支持

4. **异步接口统一**：
   - 所有操作方法都使用Async后缀，并添加CancellationToken参数
   - 添加IProgress<T>参数用于进度报告：
   ```csharp
   Task<bool> MoveToPositionAsync(double position, IProgress<double> progress = null, CancellationToken cancellationToken = default);
   ```

### 3.2 实现层整改

1. **创建统一基类**：
   - 创建`PlcAxisViewModelBase`抽象基类，实现`IPlcAxisViewModel`接口：
   ```csharp
   /// <summary>
   /// PLC轴ViewModel基类
   /// </summary>
   public abstract class PlcAxisViewModelBase : ObservableValidator, IPlcAxisViewModel, IDisposable
   {
       protected readonly IPlcInstance _plcInstance;
       protected readonly ILoggingService _loggingService;
       protected readonly ConcurrentDictionary<string, ConcurrentBag<Action<object>>> _variableActions = new();
       
       // 构造函数
       protected PlcAxisViewModelBase(string axisName, IPlcInstance plcInstance, ILoggingService loggingService)
       {
           AxisName = axisName ?? throw new ArgumentNullException(nameof(axisName));
           _plcInstance = plcInstance;
           _loggingService = loggingService;
       }
       
       // IPlcAxisViewModel接口实现
       public string AxisName { get; }
       public bool IsConnected { get; protected set; }
       public bool IsReady { get; protected set; }
       public bool IsEnabled { get; protected set; }
       public bool HasError { get; protected set; }
       public bool Arrive_position { get; protected set; }
       
       // 异步操作实现
       public virtual async Task<bool> MoveToPositionAsync(double position, CancellationToken cancellationToken = default)
       {
           try
           {
               cancellationToken.ThrowIfCancellationRequested();
               
               // 设置目标位置
               await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}Position", position, cancellationToken);
               
               // 执行移动命令
               await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.MoveABS, cancellationToken);
               await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true, cancellationToken);
               
               return true;
           }
           catch (OperationCanceledException)
           {
               _loggingService?.LogWarning($"移动{AxisName}轴到位置 {position} 操作被取消", EventIds.AxisMoveError);
               throw;
           }
           catch (Exception ex)
           {
               _loggingService?.LogError(ex, $"移动{AxisName}轴到位置 {position} 失败", EventIds.AxisMoveError);
               return false;
           }
       }
       
       // 实现异步PLC变量读写，不使用Task.Run包装
       public virtual async Task<bool> WritePlcVariableAsync<T>(string variableName, T value, CancellationToken cancellationToken = default)
       {
           if (_plcInstance == null)
           {
               _loggingService?.LogWarning($"PLC实例为null，跳过变量写入 - {AxisName}轴, 变量: {variableName}", EventIds.PlcInstanceNullSkipWrite);
               return false;
           }
           
           try
           {
               return await _plcInstance.WriteVariableAsync(
                   new PLCVarWriteInfo { Name = variableName, Value = value },
                   cancellationToken).ConfigureAwait(false);
           }
           catch (Exception ex) when (!(ex is OperationCanceledException))
           {
               _loggingService?.LogError(ex, $"PLC变量:{variableName}:{value} 写入失败", EventIds.PlcVariablesWriteFailed);
               return false;
           }
       }
       
       // IDisposable实现
       public void Dispose()
       {
           Dispose(true);
           GC.SuppressFinalize(this);
       }
       
       protected virtual void Dispose(bool disposing)
       {
           if (disposing)
           {
               // 清理托管资源
               _variableActions.Clear();
           }
       }
   }
   ```

2. **Z轴实现重构**：
   - 重构`ZAxisViewModel`，继承自`PlcAxisViewModelBase`：
   ```csharp
   public class ZAxisViewModel : PlcAxisViewModelBase, IZAxisViewModel
   {
       public ZAxisViewModel(string axisName, IPlcInstance plcInstance, string plcAddress, int port, ILoggingService loggingService)
           : base(axisName, plcInstance, loggingService)
       {
           // 初始化代码
           _ = ConnectAsync(plcAddress, port);
       }
       
       // Z轴特殊功能实现
       public async Task<bool> SetHomeOffsetAsync(double value, CancellationToken cancellationToken = default)
       {
           try
           {
               cancellationToken.ThrowIfCancellationRequested();
               
               return await WritePlcVariableAsync(
                   $"{AxisConstants.AXIS_GVL}.ZHomeOffset",
                   value * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION,
                   cancellationToken);
           }
           catch (OperationCanceledException)
           {
               throw;
           }
           catch (Exception ex)
           {
               _loggingService?.LogError(ex, $"设置Z轴回原点偏移 {value} 失败", EventIds.AxisMoveError);
               return false;
           }
       }
       
       // 紧急停止功能
       public async Task<bool> ZHomeStopAsync(CancellationToken cancellationToken = default)
       {
           bool result = false;
           try
           {
               cancellationToken.ThrowIfCancellationRequested();
               
               result = await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.ZHomeStop", true, cancellationToken);
               
               // 使用Task.Delay替代直接延时，支持取消
               using var delayTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
               await Task.Delay(200, delayTokenSource.Token);
               
               result = await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.ZHomeStop", false, cancellationToken);
               return result;
           }
           catch (OperationCanceledException)
           {
               _loggingService?.LogWarning("Z轴紧急停止操作被取消", EventIds.AxisMoveError);
               // 确保在取消时也设置回false状态
               try
               {
                   await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.ZHomeStop", false, CancellationToken.None);
               }
               catch { /* 忽略清理操作中的错误 */ }
               throw;
           }
           catch (Exception ex)
           {
               _loggingService?.LogError(ex, "Z轴紧急停止失败", EventIds.AxisMoveError);
               return false;
           }
       }
       
       // 实现其他特殊功能...
   }
   ```

3. **相机轴实现重构**：
   - 重构`CameralHoldAxisViewModel`，继承自`PlcAxisViewModelBase`：
   ```csharp
   public class CameralHoldAxisViewModel : PlcAxisViewModelBase, ICameraAxisViewModel
   {
       private readonly string _cameraPosition;
       
       public string CameraPosition => _cameraPosition;
       
       public CameralHoldAxisViewModel(string axisName, IPlcInstance plcInstance, string plcAddress, int port, 
           string cameraPosition, ILoggingService loggingService)
           : base(axisName, plcInstance, loggingService)
       {
           _cameraPosition = cameraPosition ?? throw new ArgumentNullException(nameof(cameraPosition));
           _ = ConnectAsync(plcAddress, port);
       }
       
       // 重写基类方法，添加相机位置标识到日志
       public override async Task<bool> MoveToPositionAsync(double position, CancellationToken cancellationToken = default)
       {
           try
           {
               cancellationToken.ThrowIfCancellationRequested();
               
               // 设置目标位置
               await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}Position", position, cancellationToken);
               
               // 执行移动命令
               await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.MoveABS, cancellationToken);
               await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true, cancellationToken);
               
               return true;
           }
           catch (OperationCanceledException)
           {
               _loggingService?.LogWarning($"移动{CameraPosition}相机{AxisName}轴到位置 {position} 操作被取消", EventIds.AxisMoveError);
               throw;
           }
           catch (Exception ex)
           {
               _loggingService?.LogError(ex, $"移动{CameraPosition}相机{AxisName}轴到位置 {position} 失败", EventIds.AxisMoveError);
               return false;
           }
       }
       
       // 实现其他特殊方法...
   }
   ```

4. **统一资源管理**：
   - 统一IDisposable实现，确保所有资源正确释放
   - 统一事件注册和注销机制

5. **异步实现策略**：
   - 使用真正的异步PLC通信API，避免使用Task.Run包装
   - 提供进度报告机制和取消支持
   - 添加超时处理

### 3.3 工厂层整改

1. **统一工厂方法**：
   - 重构`AxisViewModelFactory`，提供一致的实例获取方法：
   ```csharp
   public class AxisViewModelFactory : IAxisViewModelFactory
   {
       private readonly IPlcConnectionManager _plcManager;
       private readonly ILoggingService _loggingService;
       private readonly IServiceProvider _serviceProvider;
       
       // 单例字段
       private readonly Dictionary<string, IAxisViewModel> _axisInstances = new();
       private readonly object _lock = new object();
       
       public AxisViewModelFactory(IPlcConnectionManager plcManager, ILoggingService loggingService, IServiceProvider serviceProvider)
       {
           _plcManager = plcManager;
           _loggingService = loggingService;
           _serviceProvider = serviceProvider;
       }
       
       // 异步工厂方法
       public async Task<IZAxisViewModel> GetZAxisViewModelAsync(CancellationToken cancellationToken = default)
       {
           // 检查现有实例
           if (_axisInstances.TryGetValue("Z", out var existingAxis) && existingAxis is IZAxisViewModel zAxis)
           {
               return zAxis;
           }
           
           // 双检锁模式
           lock (_lock)
           {
               if (_axisInstances.TryGetValue("Z", out existingAxis) && existingAxis is IZAxisViewModel zAxisLocked)
               {
                   return zAxisLocked;
               }
               
               // 创建新实例
               try
               {
                   var plcInstance = _plcManager.GetPlcInstance("ZAxis");
                   if (plcInstance == null)
                   {
                       // 使用异步API连接PLC
                       if (await _plcManager.ConnectAsync("ZAxis", "192.168.1.88", 502, cancellationToken))
                       {
                           plcInstance = _plcManager.GetPlcInstance("ZAxis");
                       }
                   }
                   
                   var zAxisViewModel = new ZAxisViewModel("Z", plcInstance, "192.168.1.88", 502, _loggingService);
                   _axisInstances["Z"] = zAxisViewModel;
                   return zAxisViewModel;
               }
               catch (Exception ex)
               {
                   _loggingService.LogError(ex, "创建Z轴ViewModel失败", EventIds.AxisInstanceCreateFailed);
                   
                   // 创建一个离线实例作为回退
                   var offlineInstance = new ZAxisViewModel("Z", null, "192.168.1.88", 502, _loggingService);
                   _axisInstances["Z"] = offlineInstance;
                   return offlineInstance;
               }
           }
       }
       
       // 向后兼容的同步方法
       [Obsolete("请使用异步版本GetZAxisViewModelAsync")]
       public IZAxisViewModel GetZAxisViewModel()
       {
           // 使用不同的同步上下文避免死锁
           var task = Task.Run(() => GetZAxisViewModelAsync());
           return task.GetAwaiter().GetResult();
       }
       
       // 相机轴工厂方法
       public async Task<ICameraAxisViewModel> GetLXAxisViewModelAsync(CancellationToken cancellationToken = default)
       {
           // 类似的实现...
       }
       
       // 其他轴的工厂方法...
   }
   ```

2. **统一异步初始化**：
   - 提供一致的异步初始化方法
   - 保留同步方法作为过渡性兼容方案

3. **异步工厂方法**：
   - 使用一致的方法命名和参数
   - 添加适当的异常处理和日志记录

### 3.4 错误处理整改

1. **统一异常处理**：
   - 利用现有.NET异常类型，添加详细错误信息
   - 在基类中提供通用异常处理逻辑

2. **增强日志记录**：
   - 使用统一的`ILoggingService`
   - 为所有操作添加适当的事件ID

3. **异步错误处理**：
   - 正确处理异步操作中的异常
   - 添加特定异常过滤器：
   ```csharp
   try
   {
       // 异步操作...
   }
   catch (OperationCanceledException)
   {
       _loggingService?.LogWarning($"操作被取消", EventIds.OperationCancelled);
       throw; // 重新抛出取消异常，让调用者处理
   }
   catch (Exception ex) when (!(ex is OperationCanceledException))
   {
       _loggingService?.LogError(ex, $"操作失败", EventIds.AxisMoveError);
       return false;
   }
   ```

### 3.5 处理Z轴特殊功能

1. **安全机制保留**：
   - 确保Z轴的紧急停止和强制停止功能完整保留
   - 在基类中添加可选的安全机制钩子，允许Z轴实现特有的安全逻辑

2. **原点偏移设置**：
   - 保留`SetHomeOffsetAsync`方法在Z轴实现中
   - 考虑将通用的原点设置功能提升到基类中，让其他轴也可选择使用

3. **特殊状态处理**：
   - 为Z轴添加特殊状态监控和处理
   - 确保垂直运动的安全限制得到维护

### 3.6 完全异步化实现

1. **PLC通信异步化**：
   - 使用异步I/O模式与PLC通信
   - 避免使用同步阻塞API
   - 实现批量读写操作的异步化

2. **状态查询异步化**：
   - 将所有状态查询操作改为异步实现
   - 使用异步轮询或事件通知机制
   - 避免使用`Thread.Sleep`或同步等待

3. **运动控制异步化**：
   - 将所有运动控制命令改为异步实现
   - 实现异步运动完成通知机制
   - 利用现有.NET类型实现进度报告功能

4. **UI更新异步化**：
   - 确保所有UI更新在正确的线程上执行
   - 使用标准`IProgress<T>`报告进度
   - 避免在异步操作中直接更新UI

5. **取消支持**：
   - 为所有长时间运行的异步操作添加取消支持
   - 实现优雅的取消机制
   - 确保资源在取消时正确释放

## 四、实施步骤

### 4.1 准备阶段

1. **代码审查**：
   - 全面审查现有Z轴和相机轴代码
   - 识别所有需要保留的特殊功能
   - **特别关注Z轴的安全机制和特殊功能**
   - **识别所有同步实现和伪异步代码**

2. **测试用例准备**：
   - 创建测试用例验证当前功能
   - 确保有足够的测试覆盖率
   - **为Z轴特殊功能创建专门的测试用例**
   - **创建异步操作测试用例**

### 4.2 实施阶段

1. **创建接口层**：
   - 实现`IPlcAxisViewModel`接口
   - 重构`IZAxisViewModel`和`ICameraAxisViewModel`接口
   - **确保所有接口方法都是异步的**

2. **创建基础设施**：
   - 实现`PlcAxisViewModelBase`抽象基类
   - 创建必要的辅助类和扩展方法
   - **实现异步操作基础设施**

3. **Z轴整改**：
   - 重构`ZAxisViewModel`类，继承自`PlcAxisViewModelBase`
   - **确保特殊功能和安全机制得到保留**
   - **将所有操作改为真正的异步实现**
   - 更新相关依赖注入配置

4. **相机轴整改**：
   - 重构`CameralHoldAxisViewModel`类，继承自`PlcAxisViewModelBase`
   - 移除分部类实现
   - **将所有操作改为真正的异步实现**

5. **工厂类整改**：
   - 更新`AxisViewModelFactory`
   - 统一单例获取逻辑
   - **实现异步工厂方法**

6. **错误处理整改**：
   - 实现统一的异常处理
   - 更新日志记录
   - **添加异步错误处理机制**

7. **异步化改造**：
   - 识别并替换所有伪异步代码
   - 添加取消支持
   - 实现进度报告机制

### 4.3 验证阶段

1. **单元测试**：
   - 对重构后的类进行单元测试
   - 验证所有功能正常工作
   - **特别验证Z轴特殊功能**
   - **测试异步操作的正确性**

2. **集成测试**：
   - 测试与其他组件的集成
   - 验证系统整体功能
   - **测试多个异步操作的协调**

3. **性能测试**：
   - 测试重构后的性能
   - 确保没有性能退化
   - **测量异步操作的响应时间**

4. **安全测试**：
   - 特别测试Z轴的安全机制
   - 验证紧急停止和强制停止功能
   - **测试异步操作的取消机制**

5. **UI响应性测试**：
   - 测试UI在执行长时间运行操作时的响应性
   - 确保没有UI线程阻塞
   - 验证进度报告机制

## 五、风险与缓解措施

1. **功能中断风险**：
   - **缓解**：采用渐进式重构，确保每一步都可以正常工作
   - **缓解**：保留原有类作为备份，直到新实现完全验证
   - **缓解**：使用适配器模式作为过渡手段，降低一次性重构风险

2. **兼容性风险**：
   - **缓解**：确保新接口与现有客户端代码兼容
   - **缓解**：使用适配器模式处理不兼容情况
   - **缓解**：提供特性切换机制，允许在运行时选择新旧实现

3. **性能风险**：
   - **缓解**：进行性能基准测试
   - **缓解**：优化关键路径代码

4. **Z轴特殊功能丢失风险**：
   - **缓解**：创建详细的功能清单，确保所有功能都被保留
   - **缓解**：为Z轴特殊功能创建专门的测试用例
   - **缓解**：在重构过程中保持与原始代码的对比

5. **异步化风险**：
   - **缓解**：使用异步分析工具检查代码
   - **缓解**：实施全面的异步操作测试
   - **缓解**：逐步引入异步模式，而不是一次性全部更改
   - **缓解**：优先处理UI响应问题最严重的部分

## 六、时间规划

1. **准备阶段**：2天
   - 代码审查和测试准备
   - Z轴特殊功能分析
   - **异步化需求分析**
   - **为伪异步和阻塞代码创建检测工具**

2. **接口设计**：2天
   - 创建分层接口结构
   - 确保Z轴特殊功能在接口中得到保留
   - **设计统一的异步接口**
   - **设计适配器类模式**

3. **基础设施实现**：3天
   - 创建基类和辅助类
   - **实现异步操作基础设施**
   - **创建异步工具类**
   - **实现适配器类作为过渡方案**

4. **Z轴整改**：2天
   - 重构实现和测试
   - 确保特殊功能完整保留
   - **实现真正的异步操作**
   - **添加取消和进度支持**

5. **相机轴整改**：2天
   - 重构实现和测试
   - **实现真正的异步操作**
   - **移除分部类实现**
   - **添加取消和进度支持**

6. **工厂类整改**：2天
   - 更新工厂类和依赖注入
   - **实现异步工厂方法**
   - **修复单例模式实现**
   - **消除GetAwaiter().GetResult()调用**

7. **适配器实施与迁移**：2天
   - 逐步将调用点从旧实现迁移到新实现
   - 添加特性切换机制
   - 验证新旧实现并存的可行性

8. **验证与修复**：3天
   - 全面测试和问题修复
   - 特别验证Z轴功能
   - **验证异步操作的正确性**
   - **测试UI响应性**
   - **检验死锁和性能问题**

**总计**：18个工作日

## 七、成功标准

1. 所有Z轴和相机轴使用统一的实现方式
2. 所有单例模式实现一致
3. **所有操作都是真正的异步实现，没有伪异步代码**
4. **异步操作支持取消和进度报告**
5. **UI线程不会因为轴操作而阻塞**
6. 错误处理和日志记录统一
7. **Z轴的所有特殊功能和安全机制完整保留**
8. 所有现有功能正常工作
9. 代码更加简洁、可维护
10. 没有编译警告和错误

## 八、后续工作

1. **通信方式差异化架构**：
   - 区分PLC控制轴（Z轴和相机轴）和串口控制轴（XYR轴）
   - 为不同通信方式的轴提供专门的基类（`PlcAxisViewModelBase`和`SerialAxisViewModelBase`）
   - 在接口层保持统一，在实现层区分通信方式

2. **统一接口层设计**：
   - 设计更加通用的`IAxisViewModel`接口层次结构
   - 将通信方式无关的功能提升到共同接口
   - 通过接口隔离原则区分不同类型轴的特殊功能

3. **优化PLC通信批处理**：
   - 专注于改进PLC控制轴的批量读写操作
   - 实现变量组概念，减少通信次数
   - 优化PLC轴的通信性能

4. **添加更全面的单元测试**：
   - 为PLC轴和串口轴分别创建测试套件
   - 测试不同通信方式下的异常处理
   - 验证异步操作在不同通信方式下的行为

5. **更新技术文档**：
   - 明确记录PLC轴和串口轴的架构差异
   - 提供通信方式选择指南
   - 记录不同类型轴的最佳实践

6. **考虑为串口轴添加安全机制**：
   - 借鉴Z轴的安全机制设计
   - 为串口控制的XYR轴添加适当的安全限制
   - 统一安全处理模式，但保持实现的差异化

7. **实现更高级的异步操作编排**：
   - 支持跨通信方式的协调操作
   - 实现复合轴运动（同时控制多个不同通信方式的轴）
   - 优化不同通信方式轴之间的协作

8. **添加性能监控和诊断功能**：
   - 分别监控PLC通信和串口通信性能
   - 提供通信延迟和吞吐量统计
   - 实现通信故障诊断工具 

## 九、详细清单

### 9.1 需要替换的内容

#### 接口替换
| 当前接口 | 替换为 | 影响范围 |
|---------|-------|---------|
| `ICameraAxisViewModel` | 保留，但继承自新的 `IPlcAxisViewModel` | 相机轴实现类、工厂类、客户端代码 |
| `IZAxisViewModel` | 保留，但继承自新的 `IPlcAxisViewModel` | Z轴实现类、工厂类、客户端代码 |
| 无 | 新增 `IPlcAxisViewModel` 接口 | 所有PLC控制轴 |

#### 实现类替换
| 当前实现类 | 替换为 | 影响范围 |
|----------|-------|---------|
| `ZAxisViewModel` | 重构为继承自 `PlcAxisViewModelBase` | Z轴相关功能、工厂类 |
| `CameralHoldAxisViewModel` | 重构为继承自 `PlcAxisViewModelBase` | 相机轴相关功能、工厂类 |
| `CameralHoldAxisViewModel.ICameraAxisViewModel.cs`（分部类） | 移除，合并到主类中 | 相机轴实现 |

#### 方法替换
| 当前方法 | 替换为 | 影响范围 |
|---------|-------|---------|
| `GetAxisPosition` | `GetAxisPositionAsync` | 所有使用位置获取的代码 |
| `MoveAxisToPosition` | `MoveAxisToPositionAsync` | 所有轴移动操作 |
| `StopAxis` | `StopAxisAsync` | 所有停止操作 |
| `SetHomeOffset` (Z轴) | `SetHomeOffsetAsync` | Z轴原点设置 |
| `ZHomeStop` (Z轴) | `ZHomeStopAsync` | Z轴紧急停止 |
| `ZTakeForceStop` (Z轴) | `ZTakeForceStopAsync` | Z轴强制停止 |

#### 工厂方法替换
| 当前工厂方法 | 替换为 | 影响范围 |
|------------|-------|---------|
| `GetLxAxisViewModel` | 保留，并添加 `GetLxAxisViewModelAsync` | 相机左X轴获取 |
| `GetLyAxisViewModel` | 保留，并添加 `GetLyAxisViewModelAsync` | 相机左Y轴获取 |
| `GetLzAxisViewModel` | 保留，并添加 `GetLzAxisViewModelAsync` | 相机左Z轴获取 |
| `GetRxAxisViewModel` | 保留，并添加 `GetRxAxisViewModelAsync` | 相机右X轴获取 |
| `GetRyAxisViewModel` | 保留，并添加 `GetRyAxisViewModelAsync` | 相机右Y轴获取 |
| `GetRzAxisViewModel` | 保留，并添加 `GetRzAxisViewModelAsync` | 相机右Z轴获取 |
| `GetZAxisViewModel` | 保留，并添加 `GetZAxisViewModelAsync` | Z轴获取 |

### 9.2 重构中的空实现识别

#### 已识别的空实现
| 类/方法 | 位置 | 问题描述 |
|--------|------|---------|
| `CameralHoldAxisViewModel.RegisterAction` | `CameralHoldAxisViewModel.ICameraAxisViewModel.cs` | 空实现，没有实际注册事件 |
| `CameralHoldAxisViewModel.UnregisterAction` | `CameralHoldAxisViewModel.ICameraAxisViewModel.cs` | 空实现，没有实际注销事件 |
| `ZAxisViewModel.RegisterAction` | `ZAxisViewModel.cs` | 实现不完整，缺少异常处理 |
| `ZAxisViewModel.UnregisterAction` | `ZAxisViewModel.cs` | 实现不完整，缺少异常处理 |
| `GetAxisViewModel(string id)` | `AxisViewModelFactory.cs` | 工厂类中的空实现，导致无法通过ID获取轴 |
| 异常处理 | 多处 | 多处异常捕获后无实际处理，仅记录日志 |

#### 需要补充实现的功能
| 功能 | 当前状态 | 需要补充的内容 |
|-----|---------|--------------|
| 事件注册/注销 | 空实现或不完整 | 完整的事件注册和注销机制，包括异常处理 |
| 资源释放 | 不完整 | 完整的IDisposable实现，确保所有资源正确释放 |
| 异步取消 | 缺失 | 添加对CancellationToken的支持 |
| 进度报告 | 缺失 | 添加对IProgress<T>的支持 |
| 错误恢复 | 缺失 | 添加错误后的恢复机制 |
| 状态管理 | 不完整 | 完整的状态转换和验证 |

### 9.3 接口与实现映射

#### Z轴接口与实现映射
| 接口方法 | 当前实现 | 需要改进 |
|---------|---------|---------|
| `Task<double> GetAxisPositionAsync()` | 同步实现包装为异步 | 改为真正的异步实现 |
| `Task<bool> MoveAxisToPositionAsync(double position)` | 同步实现包装为异步 | 改为真正的异步实现 |
| `Task<bool> StopAxisAsync()` | 同步实现包装为异步 | 改为真正的异步实现 |
| `Task<bool> SetHomeOffsetAsync(double value)` | 同步实现包装为异步 | 改为真正的异步实现 |
| `Task<bool> ZHomeStopAsync()` | 同步实现包装为异步 | 改为真正的异步实现 |
| `Task<bool> ZTakeForceStopAsync()` | 同步实现包装为异步 | 改为真正的异步实现 |

#### 相机轴接口与实现映射
| 接口方法 | 当前实现 | 需要改进 |
|---------|---------|---------|
| `Task<double> GetAxisPositionAsync()` | 同步实现包装为异步 | 改为真正的异步实现 |
| `Task<bool> MoveAxisToPositionAsync(double position)` | 同步实现包装为异步 | 改为真正的异步实现 |
| `Task<bool> StopAxisAsync()` | 同步实现包装为异步 | 改为真正的异步实现 |
| `string CameraPosition { get; }` | 属性实现 | 保持不变 |
| `void RegisterAction(string key)` | 空实现 | 实现完整的事件注册 |
| `void UnregisterAction(string key)` | 空实现 | 实现完整的事件注销 |

### 9.4 单例实现比较

#### 当前单例实现差异
| 轴类型 | 单例实现方式 | 问题 |
|-------|------------|------|
| XYR轴 | 双检锁完整实现 | 无主要问题 |
| Z轴 | 简化单例实现 | 缺少线程安全保证 |
| 相机轴 | 工厂方法创建 | 单例逻辑不完整，可能创建多个实例 |

#### 统一后的单例实现
| 轴类型 | 建议实现方式 | 改进 |
|-------|------------|------|
| PLC轴（Z轴、相机轴） | 双检锁完整实现 | 添加完整的线程安全保证 |
| 串口轴（XYR轴） | 保持现有双检锁实现 | 无需大幅改变 |

### 9.5 异步实现检查清单

- [ ] 所有公开方法都有异步版本
- [ ] 异步方法都接受CancellationToken参数
- [ ] 没有使用async void（除事件处理器外）
- [ ] 没有使用Task.Run包装同步代码
- [ ] 异步方法内没有使用阻塞调用
- [ ] 正确处理异步操作中的异常
- [ ] 提供进度报告机制
- [ ] 支持取消操作
- [ ] 异步方法命名遵循Async后缀约定
- [ ] 避免死锁风险（如使用ConfigureAwait(false)）
- [ ] **没有使用GetAwaiter().GetResult()阻塞调用**

### 9.6 不良异步模式识别

#### GetAwaiter().GetResult()问题

在相机轴实现中发现了大量使用`GetAwaiter().GetResult()`的代码，这种方式存在以下严重问题：

1. **死锁风险**：
   - 在UI线程中调用`GetAwaiter().GetResult()`可能导致死锁
   - 当异步操作尝试回到UI线程而UI线程正在等待结果时，会发生死锁

2. **性能问题**：
   - 阻塞当前线程，浪费线程资源
   - 失去了异步操作的主要优势（非阻塞）

3. **异常处理复杂化**：
   - 异步操作的异常会被包装在AggregateException中
   - 使调试和错误处理变得更加困难

4. **无法取消**：
   - 一旦调用`GetAwaiter().GetResult()`，就无法取消该操作
   - 即使传入了CancellationToken也无效

#### 已识别的不良异步模式

| 文件 | 行号 | 问题代码 | 建议替换方式 |
|------|------|---------|------------|
| `CameralHoldAxisViewModel.cs` | 多处 | `task.GetAwaiter().GetResult()` | 使用`await task`并重构方法为异步 |
| `ZAxisViewModel.cs` | 多处 | `Task.Run(() => {...}).GetAwaiter().GetResult()` | 直接使用异步方法或使用`await Task.Run(() => {...})` |
| `AxisViewModelFactory.cs` | 多处 | `InitializeAsync().GetAwaiter().GetResult()` | 提供异步工厂方法并使用`await InitializeAsync()` |

#### 其他不良异步模式

1. **同步包装异步**：
   ```csharp
   public double GetAxisPosition()
   {
       return GetAxisPositionAsync().GetAwaiter().GetResult();
   }
   ```

2. **异步包装同步**：
   ```csharp
   public async Task<double> GetAxisPositionAsync()
   {
       return await Task.Run(() => GetAxisPosition());
   }
   ```

3. **混合同步和异步代码**：
   ```csharp
   public async Task<bool> MoveAxisToPositionAsync(double position)
   {
       // 同步代码
       var result = DoSomethingSynchronously();
       
       // 异步代码，但使用GetAwaiter().GetResult()阻塞
       var otherResult = DoSomethingAsync().GetAwaiter().GetResult();
       
       // 真正的异步代码
       await DoSomethingElseAsync();
       
       return result && otherResult;
   }
   ```

### 9.7 GetAwaiter().GetResult()整改方案

#### 整改原则

1. **全面消除GetAwaiter().GetResult()**：
   - 识别所有使用此模式的代码
   - 将包含此代码的方法重构为异步方法
   - 替换为正确的await模式

2. **自底向上重构**：
   - 先重构底层方法（如PLC通信方法）
   - 然后重构中间层（如轴控制方法）
   - 最后重构上层调用代码（如UI事件处理）

3. **保持接口兼容**：
   - 为保持向后兼容，可以同时提供同步和异步版本
   - 同步版本应标记为过时(Obsolete)
   - 长期目标是完全移除同步版本

#### 具体整改步骤

1. **识别阶段**：
   - 使用代码分析工具识别所有GetAwaiter().GetResult()调用
   - 创建完整的调用图，了解依赖关系

2. **重构底层方法**：
   - 将PLC通信方法改为真正的异步实现
   - 确保异步方法支持取消和超时

3. **重构中间层**：
   - 将轴控制方法改为异步方法
   - 使用await替代GetAwaiter().GetResult()

4. **重构上层调用**：
   - 将UI事件处理器改为async方法
   - 使用SafeInvoke确保UI更新在正确线程

5. **验证**：
   - 测试所有异步路径
   - 确认没有引入新的死锁或性能问题

#### 代码示例

**不良模式**：
```csharp
public bool MoveAxisToPosition(double position)
{
    return MoveAxisToPositionAsync(position).GetAwaiter().GetResult();
}

public async Task<bool> MoveAxisToPositionAsync(double position)
{
    // 实现
}
```

**改进模式**：
```csharp
[Obsolete("使用MoveAxisToPositionAsync替代")]
public bool MoveAxisToPosition(double position)
{
    // 仅在不得不保留同步方法时使用
    try
    {
        // 使用不同的同步上下文避免死锁
        Task<bool> task = Task.Run(() => MoveAxisToPositionAsync(position));
        return task.GetAwaiter().GetResult();
    }
    catch (Exception ex)
    {
        _loggingService?.LogError(ex, "移动轴位置时发生错误", EventIds.AxisMoveError);
        return false;
    }
}

public async Task<bool> MoveAxisToPositionAsync(double position, CancellationToken cancellationToken = default)
{
    try
    {
        // 真正的异步实现
        await _plcClient.WriteVariableAsync("Position", position, cancellationToken).ConfigureAwait(false);
        return true;
    }
    catch (Exception ex) when (!(ex is OperationCanceledException))
    {
        _loggingService?.LogError(ex, "异步移动轴位置时发生错误", EventIds.AxisMoveError);
        return false;
    }
}
```

## 十、实施优先级

### 10.1 高优先级任务
1. 创建`IPlcAxisViewModel`接口
2. 实现`PlcAxisViewModelBase`基类
3. 修复已识别的空实现
4. **消除所有GetAwaiter().GetResult()调用**
5. 实现完整的异步方法

### 10.2 中优先级任务
1. 重构Z轴实现
2. 重构相机轴实现
3. 更新工厂类
4. 添加异步工厂方法

### 10.3 低优先级任务
1. 添加进度报告机制
2. 优化异步性能
3. 添加高级诊断功能
4. 实现复合轴操作 

## 十一、功能一致性保障

为确保整改后的代码与原始功能完全一致，我们将采取以下措施：

### 11.1 功能映射与验证

1. **功能映射表**：
   - 创建详细的功能映射表，记录每个原始功能及其在新架构中的实现方式
   - 确保所有原始功能都有对应的新实现
   - 特别关注Z轴特殊功能和相机轴位置信息

#### Z轴功能映射表

| 原始功能 | 原始实现 | 新架构实现 | 变化说明 |
|--------|---------|----------|---------|
| 获取轴位置 | `GetAxisPosition()` | `GetAxisPositionAsync(CancellationToken)` | 改为真正异步，添加取消支持 |
| 移动到指定位置 | `MoveAxisToPosition(double)` | `MoveAxisToPositionAsync(double, CancellationToken)` | 改为真正异步，添加取消支持 |
| 停止轴运动 | `StopAxis()` | `StopAxisAsync(CancellationToken)` | 改为真正异步，添加取消支持 |
| 设置原点偏移 | `SetHomeOffset(double)` | `SetHomeOffsetAsync(double, CancellationToken)` | 改为真正异步，添加取消支持 |
| 紧急停止 | `ZHomeStop()` | `ZHomeStopAsync(CancellationToken)` | 改为真正异步，添加取消支持 |
| 强制停止 | `ZTakeForceStop()` | `ZTakeForceStopAsync(CancellationToken)` | 改为真正异步，添加取消支持 |
| 状态监控 | 同步轮询 | 异步事件通知 | 改为基于事件的异步通知机制 |
| 安全限位检查 | 同步检查 | 异步检查+实时监控 | 改为异步检查，增加实时监控 |
| 事件注册 | `RegisterAction(string)` | `RegisterActionAsync(string, CancellationToken)` | 完善实现，增加异步支持 |
| 事件注销 | `UnregisterAction(string)` | `UnregisterActionAsync(string, CancellationToken)` | 完善实现，增加异步支持 |
| 初始化 | 构造函数同步初始化 | `InitializeAsync(CancellationToken)` | 分离初始化逻辑，改为异步 |
| 资源释放 | `IDisposable` 不完整实现 | 完整的 `IAsyncDisposable` 和 `IDisposable` 实现 | 增加异步资源释放，完善同步释放 |
| 错误处理 | 简单日志记录 | 结构化异常处理+恢复机制 | 增加详细错误信息，添加恢复机制 |

#### 相机轴功能映射表

| 原始功能 | 原始实现 | 新架构实现 | 变化说明 |
|--------|---------|----------|---------|
| 获取轴位置 | `GetAxisPosition()` | `GetAxisPositionAsync(CancellationToken)` | 改为真正异步，添加取消支持 |
| 移动到指定位置 | `MoveAxisToPosition(double)` | `MoveAxisToPositionAsync(double, CancellationToken)` | 改为真正异步，添加取消支持 |
| 停止轴运动 | `StopAxis()` | `StopAxisAsync(CancellationToken)` | 改为真正异步，添加取消支持 |
| 获取相机位置信息 | `CameraPosition` 属性 | `CameraPosition` 属性（保持不变） | 保持属性访问方式，内部实现改进 |
| 事件注册 | `RegisterAction(string)` 空实现 | `RegisterActionAsync(string, CancellationToken)` | 完整实现事件注册，支持异步 |
| 事件注销 | `UnregisterAction(string)` 空实现 | `UnregisterActionAsync(string, CancellationToken)` | 完整实现事件注销，支持异步 |
| 初始化 | 构造函数同步初始化 | `InitializeAsync(CancellationToken)` | 分离初始化逻辑，改为异步 |
| 资源释放 | 不完整实现 | 完整的 `IAsyncDisposable` 和 `IDisposable` 实现 | 增加异步资源释放，完善同步释放 |
| 错误处理 | 简单日志记录 | 结构化异常处理+恢复机制 | 增加详细错误信息，添加恢复机制 |
| 相机轴同步移动 | 多个同步调用 | `MoveCameraAxesAsync(positions, CancellationToken)` | 新增协调多轴同步移动的异步方法 |

#### 共享基础功能映射表

| 原始功能 | 原始实现 | 新架构实现 | 变化说明 |
|--------|---------|----------|---------|
| PLC变量读取 | 同步读取 | `ReadVariableAsync<T>(string, CancellationToken)` | 统一到基类，改为真正异步 |
| PLC变量写入 | 同步写入 | `WriteVariableAsync<T>(string, T, CancellationToken)` | 统一到基类，改为真正异步 |
| 连接管理 | 独立实现 | `ConnectAsync(CancellationToken)` / `DisconnectAsync(CancellationToken)` | 统一到基类，改为异步实现 |
| 状态通知 | 不一致实现 | 统一的事件通知机制 | 基于`IObservable<T>`实现一致的通知 |
| 异常处理 | 不一致实现 | 统一的异常处理流程 | 在基类中实现通用异常处理逻辑 |
| 日志记录 | 不一致实现 | 统一使用`ILoggingService` | 统一日志接口，添加结构化事件ID |
| 安全检查 | 部分实现 | 在基类中实现安全检查方法 | 统一安全检查框架，可被特定轴覆盖 |
| 状态监控 | 独立实现 | 在基类中实现统一的状态监控 | 统一异步监控框架 |

#### 工厂方法映射表

| 原始功能 | 原始实现 | 新架构实现 | 变化说明 |
|--------|---------|----------|---------|
| 获取Z轴实例 | `GetZAxisViewModel()` | `GetZAxisViewModel()` + `GetZAxisViewModelAsync(CancellationToken)` | 保留同步方法，添加异步方法 |
| 获取左X相机轴实例 | `GetLxAxisViewModel()` | `GetLxAxisViewModel()` + `GetLxAxisViewModelAsync(CancellationToken)` | 保留同步方法，添加异步方法 |
| 获取左Y相机轴实例 | `GetLyAxisViewModel()` | `GetLyAxisViewModel()` + `GetLyAxisViewModelAsync(CancellationToken)` | 保留同步方法，添加异步方法 |
| 获取左Z相机轴实例 | `GetLzAxisViewModel()` | `GetLzAxisViewModel()` + `GetLzAxisViewModelAsync(CancellationToken)` | 保留同步方法，添加异步方法 |
| 获取右X相机轴实例 | `GetRxAxisViewModel()` | `GetRxAxisViewModel()` + `GetRxAxisViewModelAsync(CancellationToken)` | 保留同步方法，添加异步方法 |
| 获取右Y相机轴实例 | `GetRyAxisViewModel()` | `GetRyAxisViewModel()` + `GetRyAxisViewModelAsync(CancellationToken)` | 保留同步方法，添加异步方法 |
| 获取右Z相机轴实例 | `GetRzAxisViewModel()` | `GetRzAxisViewModel()` + `GetRzAxisViewModelAsync(CancellationToken)` | 保留同步方法，添加异步方法 |
| 通过ID获取轴实例 | `GetAxisViewModel(string)` 空实现 | `GetAxisViewModel(string)` + `GetAxisViewModelAsync(string, CancellationToken)` | 完整实现通过ID查找轴功能 |

#### 接口关系映射

| 原始接口 | 新架构接口 | 关系说明 |
|--------|----------|---------|
| `IAxisViewModel` | `IAxisViewModel` | 保持不变，顶层轴控制接口 |
| `IZAxisViewModel` | `IZAxisViewModel : IPlcAxisViewModel` | 添加继承自新接口 |
| `ICameraAxisViewModel` | `ICameraAxisViewModel : IPlcAxisViewModel` | 添加继承自新接口 |
| 无 | `IPlcAxisViewModel : IAxisViewModel` | 新增，作为PLC控制轴的共同接口 |

#### 进度报告和取消支持映射

| 操作类型 | 原始实现 | 新架构实现 | 变化说明 |
|--------|---------|----------|---------|
| 长时间运动操作 | 无进度报告 | 使用标准`IProgress<T>` | 添加详细进度报告 |
| 取消支持 | 无法取消 | `CancellationToken` 支持 | 所有异步操作支持取消 |
| 状态通知 | 不一致或缺失 | 统一的 `AxisStatusChanged` 事件 | 提供一致的状态变化通知 |
| 错误通知 | 简单异常抛出 | 事件通知 + 结构化错误信息 | 提供详细的错误信息和恢复建议 |

2. **行为规范文档**：
   - 记录每个轴控制操作的预期行为
   - 包括正常流程和异常处理路径
   - 作为测试和验证的基准 

### 11.2 兼容层设计

1. **向后兼容接口**：
   - 保留所有现有公开接口
   - 新接口作为扩展，而不是替换
   - 确保现有客户端代码不需要大量修改

2. **渐进式适配器模式实现**：
   - 创建适配器类包装新实现，保持与旧接口兼容
   - 分阶段进行迁移，而非一次性重构所有代码
   - 实施步骤：
     1. 先完成新接口和基类实现
     2. 创建适配器类连接新旧实现
     3. 对频繁使用和UI阻塞严重的功能优先迁移
     4. 逐步替换调用点，从底层到上层
     5. 完成迁移后再移除适配器和旧实现

3. **特性切换机制**：
   - 实现配置选项，允许在运行时选择新旧实现
   - 按轴类型或功能区域分别启用新实现
   - 在部署过程中可以快速回滚到旧实现
   - 通过日志和监控比较新旧实现的性能和稳定性

4. **渐进式迁移策略**：
   - 允许新旧实现并存
   - 提供明确的迁移路径和时间表
   - 设置过渡期，在此期间同时支持两种方式
   - 分批次迁移客户端代码，优先处理关键功能

### 11.3 功能验证策略

1. **特性对比测试**：
   - 对每个功能进行"旧vs新"的对比测试
   - 验证相同输入产生相同输出
   - 确认异常处理行为一致

2. **边界条件测试**：
   - 测试极端值和边界条件
   - 验证错误处理和恢复机制
   - 确保新实现在边界条件下行为一致

3. **性能基准测试**：
   - 建立性能基准
   - 确保新实现不会导致性能下降
   - 在可能的情况下，量化性能改进

4. **长时间运行测试**：
   - 进行长时间运行测试，验证稳定性
   - 测试资源使用情况（内存、线程等）
   - 确保没有资源泄漏

### 11.4 关键功能保障清单

#### Z轴特殊功能保障
| 功能 | 验证方法 | 成功标准 |
|-----|---------|---------|
| 原点偏移设置 | 功能测试 + 单元测试 | 与原始功能行为一致，支持异步操作 |
| 紧急停止 | 功能测试 + 模拟故障 | 在所有测试场景中正确停止，响应时间不增加 |
| 强制停止 | 功能测试 + 压力测试 | 在高负载下也能正确停止，不产生副作用 |
| 安全限位 | 功能测试 + 边界测试 | 正确处理所有限位情况，不允许超出安全范围 |

#### 相机轴功能保障
| 功能 | 验证方法 | 成功标准 |
|-----|---------|---------|
| 相机位置记录 | 功能测试 | 正确记录和报告左/右相机位置 |
| 轴联动 | 集成测试 | 多轴协同移动时行为一致 |
| 位置精度 | 精度测试 | 与原始实现相同或更高的精度 |
| 状态报告 | 功能测试 | 正确报告轴状态，与原始实现一致 |

### 11.5 回滚计划

为确保在出现问题时能够快速恢复，我们将制定详细的回滚计划：

1. **代码版本控制**：
   - 在整改前创建稳定分支
   - 每个主要改动点创建检查点
   - 确保能够回滚到任何检查点

2. **功能切换机制**：
   - 实现功能开关，可以在运行时切换新旧实现
   - 在生产环境部署前进行A/B测试
   - 允许按轴类型选择性启用新实现

3. **监控与警报**：
   - 添加关键指标监控
   - 设置自动警报阈值
   - 在发现异常时自动通知

4. **应急响应流程**：
   - 制定明确的问题响应流程
   - 定义严重程度级别和对应措施
   - 准备应急修复模板

通过以上措施，我们可以确保整改后的代码完全保持原始功能的一致性，同时提供更好的异步支持、错误处理和可维护性。即使在出现问题时，也能够快速恢复到稳定状态。 

# 最新进度评估（2024年X月X日）

## 一、整改进度概述

根据最新代码分析，Z轴与相机轴整改已完成约80%。核心结构和大部分异步化已实现，接口与基类统一、工厂方法异步化、特殊功能保留等主要目标已基本达成，但仍存在部分历史遗留问题，需进一步完善。

## 二、已完成工作

- 已实现统一PLC轴基类（`PlcAxisViewModelBase`），并覆盖绝大多数异步方法（如`MoveToPositionAsync`、`SetPositionAsync`、`GetCurrentPositionAsync`等），事件、日志、连接管理等基础设施完善。
- 新版Z轴（`ZAxisViewModelNew`）与相机轴（`CameraAxisViewModelNew`）已继承统一基类，保留了Z轴原点偏移、紧急停止、相机位置等特殊功能，结构清晰。
- 工厂类（`AxisViewModelFactory`）已实现大量异步工厂方法和单例缓存，线程安全性良好。
- 事件、日志、依赖注入等配套设施已按整改方案完善。

## 三、未完成/需加强部分

- `GetAwaiter().GetResult()`等阻塞/伪异步模式在工厂类、部分旧实现、测试等仍有大量遗留，未完全消除。
- 伪异步（`Task.Run`包裹同步）在新实现中仍有少量存在，需进一步彻底异步化。
- `IPlcAxisViewModel`接口未见明确定义，接口分层与整改方案略有差异（目前`IZAxisViewModel`、`ICameraAxisViewModel`直接继承自`IAxisViewModel`，建议补充/合并）。
- 旧分部类（如`ZAxisViewModel.IZAxisViewModel.cs`）仍存在，建议彻底合并/移除。
- 进度报告、取消支持等细节功能有待进一步完善。

## 四、后续建议

1. **彻底排查并消除所有`GetAwaiter().GetResult()`和`Task.Run`伪异步用法，所有调用链全异步化。**
2. **补充/合并`IPlcAxisViewModel`接口，完善接口分层，确保与整改方案一致。**
3. **合并/移除所有旧分部类，所有实现集中到新ViewModel。**
4. **完善进度报告、取消支持、异常处理等细节，提升健壮性。**
5. **对所有新实现进行单元/集成/性能/安全测试，确保功能一致性和性能提升。**

---

# 整改推进详细方案

## 一、彻底消除阻塞/伪异步模式
1. **全局排查**：
   - 使用IDE/脚本全局搜索`GetAwaiter().GetResult()`、`Task.Run`等关键词，定位所有阻塞/伪异步用法。
   - 涉及文件：工厂类、测试、旧分部类、部分业务逻辑。
2. **分层整改**：
   - 优先整改底层（如PLC通信、轴控制）为真正异步，逐步向上层推进。
   - 所有调用链全部异步化，必要时重构调用方为`async`。
3. **同步接口处理**：
   - 对于必须保留的同步接口，使用`[Obsolete]`标记，并在内部用`Task.Run`+`await`，避免UI线程死锁。
4. **验收标准**：
   - 代码中不再出现`GetAwaiter().GetResult()`、`Task.Run`包裹同步业务。
   - 所有异步方法命名规范，调用链无阻塞。

## 二、接口分层与IPlcAxisViewModel补充
1. **接口梳理**：
   - 明确`IAxisViewModel`为顶层通用接口，`IPlcAxisViewModel`为所有PLC轴的专用接口。
   - `IZAxisViewModel`、`ICameraAxisViewModel`继承自`IPlcAxisViewModel`。
2. **接口实现**：
   - 在`PlcAxisViewModelBase`实现`IPlcAxisViewModel`，并补充PLC轴通用方法（如PLC变量读写、批量操作等）。
   - 业务代码、工厂、批量操作等统一依赖`IPlcAxisViewModel`。
3. **接口文档与示例**：
   - 补充接口注释和用法示例，便于团队理解和调用。
4. **验收标准**：
   - 代码结构清晰，接口分层合理，所有PLC轴均实现`IPlcAxisViewModel`。

## 三、旧分部类合并与冗余移除
1. **分部类梳理**：
   - 列出所有旧分部类（如`ZAxisViewModel.IZAxisViewModel.cs`、`CameralHoldAxisViewModel.ICameraAxisViewModel.cs`）。
2. **功能迁移**：
   - 将分部类中的有效实现合并到新ViewModel（如`ZAxisViewModelNew`、`CameraAxisViewModelNew`）。
   - 对比功能映射表，确保无功能遗漏。
3. **冗余移除**：
   - 删除所有已无用的分部类、旧接口、旧实现。
4. **验收标准**：
   - 代码中仅保留新ViewModel实现，分部类全部移除，功能无损失。

## 四、进度报告与取消支持完善
1. **进度报告**：
   - 在长时间运行的异步方法中，增加`IProgress<T>`参数，实时上报进度。
   - UI层订阅进度，提升用户体验。
2. **取消支持**：
   - 所有异步方法补充`CancellationToken`参数，支持优雅取消。
   - 关键操作点检查`cancellationToken.ThrowIfCancellationRequested()`。
3. **异常处理**：
   - 统一异步异常处理，日志记录详细，必要时向上抛出。
4. **验收标准**：
   - 所有异步方法均支持进度和取消，UI可感知进度与取消，异常处理一致。

## 五、单元/集成/性能/安全测试
1. **单元测试**：
   - 针对新接口、新实现补充单元测试，覆盖所有核心功能和异常分支。
2. **集成测试**：
   - 验证工厂、批量操作、UI集成等场景，确保调用链无阻塞、无死锁。
3. **性能测试**：
   - 对比整改前后异步操作响应时间，确保无性能退化。
4. **安全测试**：
   - 重点测试Z轴紧急停止、原点偏移等安全机制。
5. **验收标准**：
   - 所有测试通过，功能一致，性能达标，安全机制无遗漏。

## 六、责任分工与推进节奏
1. **责任分工**：
   - 明确每一项整改由专人负责（如接口分层、异步化、分部类合并、测试等）。
2. **推进节奏**：
   - 建议采用"自底向上、分阶段提交"方式，每完成一层整改即提交review，减少合并冲突。
3. **文档同步**：
   - 每完成一项整改，及时更新本整改文档和接口说明。

## 七、验收与回滚机制
1. **阶段性验收**：
   - 每完成一项整改，进行小范围review和功能验证。
2. **最终验收**：
   - 全量测试通过后，由项目负责人统一验收。
3. **回滚机制**：
   - 保留整改前分支，若发现严重问题可随时回退。

---