﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
//using SemiautoParallelWeld.Panels;
//using FileParameter;
//using SemiautoParallelWeld.自定义;
using System.Windows.Forms;
using System.Runtime.InteropServices;
//using SemiautoParallelWeld.自定义.控件;
//using SemiautoParallelWeld.自定义.类;
using System.Xml.Linq;
using System.Diagnostics;
using System.Threading;
//using SemiautoParallelWeld.自定义.WebAPI.Models;
namespace WaferAligner
{

    public enum SoftElemType
    {
        ELEM_QX = 0,     //QX元件
        ELEM_MW = 1,     //MW元件
    }
    class PLC
    {
        #region //标准库
        //[DllImport("StandardModbusApi.dll", EntryPoint = "Init_ETH_String", CallingConvention = CallingConvention.Cdecl)]
        //public static extern bool Init_ETH_String(string sIpAddr, int nNetId = 0, int IpPort = 502);

        //[DllImport("StandardModbusApi.dll", EntryPoint = "Exit_ETH", CallingConvention = CallingConvention.Cdecl)]
        //public static extern bool Exit_ETH(int nNetId = 0);

        //[DllImport("StandardModbusApi.dll", EntryPoint = "H3u_Write_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int H3u_Write_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);

        //[DllImport("StandardModbusApi.dll", EntryPoint = "H3u_Read_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int H3u_Read_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);

        //[DllImport("StandardModbusApi.dll", EntryPoint = "H3u_Read_Soft_Elem_Float", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int H3u_Read_Soft_Elem_Float(SoftElemType eType, int nStartAddr, int nCount, float[] pValue, int nNetId = 0);

        #region //标准库

        [DllImport("StandardModbusApi.dll", EntryPoint = "Init_ETH_String", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool Init_ETH_String(string sIpAddr, int nNetId = 0, int IpPort = 502);
        

        [DllImport("StandardModbusApi.dll", EntryPoint = "Exit_ETH", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool Exit_ETH(int nNetId = 0);

        //[DllImport("StandardModbusApi.dll", EntryPoint = "H3u_Write_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int H3u_Write_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);

        //[DllImport("StandardModbusApi.dll", EntryPoint = "H3u_Read_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int H3u_Read_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);

        //[DllImport("StandardModbusApi.dll", EntryPoint = "H3u_Read_Soft_Elem_Float", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int H3u_Read_Soft_Elem_Float(SoftElemType eType, int nStartAddr, int nCount, float[] pValue, int nNetId = 0);

        //[DllImport("StandardModbusApi.dll", EntryPoint = "H5u_Write_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int H5u_Write_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);

        //[DllImport("StandardModbusApi.dll", EntryPoint = "H5u_Read_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int H5u_Read_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);

        //[DllImport("StandardModbusApi.dll", EntryPoint = "H5u_Read_Soft_Elem_Float", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int H5u_Read_Soft_Elem_Float(SoftElemType eType, int nStartAddr, int nCount, float[] pValue, int nNetId = 0);


        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);

        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);

        #endregion





        /******************************************************************************
         1.功能描述 : 写Am600软元件
         2.返 回 值 :1 成功  0 失败
         3.参    数 : nNetId:网络链接编号
                      eType：软元件类型    ELEM_QX = 0//QX元件  ELEM_MW = 1 //MW元件
                      nStartAddr:软元件起始地址（QX元件由于带小数点，地址需要乘以10去掉小数点，如QX10.1，请输入101，MW元件直接就是它的元件地址不用处理）
                      nCount：软元件个数
                      pValue：数据缓存区
        4.注意事项 :  1.x和y元件地址需为8进制; 
                      2. 当元件位C元件双字寄存器时，每个寄存器需占4个字节的数据
                      3.如果是写位元件，每个位元件的值存储在一个字节中
        ******************************************************************************/
        //[DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int Am600_Write_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_Int16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_Int16(SoftElemType eType, int nStartAddr, int nCount, short[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_Int32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_Int32(SoftElemType eType, int nStartAddr, int nCount, int[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_UInt16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_UInt16(SoftElemType eType, int nStartAddr, int nCount, ushort[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_UInt32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_UInt32(SoftElemType eType, int nStartAddr, int nCount, uint[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_Float", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_Float(SoftElemType eType, int nStartAddr, int nCount, float[] pValue, int nNetId = 0);



        /******************************************************************************
         1.功能描述 : 读Am600软元件
         2.返 回 值 :1 成功  0 失败
         3.参    数 : nNetId:网络链接编号
                      eType：软元件类型   ELEM_QX = 0//QX元件  ELEM_MW = 1 //MW元件
                      nStartAddr:软元件起始地址（QX元件由于带小数点，地址需要乘以10去掉小数点，如QX10.1，请输入101，其它元件不用处理）
                      nCount：软元件个数
                      pValue：返回数据缓存区
         4.注意事项 : 如果是读位元件，每个位元件的值存储在一个字节中，pValue数据缓存区字节数必须是8的整数倍
        ******************************************************************************/
        //[DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int Am600_Read_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_Int16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_Int16(SoftElemType eType, int nStartAddr, int nCount, short[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_Int32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_Int32(SoftElemType eType, int nStartAddr, int nCount, int[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_UInt16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_UInt16(SoftElemType eType, int nStartAddr, int nCount, ushort[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_UInt32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_UInt32(SoftElemType eType, int nStartAddr, int nCount, uint[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_Float", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_Float(SoftElemType eType, int nStartAddr, int nCount, float[] pValue, int nNetId = 0);


        #endregion


        public static bool Open()
        {
            int nNetId = 0;
            int nIpPort = 502;
            XDocument PLCXML = new(new XElement("PLCaddress"));
            PLCXML.Save("PLCXML.xml");
            try
            {
                bool result = Init_ETH_String("************", nNetId, nIpPort);

                //return result;
                return true;

            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message);
                return false;
            }
        }
        public static void ReadRecordingToXMLAsync(string str, string data)
        {
            XDocument plcxml = XDocument.Load("PLCXML.xml");
            XElement root = plcxml.Element("PLCaddress");
            var a = from e in root.Elements() where e.Name.ToString() == str select e;//匹配是否有该PLC地址记录
            if (a.Count() == 0)
            {
                root.Add(new XElement(str, data));//如果没有就新增
            }
            else
            {
                foreach (XElement n in a)
                {
                    n.SetValue(data);
                }
            }
            plcxml.Save("PLCXML.xml");
        }
        public static bool Close()
        {
            int nNetId = 0;
            try
            {
                bool result = Exit_ETH(nNetId);
                if (result == true)
                {
                    //MessageBox.Show("连接关闭");
                    return true;
                }
                else
                { 
                    //MessageBox.Show("关闭连接失败");
                    return false;
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message);
                return false;
            }            
        }

       
       
        /// <summary>
        /// 写PLC
        /// </summary>
        /// <param name="name">PLC寄存器地址</param>
        /// <param name="date">要写入的值</param>

        public static void WriteData(string name, String date)
        {
            string str = name.Trim().Substring(0, 2);//Trim()去掉开头和末尾空格和其它字符.Substring(0, 2)从头开始截取2个字符

            if (str.Equals("MW"))//如果截取的头部字符串是MW
            {
                int nStartAddr = int.Parse(name.Trim().Substring(2, name.Length - 2));//获取地址的数字部分
                int nCount = 2;
                int[] pValue = new int[2];//缓冲区，2个整型
                int nNetId = 0;

                int nValue = int.Parse(date);//将数据转为整数

                pValue[0] = nValue;//赋值给缓存组1
                pValue[1] = nValue;//赋值给缓存组2

                try
                {
                    // 调用api写数据，软元素类型为MW，地址，长度，缓存数组，ID
                    int nRet = Am600_Write_Soft_Elem_Int32(SoftElemType.ELEM_MW, nStartAddr, nCount, pValue, nNetId);
                    if (1 != nRet)
                    {
                        int nRet1 = Am600_Write_Soft_Elem_Int32(SoftElemType.ELEM_MW, nStartAddr, nCount, pValue, nNetId);
                        if (1 != nRet1)
                        {
                            int nRet2 = Am600_Write_Soft_Elem_Int32(SoftElemType.ELEM_MW, nStartAddr, nCount, pValue, nNetId);
                            if (1 != nRet2)
                            {
                                //GlobalVariable.myMessage.Message("写寄存器QX" + nStartAddr + "失败");
                                //GlobalVariable.myMessage.Show();
                                //GlobalVariable.myLog.writeLog("错误地址是" + name + "," + nStartAddr);

                            }

                        }
                    }
                    else
                    {

                        // ReadRecordingToXMLAsync(name, pValue[0].ToString());

                    }
                }
                catch (Exception e)
                {
                    MessageBox.Show(e.Message);
                }
            }
            else if (str.Equals("QX"))
            {
                int nStartAddr = int.Parse(name.Trim().Substring(2, name.Length - 2));
                int nCount = 1;
                byte[] pValue = new byte[1];//缓冲区，长度为1个字节
                int nNetId = 0;

                pValue[0] = byte.Parse(date);
                try
                {
                    // 调用api写数据，软元素类型为QX，地址，长度，缓存数组，ID
                    int nRet = Am600_Write_Soft_Elem(SoftElemType.ELEM_QX, nStartAddr, nCount, pValue, nNetId);
                    if (1 != nRet)
                    {
                        int nRet1 = Am600_Write_Soft_Elem(SoftElemType.ELEM_QX, nStartAddr, nCount, pValue, nNetId);
                        if (1 != nRet1)
                        {
                            int nRet2 = Am600_Write_Soft_Elem(SoftElemType.ELEM_QX, nStartAddr, nCount, pValue, nNetId);
                            if (1 != nRet2)
                            {
                                //GlobalVariable.myMessage.Message("写寄存器QX" + nStartAddr + "失败");
                                //GlobalVariable.myMessage.Show();
                                //GlobalVariable.myLog.writeLog("错误地址是" + name + "," + nStartAddr);

                            }

                        }

                        //MessageBox.Show((""写寄存器QX"+nStartAddr+"失败""));
                    }
                    else
                    {
                        // ReadRecordingToXMLAsync(name, pValue[0].ToString());

                    }
                }
                catch (Exception e)
                {
                    MessageBox.Show(e.Message);
                }
            }
        }
        /// <summary>
        /// 读取PLC寄存器的值
        /// </summary>
        /// <param name="name">寄存器地址</param>
        /// <returns></returns>
        public static string ReadData(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                return "-1";
            }
            string str = name.Trim().Substring(0, 2);

            if (str.Equals("MW"))
            {
                int nStartAddr = int.Parse(name.Trim().Substring(2, name.Length - 2));
                int nCount = 2;
                int[] pValue = new int[2];//缓冲区
                int nNetId = 0;
                try
                {
                    //调用api读数据
                    int nRet = Am600_Read_Soft_Elem_Int32(SoftElemType.ELEM_MW, nStartAddr, nCount, pValue, nNetId);
                    if (1 != nRet)
                    {
                        int nRet1 = Am600_Read_Soft_Elem_Int32(SoftElemType.ELEM_MW, nStartAddr, nCount, pValue, nNetId);
                        if (1 != nRet1)
                        {
                            int nRet2 = Am600_Read_Soft_Elem_Int32(SoftElemType.ELEM_MW, nStartAddr, nCount, pValue, nNetId);
                            if (1 != nRet2)
                            {
                                //GlobalVariable.myMessage.Message("读寄存器MW" + nStartAddr + "失败");
                                //GlobalVariable.myMessage.Show();
                                //GlobalVariable.myLog.writeLog("错误地址是" + name + "," + nStartAddr);
                            }
                        }
                        //MessageBox.Show(("读寄存器MW" + nStartAddr + "失败"));
                    }

                    //RecordPLCDataInSQL(name, pValue[0].ToString());
                    return pValue[0].ToString();
                    // return "0";

                }
                catch (Exception e)
                {
                    MessageBox.Show(e.Message);
                }
            }
            else if (str.Equals("QX"))
            {
                int nStartAddr = int.Parse(name.Trim().Substring(2, name.Length - 2));
                int nCount = 1;
                byte[] pValue = new byte[1];//缓冲区
                int nNetId = 0;
                try
                {
                    //调用api写数据
                    int nRet = Am600_Read_Soft_Elem(SoftElemType.ELEM_QX, nStartAddr, nCount, pValue, nNetId);
                    if (1 != nRet)
                    {
                        int nRet1 = Am600_Read_Soft_Elem(SoftElemType.ELEM_QX, nStartAddr, nCount, pValue, nNetId);
                        if (1 != nRet1)
                        {
                            int nRet2 = Am600_Read_Soft_Elem(SoftElemType.ELEM_QX, nStartAddr, nCount, pValue, nNetId);
                            if (1 != nRet2)
                            {
                                //GlobalVariable.myMessage.Message("读寄存器QX" + nStartAddr + "失败");
                                //GlobalVariable.myMessage.Show();
                                //GlobalVariable.myLog.writeLog("错误地址是" + name + "," + nStartAddr);

                                //MessageBox.Show(("读寄存器QX" + nStartAddr + "失败"));
                            }

                        }
                    }
                    //RecordPLCDataInSQL(name, pValue[0].ToString());
                    return pValue[0].ToString();
                    //return "0";
                }

                catch (Exception e)
                {
                    MessageBox.Show(e.Message);//try{}catch{}在try里面发生的错误不执行try里面的内容，直接跳到catch /e:异常参数
                }
            }
            return "-1";
        }
        /// <summary>
        /// 读取PLC寄存器的值，一次读1个字
        /// </summary>
        /// <param name="name">PLC寄存器地址</param>
        /// <returns></returns>
        public static string ReadData2(string name)//真空度读取专用
        {
            if (string.IsNullOrEmpty(name))
            {
                return "-1";
            }
            string str = name.Trim().Substring(0, 2);

            if (str.Equals("MW"))
            {
                int nStartAddr = int.Parse(name.Trim().Substring(2, name.Length - 2));
                int nCount = 2;
                short[] pValue = new short[2];//缓冲区
                int nNetId = 0;
                try
                {
                    ////调用api读数据
                    int nRet = Am600_Read_Soft_Elem_Int16(SoftElemType.ELEM_MW, nStartAddr, nCount, pValue, nNetId);
                    if (1 != nRet)
                    {
                        int nRet1 = Am600_Read_Soft_Elem_Int16(SoftElemType.ELEM_MW, nStartAddr, nCount, pValue, nNetId);
                        if (1 != nRet1)
                        {
                            int nRet2 = Am600_Read_Soft_Elem_Int16(SoftElemType.ELEM_MW, nStartAddr, nCount, pValue, nNetId);
                            if (1 != nRet2)
                            {
                                //GlobalVariable.myMessage.Message("读寄存器MW" + nStartAddr + "失败");
                                //GlobalVariable.myMessage.Show();
                                ////MessageBox.Show(("读寄存器MW" + nStartAddr + "失败"));
                            }
                        }
                    }
                    //RecordPLCDataInSQL(name, pValue[0].ToString());
                    return pValue[0].ToString();
                    // return "0";
                }
                catch (Exception e)
                {
                    MessageBox.Show(e.Message);
                }
            }
            return "-1";
        }
    }
    class GlobalVariable
    {

        public static string myTfilepath = System.Windows.Forms.Application.StartupPath + "\\SystemFile\\TurnoffInfo.xml";
        public static bool LovenProceduring = false;
        public static bool RovenProceduring = false;
        public static 提示消息 myMessage = new ();
        public static 确认消息 myMessageDR = new ();
        public static bool switchUser = false;
        public static bool changeUser = false;
        //public static ClassLog myLog = new ClassLog();
        public static bool spotWeldCountAlarm = false;
        public static bool sewWeldCountAlarm = false;
        public static string readWaterContent = "01 03 00 00 00 01 84 0A";  //水含量读取命令
        public static string readOxygenContent = "02 03 00 00 00 02 C4 38";//氧含量读取命令
        public static string readHeliumContent1 = "03 03 00 00 00 01 85 E8";//氦含量1读取命令
        public static string readHeliumContent2 = "04 03 00 00 00 01 84 5F";//氦含量2读取命令

        public static string readDiluteGasFeedback = "07 04 00 01 00 02 20 6D";//稀释气体反馈值读取命令
        public static string readHeFeedback = "07 04 00 03 00 02 81 AD";//氦反馈值读取命令
        public static string readDiluteGasConcentration = "07 04 00 05 00 02 61 AC";//稀释气体浓度读取命令
        public static string readHeConcentration = "07 04 00 07 00 02 C0 6C";//氦浓度读取命令
        public static string readDiluteGasPress = "07 04 00 09 00 02 A1 AF";//稀释气体压力读取命令
        public static string readHePress = "07 04 00 0B 00 02 00 6F";//氦压力读取命令
        public static string readGasFlowFeedback = "07 04 00 0D 00 02 E0 6E";//总气体流量反馈值读取命令
        public static string readNitrogenSelectStatus = "07 02 00 00 00 01 B9 AC";//读取N2选择按钮的状态
        public static string readOxygenSelectStatus = "07 02 00 01 00 01 E8 6C";//读取氧气选择按钮状态
        public static string readAirSelectStatus = "07 02 00 02 00 01 18 6C";//读取压缩空气选择按钮状态
        public static string readMannulSwitchStatus = "07 02 00 03 00 01 49 AC";//读取手动开关状态
        public static string readMasterSwitchSattus = "07 02 00 04 00 01 F8 6D";//读取总开关状态

        public static string readSewI1 = "01 03 00 00 00 01 84 0A";//读取I1
        public static string readSewI2 = "01 03 00 01 00 01 D5 CA ";//读取I2
        public static string readSewI3 = "01 03 00 02 00 01 25 CA";//读取I3
        public static string readSewI4 = "01 03 00 03 00 01 74 0A";//读取I4
        public static string readSewN1 = "01 03 00 04 00 01 C5 CB";//读取N1
        public static string readSewN2 = "01 03 00 05 00 01 94 0B";//读取N2
        public static string readSewN3 = "01 03 00 06 00 01 64 0B";//读取N3
        public static string readSewN4 = "01 03 00 07 00 01 35 CB";//读取N4
        public static string readSewTH = "01 03 00 09 00 01 54 08";//读取TH
        public static string readSewTC = "01 03 00 0A 00 01 A4 08";//读取TC
        public static string readSewGP = "01 03 00 24 00 01 C4 01";//读取GP
        public static string readSewFBMP = "01 03 00 27 00 01 34 01";//读取FBMP
        public static string readSewERRC = "01 03 00 28 00 01 04 02";//读取ERRC
        public static string readSewIM1 = "01 03 00 2B 00 01 F4 02";//
        public static string readSewIM2 = "01 03 00 2C 00 01 45 C3";//
        public static string readSewIMC = "01 03 00 2D 00 01 14 03";//
        public static string readSewUM1 = "01 03 00 2E 00 01 E4 03";// 
        public static string readSewUM2 = "01 03 00 2F 00 01 B5 C3";//
        public static string readSewUMC = "01 03 00 30 00 01 84 05";//
        public static string readSewParameters = "01 03 00 00 00 31 84 1E";
        public static string readLastPulseDate = "01 53 00 01 30 09";//查上一次脉冲数据，包括电流和电压


    }
    public enum 指令类型
    {
        抽充抽气 = 0,
        抽充补气 = 1,
        加热 = 2,
        保温 = 3,
        保温抽气 = 4,
        保温补气 = 5,
        停止 = 6,
        抽充 = 7,
        抽真空 = 8,
    }
   
    
    class CreatFalseDate
    {
        public static string TextRandom(int minNum, int maxNum)
        {
            string faledata ;
            Random rd = new();
            faledata = rd.Next(minNum, maxNum).ToString();
            return faledata;
        }
    }
}
