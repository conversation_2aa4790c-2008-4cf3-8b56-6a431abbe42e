using System;
using System.Threading;
using System.Threading.Tasks;

namespace WaferAligner.Interfaces
{
    /// <summary>
    /// 轴控制器ViewModel接口
    /// </summary>
    public interface IAxisViewModel : IDisposable, IAsyncDisposable
    {
        string AxisName { get; }
        bool IsConnected { get; }
        bool IsReady { get; }
        bool IsEnabled { get; }
        bool HasError { get; }
        bool Arrive_position { get; } // 添加到位状态属性
        
        // 位置相关
        Task<bool> MoveToPositionAsync(double position);
        Task<bool> SetPositionAsync(double position);
        Task<double> GetCurrentPositionAsync();
        
        // 运动控制
        Task<bool> HomeAsync();
        Task<bool> StopAsync();
        Task<bool> ResetAsync();
        Task<bool> JogForwardAsync();
        Task<bool> JogBackwardAsync();
        Task<bool> JogStopAsync();
        
        // 速度控制
        Task<bool> SetJogSpeedAsync(double speed);
        Task<bool> SetRunSpeedAsync(double speed);
        
        // 事件通知
        void RegisterAction(string variableName, Action<object> action);
        void UnregisterAction(string variableName);
        
        // 连接管理
        Task<bool> ConnectAsync(string address, int port);
        Task DisconnectAsync();
    }
    
    /// <summary>
    /// XYR轴ViewModel接口
    /// </summary>
    public interface IXyrAxisViewModel : IAxisViewModel
    {
        Task<bool> SetMultiplePositionsAsync(double x, double y, double r);
        int GetRunState(); // 添加获取运行状态方法
        
        // 添加同步方法
        int GetEnableState();
        int GetAlarmState();
        int GetPosition();
        int GetSpeed(); // 增加获取速度方法
        int GetJogSpeed(); // 增加获取点动速度方法
        
        // 添加异步方法
        Task<int> GetEnableStateAsync();
        Task<int> GetAlarmStateAsync();
        Task<int> GetRunStateAsync();
        Task<int> GetCurrentSpeedAsync();
        Task<int> GetJogSpeedAsync(); // 增加异步获取点动速度方法
        
        // 添加初始化轴方法
        Task<bool> InitAxisAsync();
    }
    
    /// <summary>
    /// Z轴ViewModel接口
    /// </summary>
    public interface IZAxisViewModel : IPlcAxisViewModel
    {
        Task<bool> SetHomeOffsetAsync(double value, CancellationToken cancellationToken = default);
        
        // 添加Z轴特有的紧急停止方法，更新为Async后缀
        Task<bool> ZHomeStopAsync(CancellationToken cancellationToken = default);
        Task<bool> ZTakeForceStopAsync(CancellationToken cancellationToken = default);
        Task<bool> WaitSafetyPositionAsync(CancellationToken cancellationToken = default);
        
        // 为向后兼容保留旧方法
        [Obsolete("请使用ZHomeStopAsync")]
        Task<bool> ZHomeStop();
        
        [Obsolete("请使用ZTakeForceStopAsync")]
        Task<bool> ZTakeForceStop();
        
        // 为向后兼容保留的方法，原方法名拼写有误
        [Obsolete("请使用WaitSafetyPositionAsync")]
        Task<bool> WaitSaftyPositionAsync();
    }
    
    /// <summary>
    /// 相机支架轴ViewModel接口
    /// </summary>
    public interface ICameraAxisViewModel : IPlcAxisViewModel
    {
        string CameraPosition { get; } // "Left" or "Right"
        
        // 添加相机轴特有方法
        Task<bool> MoveToWorkPositionAsync(CancellationToken cancellationToken = default);
        Task<bool> MoveToSafePositionAsync(CancellationToken cancellationToken = default);
        Task<bool> MoveToObservePositionAsync(CancellationToken cancellationToken = default);
        Task<bool> SetCameraSpeedAsync(double speed, CancellationToken cancellationToken = default);
        Task<bool> IsAtSafePositionAsync(CancellationToken cancellationToken = default);
    }
} 