﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WaferAligner;
using WaferAligner.Services;

namespace AlignerUI
{
    public partial class MainWindowViewModel
    {
        /// <summary>
        /// Registry monitor variables
        /// </summary>
        void AddRegistry()
        {
            #region Add monitor variable
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.ZTakePhoto", typeof(double));//上拍照位
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.TakeDownWafer", typeof(double));//下拍照位
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.TakeUpWafer", typeof(double));//上吸合位
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.TakeDownWafer1", typeof(double));//下贴片位
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.ZLevel", typeof(double));//调平位


            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_初始化", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_系统标记清除", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_人工确认卡盘OK", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_人工确认上晶圆OK", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_人工确认下晶圆OK", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_当前产品6寸", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_当前产品8寸", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_S_UVW平台标定", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_S_R平台标定", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_SX_目标拍照", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_SX_对象拍照", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_SX_一次对位", typeof(bool));

            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_真空卡盘", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_真空下晶圆", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_真空上晶圆6", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_真空上晶圆8", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_调平锁紧开", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_调平锁紧关", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_Clamb推拉开", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_Clamb推拉关", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_Clamb升", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_Clamb降", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_QD4回零", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_QD5回零", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_拨片推", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.hb_拨片拉", typeof(bool));


            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.JY_6寸厚度", typeof(UInt16));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.JY_6寸MARK距离", typeof(UInt16));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.JY_8寸厚度", typeof(UInt16));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.JY_8寸MARK距离", typeof(UInt16));


            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.初始化step", typeof(UInt16));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.自动step", typeof(UInt16));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.视觉UVW标定step", typeof(UInt16));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.视觉R标定step", typeof(UInt16));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.视觉UVW校准step", typeof(UInt16));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.视觉R校准step", typeof(UInt16));

            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.Alarm_真空卡盘", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.Alarm_真空下晶圆", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.Alarm_真空上晶圆6", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.Alarm_真空上晶圆8", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.Alarm_调平锁紧开", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.Alarm_调平锁紧关", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.Alarm_Clamb推拉开", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.Alarm_Clamb推拉关", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.Alarm_Clamb升", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.Alarm_Clamb降", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.Alarm_Clamb拨片推", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.Alarm_Clamb拨片拉", typeof(bool));

            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.Alarm_QD4回零", typeof(bool));
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.Alarm_QD5回零", typeof(bool));
            #endregion

            #region Add monitor action

            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.ZTakePhoto", (obj) => TopWaferPhotoZ = (double)obj);//上拍照位
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.TakeDownWafer", (obj) => BottomWaferPhotoZ = (double)obj);//下拍照位
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.TakeUpWafer", (obj) => TopWaferTakeUpPos = (double)obj);//上吸合位
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.TakeDownWafer1", (obj) => BottomWaferTakeDownPos = (double)obj);//下贴片位
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.ZLevel", (obj) => TopWaferZLevel = (double)obj);//调平位



            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_手自动", (obj) => IsAuto = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_启动", (obj) => StartExecute = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_复位",
            //    (obj) =>
            //    ResetExecute = (bool)obj
            //    );
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_暂停", (obj) =>
            //PauseExecute = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_停止", (obj) => StopExecute = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_初始化", (obj) => InitAll = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_系统标记清除", (obj) => ClearSystemTag = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_人工确认卡盘OK", (obj) => ChuckReady = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_人工确认上晶圆OK", (obj) => TopWaferReady = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_人工确认下晶圆OK", (obj) => BottomWaferReady = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_当前产品6寸", (obj) => Active6InchEnabled = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_当前产品8寸", (obj) => Active8InchEnabled = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_S_UVW平台标定", (obj) => CalUVW = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_S_R平台标定", (obj) => CalR = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_SX_目标拍照", (obj) => TargetPhotoed = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_SX_对象拍照", (obj) => ObjectedPhotoed = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_SX_一次对位", (obj) => Aligned = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_SX_多次对位", (obj) => MoreAligned = (bool)obj);

            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.UpperWaferState", (obj) => TopWaferState = (UInt16)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.LowerWaferChuckState", (obj) => TrayWaferOuterState = (UInt16)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.LowerChuckState", (obj) => TrayWaferInnerState = (UInt16)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.LowerWaferState", (obj) => TrayState = (UInt16)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.UpperChuckCylinderState", (obj) => ChuckLockState = (UInt16)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.CylinderState", (obj) => HorizontalAdjustState = (UInt16)obj);


            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_真空卡盘", (obj) => EvaChuck = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_真空下晶圆", (obj) => EvaBottomWafer = (bool)obj);
            ////VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_真空上晶圆6", (obj) => EvaTop6 = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_真空上晶圆8", (obj) => EvaTop8 = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_调平锁紧开", (obj) => HorAdjustLocked = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_调平锁紧关", (obj) => HirAdjustUnLocked = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_Clamb推拉开", (obj) => ClampPushed = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_Clamb推拉关", (obj) => ClampUnpushed = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_Clamb升", (obj) => ClampUpMoving = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_Clamb降", (obj) => ClampDownMoving = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_拨片推", (obj) => SpacerPushed = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_拨片拉", (obj) => SpacerPulled = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_QD4回零", (obj) => Qd4Reseted = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.hb_QD5回零", (obj) => Qd5Reseted = (bool)obj);

            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.JY_6寸厚度", (obj) => RealTimeInch6Heigh = (float)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.JY_6寸MARK距离", (obj) => RealTimeInch6MarkDistance = (float)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.JY_8寸厚度", (obj) => RealTimeInch8Heigh = (float)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.JY_8寸MARK距离", (obj) => RealTimeInch8MarkDistance = (float)obj);

            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.初始化step", (obj) =>
            //{
            //    InitStepNumber = Convert.ToInt32(obj);
            //    InitStepName = GetInitSteptDescription(InitStepNumber);
            //});
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.自动step", (obj) =>
            //{
            //    AutoStepNumber = Convert.ToInt32(obj);
            //    AutoStepName = GetCraftDescription(AutoStepNumber);
            //    ManualCheckAction(AutoStepNumber);
            //});
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.视觉UVW标定step", (obj) => UvwCalStepNumber = (Int16)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.视觉R标定step", (obj) => RCalStepNumber = (Int16)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.视觉UVW校准step", (obj) => UvwStepNumber = (Int16)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.视觉R校准step", (obj) => RStepNumber = (Int16)obj);

            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.Alarm_真空卡盘", (obj) => { if ((bool)obj) ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = 1, Message = "控制卡盘真空吸附时发生错误", RecoverySuggesstion = string.Empty }); else DeleteErrorMessage(1); });
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.Alarm_真空下晶圆", (obj) =>
            //{
            //    if ((bool)obj)
            //        ReportErrorMessage(new ErrorInformation
            //        {
            //            Process = ErrorProcess.Add,
            //            ErrorCode = 2,
            //            Message = "控制吸附下晶圆时发生错误",
            //            RecoverySuggesstion = string.Empty
            //        });
            //    else

            //        ReportErrorMessage(new ErrorInformation
            //        {
            //            Process = ErrorProcess.Remove,
            //            ErrorCode = 2,
            //            Message = "控制吸附下晶圆时发生错误",
            //            RecoverySuggesstion = string.Empty
            //        }); ;
            //});
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.Alarm_真空上晶圆6", (obj) =>
            //{
            //    if ((bool)obj)
            //        ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = 3, Message = "控制吸附6寸上晶圆时发生错误", RecoverySuggesstion = string.Empty });
            //    else ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Remove, ErrorCode = 3, Message = "控制吸附6寸上晶圆时发生错误", RecoverySuggesstion = string.Empty });
            //});
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.Alarm_真空上晶圆8", (obj) =>
            //{
            //    if ((bool)obj)
            //        ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = 4, Message = "控制吸附8寸上晶圆时发生错误", RecoverySuggesstion = string.Empty });
            //    else ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Remove, ErrorCode = 4, Message = "控制吸附8寸上晶圆时发生错误", RecoverySuggesstion = string.Empty });
            //});
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.Alarm_调平锁紧开", (obj) =>
            //{
            //    if ((bool)obj)
            //        ReportErrorMessage(new ErrorInformation
            //        {
            //            Process = ErrorProcess.Add,
            //            ErrorCode = 5,
            //            Message = "打开调平锁紧开关时发生错误",
            //            RecoverySuggesstion = string.Empty
            //        });
            //    else
            //        ReportErrorMessage(new ErrorInformation
            //        {
            //            Process = ErrorProcess.Remove,
            //            ErrorCode = 5,
            //            Message = "打开调平锁紧开关时发生错误",
            //            RecoverySuggesstion = string.Empty
            //        });
            //}
            //);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.Alarm_调平锁紧关", (obj) => { if ((bool)obj) ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = 6, Message = "关闭调平锁紧开关时发生错误", RecoverySuggesstion = string.Empty }); else ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Remove, ErrorCode = 6, Message = "关闭调平锁紧开关时发生错误", RecoverySuggesstion = string.Empty }); });
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.Alarm_Clamb推拉开", (obj) => { if ((bool)obj) ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = 7, Message = "推开Clamb时发生错误", RecoverySuggesstion = string.Empty }); else ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Remove, ErrorCode = 7, Message = "推开Clamb时发生错误", RecoverySuggesstion = string.Empty }); });
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.Alarm_Clamb推拉关", (obj) => { if ((bool)obj) ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = 8, Message = "松开Clamb时发生错误", RecoverySuggesstion = string.Empty }); else ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Remove, ErrorCode = 8, Message = "松开Clamb时发生错误", RecoverySuggesstion = string.Empty }); });
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.Alarm_Clamb升", (obj) => { if ((bool)obj) ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = 9, Message = "升起Clamb时发生错误", RecoverySuggesstion = string.Empty }); else ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Remove, ErrorCode = 9, Message = "升起Clamb时发生错误", RecoverySuggesstion = string.Empty }); });
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.Alarm_Clamb降", (obj) =>
            //{
            //    if ((bool)obj) ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = 10, Message = "降下Clamb时发生错误", RecoverySuggesstion = string.Empty });
            //    else ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Remove, ErrorCode = 10, Message = "降下Clamb时发生错误", RecoverySuggesstion = string.Empty });
            //});
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.Alarm_拨片推", (obj) =>
            //{
            //    if ((bool)obj) ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = 11, Message = "拨片推进时发生错误", RecoverySuggesstion = string.Empty });
            //    else ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Remove, ErrorCode = 11, Message = "拨片推进时发生错误", RecoverySuggesstion = string.Empty });
            //});
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.Alarm_拨片拉", (obj) =>
            //{
            //    if ((bool)obj) ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = 12, Message = "拨片拉开时发生错误", RecoverySuggesstion = string.Empty });
            //    else ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Remove, ErrorCode = 12, Message = "拨片拉开时发生错误", RecoverySuggesstion = string.Empty });
            //});
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.Alarm_QD4回零", (obj) =>
            //{
            //    if ((bool)obj) ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = 13, Message = "控制QD4回零时发生错误", RecoverySuggesstion = string.Empty });
            //    else ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Remove, ErrorCode = 13, Message = "控制QD4回零时发生错误", RecoverySuggesstion = string.Empty });
            //});
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.Alarm_QD5回零", (obj) =>
            //{
            //    if ((bool)obj) ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = 14, Message = "控制QD5回零时发生错误", RecoverySuggesstion = string.Empty });
            //    else ReportErrorMessage(new ErrorInformation { Process = ErrorProcess.Remove, ErrorCode = 14, Message = "控制QD5回零时发生错误", RecoverySuggesstion = string.Empty });
            //});



            foreach (var item in VariableChangeActions)
            {
                var list = new ConcurrentBag<Action<object>>();
                list.Add(item.Value);
                ExportActions.TryAdd(item.Key, list);
            }
            #endregion
        }
    }
}
