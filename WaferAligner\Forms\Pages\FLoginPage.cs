using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using JYJ001.App.Service.Usermanagement;
using JYJ001.App.Services.Common.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Sunny.UI;
using WaferAligner.Common;
using WaferAligner.EventIds;
using WaferAligner.Interfaces;
using WaferAligner.Services;
using JYJ001.App.Services.Common.Extension;
using Sunny.UI.Demo;
namespace WaferAligner.Forms.Pages
{
    public partial class FLoginPage : UIForm, INotifyPropertyChanged
    {
        private readonly IUserManagement _userManagement;
        private readonly ILoggingService _loggingService;
        private readonly IUserContext _userContext;
        private readonly IStatusUpdateService _statusUpdateService;
        
        public event PropertyChangedEventHandler? PropertyChanged;

        public FLoginPage(IUserManagement userManagement, ILoggingService loggingService, IUserContext userContext, IStatusUpdateService statusUpdateService)
        {
            InitializeComponent();
            try
            {
                _userManagement = userManagement ?? throw new ArgumentNullException(nameof(userManagement));
                _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
                _userContext = userContext ?? throw new ArgumentNullException(nameof(userContext));
                _statusUpdateService = statusUpdateService ?? throw new ArgumentNullException(nameof(statusUpdateService));
                // 初始化UI
                txtUsername.SelectAll();
                txtUsername.Focus();
                // 绑定回车键登录
                txtUsername.KeyPress += (s, e) => { if (e.KeyChar == 13) { btnLogin_Click(this, EventArgs.Empty); } };
                txtPassword.KeyPress += (s, e) => { if (e.KeyChar == 13) { btnLogin_Click(this, EventArgs.Empty); } };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化登录界面失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Close();
            }
        }

        private void FLoginPage_Load(object sender, EventArgs e)
        {
            // 检查是否有默认用户，如果没有则创建
            EnsureDefaultUser();
            
            
            
            // 记录登录页面加载
            _loggingService.LogInformation("登录页面已加载", WaferAligner.EventIds.EventIds.Login_Page_Loaded);
        }

        private void EnsureDefaultUser()
        {
            // 创建默认管理员用户
            if (_userManagement.GetUserInfo("Admin") == null)
            {
                var defaultAdmin = new UserInfo
                {
                    Username = "Admin",
                    Roles = new[] { "Admin" },
                    Description = "默认系统管理员",
                    IsActive = true
                };

                if (_userManagement.CreateUser(defaultAdmin, "123456"))
                {
                    _loggingService.LogInformation("已创建默认管理员用户: Admin", WaferAligner.EventIds.EventIds.User_Login_Succeeded);
                }
            }

            // 创建默认操作员用户
            if (_userManagement.GetUserInfo("Operator") == null)
            {
                var defaultOperator = new UserInfo
                {
                    Username = "Operator",
                    Roles = new[] { "Operator" },
                    Description = "默认操作员",
                    IsActive = true
                };

                if (_userManagement.CreateUser(defaultOperator, "123456"))
                {
                    _loggingService.LogInformation("已创建默认操作员用户: Operator", WaferAligner.EventIds.EventIds.Default_Operator_Created);
                }
            }
        }

        private async void btnLogin_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text) || string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                ShowError("用户名和密码不能为空");
                return;
            }

            // 禁用登录按钮，防止重复点击
            btnLogin.Enabled = false;
            btnLogin.Text = "登录中...";

            try
            {
                // 验证用户
                bool isAuthenticated = await Task.Run(() => _userManagement.Authenticate(txtUsername.Text, txtPassword.Text));
                if (isAuthenticated)
                {
                    var userInfo = await Task.Run(() => _userManagement.GetUserInfo(txtUsername.Text));
                    if (userInfo != null && userInfo.IsActive)
                    {
                        // 设置用户上下文
                        _userContext.SetCurrentUser(userInfo);
                        
                        // 不再需要设置CurrentUser.User属性，因为我们已经使用_userContext.SetCurrentUser
                        // 不再需要设置UserManagement属性，它应该通过依赖注入获取

                        // 更新最后登录时间
                        userInfo.LastLoginDate = DateTime.Now;
                        await Task.Run(() => _userManagement.UpdateUser(userInfo));

                        _loggingService.LogInformation($"用户 {userInfo.Username} 登录成功", WaferAligner.EventIds.EventIds.User_Login_Succeeded);
                        
                        // 设置对话框结果为成功
                        this.DialogResult = DialogResult.OK;
                        
                        // 通知主窗体登录成功
                        UpdateStatus($"当前用户: {userInfo.Username} - {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                        // 关闭登录页面
                        this.Close();
                    }
                    else
                    {
                        ShowError("用户账户已被禁用");
                        _loggingService.LogWarning($"用户 {txtUsername.Text} 尝试登录但账户被禁用", WaferAligner.EventIds.EventIds.User_Login_Disabled);
                    }
                }
                else
                {
                    ShowError("用户名或密码错误");
                    _loggingService.LogWarning($"用户 {txtUsername.Text} 登录失败", WaferAligner.EventIds.EventIds.User_Login_Failed);
                    
                    // 清空密码框
                    txtPassword.Text = "";
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowError($"登录过程中发生错误: {ex.Message}");
                _loggingService.LogError($"登录过程中发生异常: {ex.Message}", WaferAligner.EventIds.EventIds.Login_Exception);
            }
            finally
            {
                btnLogin.Enabled = true;
                btnLogin.Text = "登录";
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            _loggingService.LogInformation("用户取消登录", WaferAligner.EventIds.EventIds.Login_Cancelled);
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void ShowError(string message)
        {
            UIMessageBox.ShowError(message, false);
        }

        private void ShowSuccess(string message)
        {
            UIMessageBox.ShowSuccess(message, true);
        }

        private string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        // 权限检查方法 - 委托给用户上下文
        public bool HasPermission(string permission)
        {
            return _userContext.HasPermission(permission);
        }

        // 角色检查方法 - 委托给用户上下文
        public bool HasRole(string roleName)
        {
            return _userContext.HasRole(roleName);
        }

        private void UpdateStatus(string status)
        {
            _statusUpdateService?.UpdateStatus(status);
        }

        private void FLoginPage_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 如果用户未登录成功就关闭窗体，则设置取消结果
            if (_userContext.CurrentUser == null && this.DialogResult != DialogResult.OK)
            {
                this.DialogResult = DialogResult.Cancel;
            }
            
            // 记录窗体关闭事件
            if (_loggingService != null)
            {
                _loggingService.LogInformation($"登录窗体关闭，DialogResult: {this.DialogResult}", WaferAligner.EventIds.EventIds.Login_Form_Closing);
            }
        }

        

    }
} 