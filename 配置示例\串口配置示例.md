# 串口配置示例

## 概述

本文档提供了WaferAligner项目中串口通信的各种配置示例，帮助您根据不同的硬件环境进行正确配置。

## 🔧 配置文件位置

**文件**: `WaferAligner/App.config`
**节点**: `<appSettings>`

## 📋 常用配置示例

### 1. **标准配置（默认）**
```xml
<!-- 适用于大多数现代串口设备 -->
<add key="DefaultComPort" value="1" />
<add key="DefaultBaudRate" value="115200" />
<add key="DefaultDataBits" value="8" />
<add key="DefaultStopBits" value="1" />
<add key="DefaultParity" value="None" />
<add key="DefaultHandshake" value="None" />
<add key="SerialTimeout" value="5000" />
<add key="SerialRetryCount" value="3" />
```

### 2. **高兼容性配置**
```xml
<!-- 适用于老旧设备或要求高兼容性的环境 -->
<add key="DefaultComPort" value="1" />
<add key="DefaultBaudRate" value="9600" />
<add key="DefaultDataBits" value="8" />
<add key="DefaultStopBits" value="1" />
<add key="DefaultParity" value="None" />
<add key="DefaultHandshake" value="None" />
<add key="SerialTimeout" value="10000" />
<add key="SerialRetryCount" value="5" />
```

### 3. **高速通信配置**
```xml
<!-- 适用于支持高速通信的现代设备 -->
<add key="DefaultComPort" value="1" />
<add key="DefaultBaudRate" value="230400" />
<add key="DefaultDataBits" value="8" />
<add key="DefaultStopBits" value="1" />
<add key="DefaultParity" value="None" />
<add key="DefaultHandshake" value="RTS" />
<add key="SerialTimeout" value="3000" />
<add key="SerialRetryCount" value="2" />
```

### 4. **带校验的配置**
```xml
<!-- 适用于要求数据完整性的环境 -->
<add key="DefaultComPort" value="1" />
<add key="DefaultBaudRate" value="57600" />
<add key="DefaultDataBits" value="8" />
<add key="DefaultStopBits" value="1" />
<add key="DefaultParity" value="Even" />
<add key="DefaultHandshake" value="None" />
<add key="SerialTimeout" value="5000" />
<add key="SerialRetryCount" value="3" />
```

### 5. **USB转串口配置**
```xml
<!-- 适用于USB转串口适配器 -->
<add key="DefaultComPort" value="3" />
<add key="DefaultBaudRate" value="115200" />
<add key="DefaultDataBits" value="8" />
<add key="DefaultStopBits" value="1" />
<add key="DefaultParity" value="None" />
<add key="DefaultHandshake" value="None" />
<add key="SerialTimeout" value="8000" />
<add key="SerialRetryCount" value="4" />
```

## 🎯 参数详细说明

### **DefaultComPort (串口号)**
- **范围**: 1-256
- **说明**: 对应系统中的COM端口号
- **示例**: 
  - `1` = COM1
  - `3` = COM3
  - `10` = COM10

### **DefaultBaudRate (波特率)**
- **常用值**: 9600, 19200, 38400, 57600, 115200, 230400
- **说明**: 串口通信速率，必须与设备匹配
- **选择建议**:
  - 9600: 最高兼容性，适用于老设备
  - 115200: 标准配置，适用于大多数设备
  - 230400: 高速配置，需要设备支持

### **DefaultDataBits (数据位)**
- **范围**: 5-8
- **常用值**: 8
- **说明**: 每个数据帧的位数

### **DefaultStopBits (停止位)**
- **可选值**: 1, 1.5, 2
- **常用值**: 1
- **说明**: 数据帧结束标志位数

### **DefaultParity (校验位)**
- **可选值**: None, Odd, Even, Mark, Space
- **常用值**: None
- **说明**: 数据完整性校验方式
  - None: 无校验（最常用）
  - Even: 偶校验
  - Odd: 奇校验

### **DefaultHandshake (流控制)**
- **可选值**: None, XOnXOff, RTS, RTSXOnXOff
- **常用值**: None
- **说明**: 数据流控制方式
  - None: 无流控制
  - RTS: 硬件流控制
  - XOnXOff: 软件流控制

### **SerialTimeout (超时时间)**
- **单位**: 毫秒
- **范围**: >0
- **建议值**: 3000-10000
- **说明**: 串口操作超时时间

### **SerialRetryCount (重试次数)**
- **范围**: ≥0
- **建议值**: 2-5
- **说明**: 连接失败时的重试次数

## 🔍 故障排除

### **连接失败**
1. 检查串口号是否正确
2. 确认设备已连接并上电
3. 验证波特率是否匹配
4. 增加超时时间和重试次数

### **数据错误**
1. 检查校验位设置
2. 验证数据位和停止位配置
3. 考虑启用流控制

### **性能问题**
1. 尝试更高的波特率
2. 减少超时时间
3. 启用硬件流控制

## 📝 配置验证

系统启动时会自动验证配置的有效性，如果配置无效会：
1. 记录警告日志
2. 自动使用默认配置
3. 在日志中显示当前使用的配置

## 🚀 最佳实践

1. **从标准配置开始**: 使用默认配置进行初始测试
2. **逐步调整**: 根据实际情况逐个参数调整
3. **记录配置**: 为不同环境保存配置文档
4. **测试验证**: 每次配置更改后进行连接测试
5. **备份配置**: 保存工作正常的配置作为备份
