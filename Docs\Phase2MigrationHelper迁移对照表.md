# Phase2MigrationHelper迁移对照表

| 旧方法/功能                | 新服务/接口                | 新方法/用法说明           |
|---------------------------|----------------------------|---------------------------|
| RegisterAxisEventAsync    | IAxisEventService          | RegisterAxisEventAsync     |
| GetAxisPositionAsync      | IAxisEventService          | GetAxisPositionAsync       |
| GetXYRPositionsAsync      | IAxisEventService          | GetXYRPositionsAsync       |
| SetXYRPositionAsync       | IAxisEventService          | SetXYRPositionAsync        |
| CylinderControlAsync      | IAxisEventService/ConstValueCompatibilityService | CylinderControlAsync |
| ReadPLCVariableSafelyAsync| IPlcVariableService        | ReadVariableSafelyAsync    |
| WritePLCVariableSafelyAsync| IPlcVariableService       | WriteVariableSafelyAsync   |
| CalibrateXYRAsync         | IAxisEventService/ConstValueCompatibilityService | CalibrateXYRAsync   |
| ...                       | ...                        | ...                       |

## 典型调用对照

```csharp
// 旧用法
await _migrationHelper.RegisterAxisEventAsync(...);
// 新用法
await _axisEventService.RegisterAxisEventAsync(...);
```

## 迁移注意事项
- 所有核心功能已迁移至新服务，接口参数和返回值保持一致。
- 兼容性服务仅用于特殊场景，禁止新业务依赖。
- 如需回溯历史实现，请查阅WaferAligner/Migration/Phase2MigrationHelper.cs归档源码。 