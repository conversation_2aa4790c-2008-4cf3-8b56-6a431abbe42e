using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WaferAligner.Interfaces;
using Microsoft.Extensions.Logging;

namespace WaferAligner.Models
{
    /// <summary>
    /// 轴ViewModel集合数据模型
    /// 统一管理所有轴ViewModel实例
    /// </summary>
    public class AxisViewModelCollection : IDisposable
    {
        private bool _disposed = false;

        /// <summary>
        /// XYR轴ViewModels
        /// </summary>
        public IXyrAxisViewModel? XAxis { get; set; }
        public IXyrAxisViewModel? YAxis { get; set; }
        public IXyrAxisViewModel? RAxis { get; set; }

        /// <summary>
        /// Z轴ViewModel
        /// </summary>
        public IZAxisViewModel? ZAxis { get; set; }

        /// <summary>
        /// 左相机轴ViewModels
        /// </summary>
        public ICameraAxisViewModel? LeftXAxis { get; set; }
        public ICameraAxisViewModel? LeftYAxis { get; set; }
        public ICameraAxisViewModel? LeftZAxis { get; set; }

        /// <summary>
        /// 右相机轴ViewModels
        /// </summary>
        public ICameraAxisViewModel? RightXAxis { get; set; }
        public ICameraAxisViewModel? RightYAxis { get; set; }
        public ICameraAxisViewModel? RightZAxis { get; set; }

        /// <summary>
        /// 获取所有轴ViewModel（不为null的）
        /// </summary>
        public IEnumerable<IAxisViewModel> GetAllAxes()
        {
            var axes = new List<IAxisViewModel>();
            
            if (XAxis != null) axes.Add(XAxis);
            if (YAxis != null) axes.Add(YAxis);
            if (RAxis != null) axes.Add(RAxis);
            if (ZAxis != null) axes.Add(ZAxis);
            if (LeftXAxis != null) axes.Add(LeftXAxis);
            if (LeftYAxis != null) axes.Add(LeftYAxis);
            if (LeftZAxis != null) axes.Add(LeftZAxis);
            if (RightXAxis != null) axes.Add(RightXAxis);
            if (RightYAxis != null) axes.Add(RightYAxis);
            if (RightZAxis != null) axes.Add(RightZAxis);

            return axes;
        }

        /// <summary>
        /// 获取所有XYR轴ViewModel
        /// </summary>
        public IEnumerable<IXyrAxisViewModel> GetXYRAxes()
        {
            var axes = new List<IXyrAxisViewModel>();
            
            if (XAxis != null) axes.Add(XAxis);
            if (YAxis != null) axes.Add(YAxis);
            if (RAxis != null) axes.Add(RAxis);

            return axes;
        }

        /// <summary>
        /// 获取所有相机轴ViewModel
        /// </summary>
        public IEnumerable<ICameraAxisViewModel> GetCameraAxes()
        {
            var axes = new List<ICameraAxisViewModel>();
            
            if (LeftXAxis != null) axes.Add(LeftXAxis);
            if (LeftYAxis != null) axes.Add(LeftYAxis);
            if (LeftZAxis != null) axes.Add(LeftZAxis);
            if (RightXAxis != null) axes.Add(RightXAxis);
            if (RightYAxis != null) axes.Add(RightYAxis);
            if (RightZAxis != null) axes.Add(RightZAxis);

            return axes;
        }

        /// <summary>
        /// 验证所有轴是否已初始化
        /// </summary>
        public bool IsFullyInitialized()
        {
            return XAxis != null && YAxis != null && RAxis != null && ZAxis != null &&
                   LeftXAxis != null && LeftYAxis != null && LeftZAxis != null &&
                   RightXAxis != null && RightYAxis != null && RightZAxis != null;
        }

        /// <summary>
        /// 异步清理所有轴资源
        /// </summary>
        public async Task CleanupAsync()
        {
            if (_disposed) return;

            try
            {
                var cleanupTasks = new List<Task>();
                
                foreach (var axis in GetAllAxes())
                {
                    if (axis is IDisposable disposableAxis)
                    {
                        cleanupTasks.Add(Task.Run(() => disposableAxis.Dispose()));
                    }
                }

                if (cleanupTasks.Count > 0)
                {
                    await Task.WhenAll(cleanupTasks);
                }

                // 清空引用
                XAxis = null;
                YAxis = null;
                RAxis = null;
                ZAxis = null;
                LeftXAxis = null;
                LeftYAxis = null;
                LeftZAxis = null;
                RightXAxis = null;
                RightYAxis = null;
                RightZAxis = null;
            }
            catch (Exception ex)
            {
                // 使用ILogger替代Debug.WriteLine
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    // 使用同步方式但不阻塞UI线程
                    // 注意: 在Dispose方法中我们无法使用await，只能使用同步方式
                    // 由于这是在Dispose中，我们需要同步执行清理工作
                    // 我们直接调用各轴的同步方法，而不是异步等待
                    foreach (var axis in GetAllAxes())
                    {
                        try
                        {
                            if (axis is IDisposable disposableAxis)
                            {
                                disposableAxis.Dispose();
                            }
                        }
                        catch { /* 忽略单个轴清理的错误 */ }
                    }
                    
                    // 清空引用
                    XAxis = null;
                    YAxis = null;
                    RAxis = null;
                    ZAxis = null;
                    LeftXAxis = null;
                    LeftYAxis = null;
                    LeftZAxis = null;
                    RightXAxis = null;
                    RightYAxis = null;
                    RightZAxis = null;
                }
                catch { /* 忽略清理过程中的任何错误 */ }
                
                _disposed = true;
            }
        }
    }
} 