using System;
using System.Windows.Forms;
using WaferAligner.Services;
using JYJ001.App.Services.Common.Interfaces;
using JYJ001.App.Services.Common.Extension;
namespace WaferAligner.Services
{
    public class UIUpdateService : IUIUpdateService
    {
        private readonly ILoggingService _loggingService;
        
        public UIUpdateService(ILoggingService loggingService)
        {
            _loggingService = loggingService;
        }
        
        public void SafeUpdateUI(Control control, Action action)
        {
            if (control == null || action == null) return;
            
            try
            {
                // 检查控件状态
                if (control.IsDisposed || control.Disposing) return;
                    
                if (control.InvokeRequired)
                {
                    try
                    {
                        if (!control.IsHandleCreated) return;
                        control.BeginInvoke(action);
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogWarning(ex, "UI线程调用失败", WaferAligner.EventIds.EventIds.UI_Thread_Invoke_Failed);
                    }
                }
                else
                {
                    // 已在UI线程，直接执行
                    action();
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning(ex, "UI更新服务异常", WaferAligner.EventIds.EventIds.UI_Service_Exception);
            }
        }
    }
} 