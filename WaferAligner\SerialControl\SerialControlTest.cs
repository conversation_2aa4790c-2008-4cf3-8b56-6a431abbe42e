using JYJ001.App.Services.Common.Interfaces;
using System;
using System.Threading.Tasks;
using WaferAligner.Common;
using WaferAligner.EventIds;
using WaferAligner.SerialControl.Interfaces;
using WaferAligner.SerialControl.Models;
using JYJ001.App.Services.Common.Extension;
namespace WaferAligner.SerialControl
{
    /// <summary>
    /// 串口控制测试类
    /// </summary>
    public class SerialControlTest
    {
        private readonly ILoggingService _loggingService;
        private readonly ISerialConnectionManager _connectionManager;
        private readonly ISerialAxisControllerFactory _axisControllerFactory;
        private readonly WaferAligner.Common.ResourceManager _resourceManager;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SerialControlTest(
            ILoggingService loggingService,
            ISerialConnectionManager connectionManager,
            ISerialAxisControllerFactory axisControllerFactory,
            WaferAligner.Common.ResourceManager resourceManager)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
            _axisControllerFactory = axisControllerFactory ?? throw new ArgumentNullException(nameof(axisControllerFactory));
            _resourceManager = resourceManager ?? throw new ArgumentNullException(nameof(resourceManager));
        }
        
        /// <summary>
        /// 运行测试
        /// </summary>
        public async Task RunTest()
        {
            try
            {
                _loggingService.LogInformation("开始串口控制测试", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                
                // 初始化连接管理器
                await _connectionManager.InitializeAsync();
                
                // 创建轴控制器
                var xAxisController = await _axisControllerFactory.CreateAxisControllerAsync("X");
                var yAxisController = await _axisControllerFactory.CreateAxisControllerAsync("Y");
                var rAxisController = await _axisControllerFactory.CreateAxisControllerAsync("R");
                
                // 注册资源
                _resourceManager.RegisterResource("XAxisController", xAxisController);
                _resourceManager.RegisterResource("YAxisController", yAxisController);
                _resourceManager.RegisterResource("RAxisController", rAxisController);
                
                // 测试位置获取
                int xPos = await xAxisController.GetPositionAsync();
                int yPos = await yAxisController.GetPositionAsync();
                int rPos = await rAxisController.GetPositionAsync();
                
                _loggingService.LogInformation($"当前位置：X={xPos}, Y={yPos}, R={rPos}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                
                // 测试轴使能
                bool xEnable = await xAxisController.EnableAxisAsync();
                bool yEnable = await yAxisController.EnableAxisAsync();
                bool rEnable = await rAxisController.EnableAxisAsync();
                
                _loggingService.LogInformation($"轴使能结果：X={xEnable}, Y={yEnable}, R={rEnable}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                
                // 测试移动
                bool xMove = await xAxisController.MoveToPositionAsync(1000);
                
                _loggingService.LogInformation($"X轴移动结果：{xMove}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                
                // 等待2秒
                await Task.Delay(2000);
                
                // 测试停止
                bool xStop = await xAxisController.StopAsync();
                
                _loggingService.LogInformation($"X轴停止结果：{xStop}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                
                _loggingService.LogInformation("串口控制测试完成", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "串口控制测试异常", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
            }
        }
    }
} 