﻿namespace Sunny.UI.Demo
{
    partial class FHeaderMainFooter
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            pictureBox1 = new PictureBox();
            btnClearAlarm = new Button();
            BtnAllHome = new Button();
            UiPanelTip = new UIPanel();
            LalSoftwareState = new UIPanel();
            UiPanelTime = new UIPanel();
            uiPanel2 = new UIPanel();
            btnStop = new Button();
            // 移除Timer1，已使用TimerWrapper替代
            // Timer1 = new System.Windows.Forms.Timer(components);
            StyleManager1 = new UIStyleManager(components);
            Footer.SuspendLayout();
            Header.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).BeginInit();
            uiPanel2.SuspendLayout();
            SuspendLayout();
            // 
            // Footer
            // 
            Footer.Controls.Add(uiPanel2);
            Footer.Controls.Add(UiPanelTime);
            Footer.Controls.Add(LalSoftwareState);
            Footer.Controls.Add(UiPanelTip);
            Footer.FillColor = SystemColors.ButtonFace;
            Footer.Location = new Point(0, 980);
            Footer.Size = new Size(1900, 50);
            Footer.StyleCustomMode = true;
            Footer.Text = "";
            // 
            // Header
            // 
            Header.Controls.Add(pictureBox1);
            Header.MenuStyle = UIMenuStyle.Custom;
            Header.SelectedForeColor = Color.FromArgb(140, 140, 140);
            Header.SelectedHighColor = Color.FromArgb(140, 140, 140);
            Header.Size = new Size(1900, 50);
            Header.Style = UIStyle.Custom;
            Header.MenuItemClick += Header_MenuItemClick;
            // 
            // pictureBox1
            // 
            pictureBox1.Image = WaferAligner.Properties.Resources.CETC透明图_彩色字体;
            pictureBox1.Location = new Point(3, -3);
            pictureBox1.Name = "pictureBox1";
            pictureBox1.Size = new Size(118, 51);
            pictureBox1.SizeMode = PictureBoxSizeMode.StretchImage;
            pictureBox1.TabIndex = 0;
            pictureBox1.TabStop = false;
            // 
            // btnClearAlarm
            // 
            btnClearAlarm.BackColor = Color.FromArgb(224, 224, 224);
            btnClearAlarm.Location = new Point(327, 4);
            btnClearAlarm.Name = "btnClearAlarm";
            btnClearAlarm.Size = new Size(130, 38);
            btnClearAlarm.TabIndex = 5;
            btnClearAlarm.Text = "清除报警";
            btnClearAlarm.UseVisualStyleBackColor = false;
            btnClearAlarm.Click += BtnClearAlarm_Click;
            // 
            // BtnAllHome
            // 
            BtnAllHome.BackColor = Color.FromArgb(224, 224, 224);
            BtnAllHome.Location = new Point(173, 4);
            BtnAllHome.Name = "BtnAllHome";
            BtnAllHome.Size = new Size(130, 38);
            BtnAllHome.TabIndex = 4;
            BtnAllHome.Text = "总回原点";
            BtnAllHome.UseVisualStyleBackColor = false;
            BtnAllHome.Click += BtnAllHome_Click;
            // 
            // UiPanelTip
            // 
            UiPanelTip.BackColor = Color.FromArgb(248, 248, 248);
            UiPanelTip.FillColor = Color.FromArgb(248, 248, 248);
            UiPanelTip.FillColor2 = Color.FromArgb(248, 248, 248);
            UiPanelTip.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            UiPanelTip.Location = new Point(0, 2);
            UiPanelTip.Margin = new Padding(4, 5, 4, 5);
            UiPanelTip.MinimumSize = new Size(1, 1);
            UiPanelTip.Name = "UiPanelTip";
            UiPanelTip.RadiusSides = UICornerRadiusSides.None;
            UiPanelTip.RectColor = Color.FromArgb(140, 140, 140);
            UiPanelTip.RectSides = ToolStripStatusLabelBorderSides.Left | ToolStripStatusLabelBorderSides.Top;
            UiPanelTip.Size = new Size(167, 47);
            UiPanelTip.Style = UIStyle.Custom;
            UiPanelTip.TabIndex = 10;
            UiPanelTip.Text = "软件状态:";
            UiPanelTip.TextAlignment = ContentAlignment.MiddleCenter;
            // 
            // LalSoftwareState
            // 
            LalSoftwareState.BackColor = Color.FromArgb(248, 248, 248);
            LalSoftwareState.FillColor = Color.FromArgb(248, 248, 248);
            LalSoftwareState.FillColor2 = Color.FromArgb(248, 248, 248);
            LalSoftwareState.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            LalSoftwareState.Location = new Point(168, 2);
            LalSoftwareState.Margin = new Padding(4, 5, 4, 5);
            LalSoftwareState.MinimumSize = new Size(1, 1);
            LalSoftwareState.Name = "LalSoftwareState";
            LalSoftwareState.RadiusSides = UICornerRadiusSides.None;
            LalSoftwareState.RectColor = Color.FromArgb(140, 140, 140);
            LalSoftwareState.RectSides = ToolStripStatusLabelBorderSides.Left | ToolStripStatusLabelBorderSides.Top;
            LalSoftwareState.Size = new Size(1071, 47);
            LalSoftwareState.Style = UIStyle.Custom;
            LalSoftwareState.TabIndex = 11;
            LalSoftwareState.Text = null;
            LalSoftwareState.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // UiPanelTime
            // 
            UiPanelTime.BackColor = Color.FromArgb(248, 248, 248);
            UiPanelTime.FillColor = Color.FromArgb(248, 248, 248);
            UiPanelTime.FillColor2 = Color.FromArgb(248, 248, 248);
            UiPanelTime.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            UiPanelTime.Location = new Point(1719, 2);
            UiPanelTime.Margin = new Padding(4, 5, 4, 5);
            UiPanelTime.MinimumSize = new Size(1, 1);
            UiPanelTime.Name = "UiPanelTime";
            UiPanelTime.RadiusSides = UICornerRadiusSides.None;
            UiPanelTime.RectColor = Color.FromArgb(140, 140, 140);
            UiPanelTime.RectSides = ToolStripStatusLabelBorderSides.Left | ToolStripStatusLabelBorderSides.Top | ToolStripStatusLabelBorderSides.Right;
            UiPanelTime.Size = new Size(179, 47);
            UiPanelTime.Style = UIStyle.Custom;
            UiPanelTime.TabIndex = 12;
            UiPanelTime.Text = null;
            UiPanelTime.TextAlignment = ContentAlignment.MiddleCenter;
            // 
            // uiPanel2
            // 
            uiPanel2.BackColor = Color.FromArgb(248, 248, 248);
            uiPanel2.Controls.Add(btnStop);
            uiPanel2.Controls.Add(BtnAllHome);
            uiPanel2.Controls.Add(btnClearAlarm);
            uiPanel2.FillColor = Color.FromArgb(248, 248, 248);
            uiPanel2.FillColor2 = Color.FromArgb(248, 248, 248);
            uiPanel2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiPanel2.Location = new Point(1239, 2);
            uiPanel2.Margin = new Padding(4, 5, 4, 5);
            uiPanel2.MinimumSize = new Size(1, 1);
            uiPanel2.Name = "uiPanel2";
            uiPanel2.RadiusSides = UICornerRadiusSides.None;
            uiPanel2.RectColor = Color.FromArgb(140, 140, 140);
            uiPanel2.RectSides = ToolStripStatusLabelBorderSides.Left | ToolStripStatusLabelBorderSides.Top;
            uiPanel2.Size = new Size(479, 47);
            uiPanel2.Style = UIStyle.Custom;
            uiPanel2.TabIndex = 14;
            uiPanel2.Text = null;
            uiPanel2.TextAlignment = ContentAlignment.MiddleCenter;
            // 
            // btnStop
            // 
            btnStop.BackColor = Color.FromArgb(224, 224, 224);
            btnStop.Location = new Point(21, 4);
            btnStop.Name = "btnStop";
            btnStop.Size = new Size(130, 38);
            btnStop.TabIndex = 6;
            btnStop.Text = "停止";
            btnStop.UseVisualStyleBackColor = false;
            btnStop.Click += btnStop_Click;
            // 
            // StyleManager1
            // 
            StyleManager1.GlobalFont = true;
            // 
            // FHeaderMainFooter
            // 
            AutoScaleMode = AutoScaleMode.None;
            BackColor = Color.FromArgb(248, 248, 248);
            ClientSize = new Size(1900, 1030);
            ControlBoxFillHoverColor = Color.FromArgb(163, 163, 163);
            Name = "FHeaderMainFooter";
            RectColor = Color.FromArgb(140, 140, 140);
            Style = UIStyle.Custom;
            Text = "键合对准系统";
            TitleColor = Color.FromArgb(140, 140, 140);
            TitleFont = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            ZoomScaleRect = new Rectangle(15, 15, 1024, 720);
            FormClosing += FHeaderMainFooter_FormClosing;
            Load += FHeaderMainFooter_Load;
            Footer.ResumeLayout(false);
            Header.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)pictureBox1).EndInit();
            uiPanel2.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private PictureBox pictureBox1;
        private Button BtnAllHome;
        private Button btnClearAlarm;
        private UIPanel LalSoftwareState;
        private UIPanel UiPanelTip;
        private UIPanel uiPanel2;
        private UIPanel UiPanelTime;
        // 移除Timer1，已使用TimerWrapper替代
        // private System.Windows.Forms.Timer Timer1;
        private UIStyleManager StyleManager1;
        private Button btnStop;
    }
}