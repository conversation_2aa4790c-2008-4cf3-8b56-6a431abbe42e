namespace WaferAligner.Forms.Pages
{
    partial class FChangePasswordDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.lblCurrentUser = new Sunny.UI.UILabel();
            this.lblOldPassword = new Sunny.UI.UILabel();
            this.txtOldPassword = new Sunny.UI.UITextBox();
            this.lblNewPassword = new Sunny.UI.UILabel();
            this.txtNewPassword = new Sunny.UI.UITextBox();
            this.lblConfirmPassword = new Sunny.UI.UILabel();
            this.txtConfirmPassword = new Sunny.UI.UITextBox();
            this.btnOK = new Sunny.UI.UIButton();
            this.btnCancel = new Sunny.UI.UIButton();
            this.lblPasswordTip = new Sunny.UI.UILabel();
            this.SuspendLayout();
            // 
            // lblCurrentUser
            // 
            this.lblCurrentUser.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Bold);
            this.lblCurrentUser.ForeColor = System.Drawing.Color.DarkBlue;
            this.lblCurrentUser.Location = new System.Drawing.Point(30, 30);
            this.lblCurrentUser.Name = "lblCurrentUser";
            this.lblCurrentUser.Size = new System.Drawing.Size(300, 23);
            this.lblCurrentUser.TabIndex = 0;
            this.lblCurrentUser.Text = "用户：";
            this.lblCurrentUser.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // lblOldPassword
            // 
            this.lblOldPassword.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblOldPassword.Location = new System.Drawing.Point(30, 70);
            this.lblOldPassword.Name = "lblOldPassword";
            this.lblOldPassword.Size = new System.Drawing.Size(80, 23);
            this.lblOldPassword.TabIndex = 1;
            this.lblOldPassword.Text = "当前密码：";
            this.lblOldPassword.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtOldPassword
            // 
            this.txtOldPassword.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtOldPassword.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.txtOldPassword.Location = new System.Drawing.Point(120, 67);
            this.txtOldPassword.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtOldPassword.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtOldPassword.Name = "txtOldPassword";
            this.txtOldPassword.Padding = new System.Windows.Forms.Padding(5);
            this.txtOldPassword.PasswordChar = '*';
            this.txtOldPassword.ShowText = false;
            this.txtOldPassword.Size = new System.Drawing.Size(220, 29);
            this.txtOldPassword.TabIndex = 2;
            this.txtOldPassword.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtOldPassword.Watermark = "请输入当前密码";
            this.txtOldPassword.KeyDown += new System.Windows.Forms.KeyEventHandler(this.txtPassword_KeyDown);
            // 
            // lblNewPassword
            // 
            this.lblNewPassword.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblNewPassword.Location = new System.Drawing.Point(30, 110);
            this.lblNewPassword.Name = "lblNewPassword";
            this.lblNewPassword.Size = new System.Drawing.Size(80, 23);
            this.lblNewPassword.TabIndex = 3;
            this.lblNewPassword.Text = "新密码：";
            this.lblNewPassword.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtNewPassword
            // 
            this.txtNewPassword.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtNewPassword.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.txtNewPassword.Location = new System.Drawing.Point(120, 107);
            this.txtNewPassword.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtNewPassword.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtNewPassword.Name = "txtNewPassword";
            this.txtNewPassword.Padding = new System.Windows.Forms.Padding(5);
            this.txtNewPassword.PasswordChar = '*';
            this.txtNewPassword.ShowText = false;
            this.txtNewPassword.Size = new System.Drawing.Size(220, 29);
            this.txtNewPassword.TabIndex = 4;
            this.txtNewPassword.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtNewPassword.Watermark = "请输入新密码";
            this.txtNewPassword.KeyDown += new System.Windows.Forms.KeyEventHandler(this.txtPassword_KeyDown);
            // 
            // lblConfirmPassword
            // 
            this.lblConfirmPassword.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblConfirmPassword.Location = new System.Drawing.Point(30, 150);
            this.lblConfirmPassword.Name = "lblConfirmPassword";
            this.lblConfirmPassword.Size = new System.Drawing.Size(80, 23);
            this.lblConfirmPassword.TabIndex = 5;
            this.lblConfirmPassword.Text = "确认密码：";
            this.lblConfirmPassword.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtConfirmPassword
            // 
            this.txtConfirmPassword.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtConfirmPassword.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.txtConfirmPassword.Location = new System.Drawing.Point(120, 147);
            this.txtConfirmPassword.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtConfirmPassword.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtConfirmPassword.Name = "txtConfirmPassword";
            this.txtConfirmPassword.Padding = new System.Windows.Forms.Padding(5);
            this.txtConfirmPassword.PasswordChar = '*';
            this.txtConfirmPassword.ShowText = false;
            this.txtConfirmPassword.Size = new System.Drawing.Size(220, 29);
            this.txtConfirmPassword.TabIndex = 6;
            this.txtConfirmPassword.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtConfirmPassword.Watermark = "请再次输入新密码";
            this.txtConfirmPassword.KeyDown += new System.Windows.Forms.KeyEventHandler(this.txtPassword_KeyDown);
            // 
            // btnOK
            // 
            this.btnOK.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnOK.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btnOK.Location = new System.Drawing.Point(180, 210);
            this.btnOK.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(80, 35);
            this.btnOK.TabIndex = 7;
            this.btnOK.Text = "确定";
            this.btnOK.TipsFont = new System.Drawing.Font("微软雅黑", 9F);
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnCancel.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btnCancel.Location = new System.Drawing.Point(280, 210);
            this.btnCancel.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(80, 35);
            this.btnCancel.TabIndex = 8;
            this.btnCancel.Text = "取消";
            this.btnCancel.TipsFont = new System.Drawing.Font("微软雅黑", 9F);
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // lblPasswordTip
            // 
            this.lblPasswordTip.Font = new System.Drawing.Font("微软雅黑", 8F);
            this.lblPasswordTip.ForeColor = System.Drawing.Color.Gray;
            this.lblPasswordTip.Location = new System.Drawing.Point(120, 180);
            this.lblPasswordTip.Name = "lblPasswordTip";
            this.lblPasswordTip.Size = new System.Drawing.Size(220, 25);
            this.lblPasswordTip.TabIndex = 9;
            this.lblPasswordTip.Text = "密码长度6-50位，需包含字母和数字";
            this.lblPasswordTip.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // FChangePasswordDialog
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(10F, 21F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(380, 270);
            this.Controls.Add(this.lblPasswordTip);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.txtConfirmPassword);
            this.Controls.Add(this.lblConfirmPassword);
            this.Controls.Add(this.txtNewPassword);
            this.Controls.Add(this.lblNewPassword);
            this.Controls.Add(this.txtOldPassword);
            this.Controls.Add(this.lblOldPassword);
            this.Controls.Add(this.lblCurrentUser);
            this.Name = "FChangePasswordDialog";
            this.Text = "修改密码";
            this.Load += new System.EventHandler(this.FChangePasswordDialog_Load);
            this.ResumeLayout(false);
        }

        #endregion

        private Sunny.UI.UILabel lblCurrentUser;
        private Sunny.UI.UILabel lblOldPassword;
        private Sunny.UI.UITextBox txtOldPassword;
        private Sunny.UI.UILabel lblNewPassword;
        private Sunny.UI.UITextBox txtNewPassword;
        private Sunny.UI.UILabel lblConfirmPassword;
        private Sunny.UI.UITextBox txtConfirmPassword;
        private Sunny.UI.UIButton btnOK;
        private Sunny.UI.UIButton btnCancel;
        private Sunny.UI.UILabel lblPasswordTip;
    }
} 