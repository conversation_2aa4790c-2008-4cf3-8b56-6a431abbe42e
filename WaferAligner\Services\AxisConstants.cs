namespace WaferAligner.Services
{
    /// <summary>
    /// 轴相关常量定义
    /// </summary>
    public static class AxisConstants
    {
        // 基本转换常量
        public const double AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION = 1000.0;
        public const double AXIS_XY_MULTIPLE_CONVERTION = 10500.0;
        public const double AXIS_R_MULTIPLE_CONVERTION = 17000.0;
        public const string AXIS_GVL = "GVL";
        
        /// <summary>
        /// 轴名称定义
        /// </summary>
        public static class AxisNames
        {
            public const string X_AXIS = "X";
            public const string Y_AXIS = "Y";
            public const string Z_AXIS = "Z";
            public const string R_AXIS = "R";
            public const string LX_AXIS = "LX";
            public const string LY_AXIS = "LY";
            public const string LZ_AXIS = "LZ";
            public const string RX_AXIS = "RX";
            public const string RY_AXIS = "RY";
            public const string RZ_AXIS = "RZ";
        }
        
        /// <summary>
        /// 轴限制常量
        /// </summary>
        public static class AxisLimits
        {
            // 从ConstValue.PosLimit和NegLimit迁移
            public const double X_AXIS_POSITIVE_LIMIT = 10;
            public const double X_AXIS_NEGATIVE_LIMIT = -10;
            public const double Y_AXIS_POSITIVE_LIMIT = 10;
            public const double Y_AXIS_NEGATIVE_LIMIT = -10;
            public const double Z_AXIS_POSITIVE_LIMIT = 10;
            public const double Z_AXIS_NEGATIVE_LIMIT = -10;
            public const double R_AXIS_POSITIVE_LIMIT = 18;
            public const double R_AXIS_NEGATIVE_LIMIT = -1;
            public const double LX_AXIS_POSITIVE_LIMIT = 40;
            public const double LX_AXIS_NEGATIVE_LIMIT = 0;
            public const double LY_AXIS_POSITIVE_LIMIT = 45;
            public const double LY_AXIS_NEGATIVE_LIMIT = 0;
            public const double LZ_AXIS_POSITIVE_LIMIT = 20;
            public const double LZ_AXIS_NEGATIVE_LIMIT = 0;
            public const double RX_AXIS_POSITIVE_LIMIT = 40;
            public const double RX_AXIS_NEGATIVE_LIMIT = 0;
            public const double RY_AXIS_POSITIVE_LIMIT = 45;
            public const double RY_AXIS_NEGATIVE_LIMIT = 0;
            public const double RZ_AXIS_POSITIVE_LIMIT = 20;
            public const double RZ_AXIS_NEGATIVE_LIMIT = 0;
        }
        
        /// <summary>
        /// 轴速度常量
        /// </summary>
        public static class AxisVelocity
        {
            public const double MAXIMUM_VELOCITY = 30;
            public const double MINIMUM_VELOCITY = 0;
        }
    }
} 