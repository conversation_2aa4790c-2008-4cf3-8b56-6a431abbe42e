﻿using System;
using System.Threading;
using System.Threading.Tasks;
using JYJ001.App.Services.Common.Interfaces;
using WaferAligner.Services.PlcCommunication;
using WaferAligner.EventIds;
using Microsoft.Extensions.Logging;
using WaferAligner.Interfaces;

namespace WaferAligner.Models
{
    /// <summary>
    /// PLC轴视图模型的基类，提供PLC轴特有功能
    /// </summary>
    public abstract class PlcAxisViewModelBase : AxisViewModelBase, IPlcAxisViewModel
    {
        protected readonly IPlcCommunication _plcCommunication;
        protected bool _isMoving;
        protected bool _isHomed;
        protected int _currentPosition;
        
        // 事件声明
        public event EventHandler<AxisPositionChangedEventArgs> PositionChanged;
        public event EventHandler<AxisStateChangedEventArgs> StateChanged;
        
        public PlcAxisViewModelBase(
            string axisName,
            IPlcCommunication plcCommunication,
            ILoggingService loggingService) 
            : base(axisName, loggingService)
        {
            _plcCommunication = plcCommunication ?? throw new ArgumentNullException(nameof(plcCommunication));
            _isMoving = false;
            _isHomed = false;
            _currentPosition = 0;
            
            IsConnected = _plcCommunication.IsConnected;
        }
        
        // 实现IPlcAxisViewModel接口方法
        
        public virtual async Task<bool> WritePlcVariableAsync<T>(string variableName, T value, CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
            {
                LogWarning($"{AxisName} 未连接，无法写入变量 {variableName}", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                return await _plcCommunication.WriteVariableAsync(AxisName, variableName, value, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 写入变量 {variableName} 操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 写入变量 {variableName}:{value} 失败", WaferAligner.EventIds.EventIds.Plc_Variables_Write_Failed);
                return false;
            }
        }
        
        public virtual async Task<T> ReadPlcVariableAsync<T>(string variableName, CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
            {
                LogWarning($"{AxisName} 未连接，无法读取变量 {variableName}", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return default;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                return await _plcCommunication.ReadVariableAsync<T>(AxisName, variableName, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 读取变量 {variableName} 操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 读取变量 {variableName} 失败", WaferAligner.EventIds.EventIds.Plc_Variables_Read_Failed);
                return default;
            }
        }
        
        public virtual async Task<bool> RegisterPlcVariableAsync(string variableName, Action<object> action, CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                RegisterAction(variableName, action);
                return true;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 注册变量 {variableName} 操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 注册变量 {variableName} 失败", WaferAligner.EventIds.EventIds.Axis_Event_Registration_Failed);
                return false;
            }
        }
        
        public virtual async Task<bool> UnregisterPlcVariableAsync(string variableName, CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                UnregisterAction(variableName);
                return true;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 注销变量 {variableName} 操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 注销变量 {variableName} 失败", WaferAligner.EventIds.EventIds.Axis_Event_Unregister_Failed);
                return false;
            }
        }
        
        public virtual async Task<bool> SetSafetyCheckEnabledAsync(bool enabled, CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                // 根据PLC特定实现设置安全检查
                await WritePlcVariableAsync($"{AxisName}SafetyCheck", enabled, cancellationToken);
                LogInformation($"{AxisName} 安全检查已{(enabled ? "启用" : "禁用")}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                return true;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 设置安全检查操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 设置安全检查失败", WaferAligner.EventIds.EventIds.Axis_Operation_Error);
                return false;
            }
        }
        
        public virtual async Task<bool> IsHomedAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                if (!IsConnected)
                {
                    return false;
                }
                
                _isHomed = await _plcCommunication.IsHomedAsync(AxisName, cancellationToken);
                return _isHomed;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 检查回原点状态操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 检查回原点状态失败", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
                return false;
            }
        }
        
        public virtual async Task<bool> IsAtPositionAsync(double position, double tolerance = 0.1, CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                if (!IsConnected)
                {
                    return false;
                }
                
                double currentPosition = await GetCurrentPositionAsync();
                return Math.Abs(currentPosition - position) <= tolerance;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 检查位置操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 检查位置失败", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
                return false;
            }
        }
        
        // 更新现有方法，添加取消支持
        
        public virtual async Task<bool> MoveToPositionAsync(double position, CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
            {
                LogWarning($"{AxisName} 未连接，无法移动", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return false;
            }
            
            if (_isMoving)
            {
                LogWarning($"{AxisName} 正在移动中，无法执行新的移动指令", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                OnStateChanged(true, _isHomed, IsConnected);
                // 修复参数顺序：第三个参数是isRelative(false)，第四个参数是cancellationToken
                var result = await _plcCommunication.MoveToPositionAsync(AxisName, (int)position, false, cancellationToken);
                
                if (result)
                {
                    var newPosition = await _plcCommunication.GetPositionAsync(AxisName, cancellationToken);
                    OnPositionChanged(newPosition);
                }
                
                OnStateChanged(false, _isHomed, IsConnected);
                return result;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 移动到位置 {position} 操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                OnStateChanged(false, _isHomed, IsConnected);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 移动失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                OnStateChanged(false, _isHomed, IsConnected);
                return false;
            }
        }
        
        // 保留原始MoveToPositionAsync签名以保持兼容性
        public override Task<bool> MoveToPositionAsync(double position)
        {
            return MoveToPositionAsync(position, CancellationToken.None);
        }
        
        public override async Task<bool> SetPositionAsync(double position)
        {
            // PLC轴通常不支持直接设置位置，而是通过移动来实现
            // 此处简单存储位置值，实际操作由MoveToPositionAsync处理
            try
            {
                LogInformation($"设置{AxisName}轴目标位置为{position}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                return true;
            }
            catch (Exception ex)
            {
                LogError(ex, $"设置{AxisName}轴目标位置失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }
        
        public override async Task<double> GetCurrentPositionAsync()
        {
            if (!IsConnected)
            {
                LogWarning($"{AxisName} 未连接，无法获取当前位置", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return 0;
            }
            
            try
            {
                var position = await _plcCommunication.GetPositionAsync(AxisName, CancellationToken.None);
                _currentPosition = position;
                OnPositionChanged(position);
                return position;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 获取当前位置失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
                return 0;
            }
        }
        
        public override async Task<bool> HomeAsync()
        {
            if (!IsConnected)
            {
                LogWarning($"{AxisName} 未连接，无法回零", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return false;
            }
            
            if (_isMoving)
            {
                LogWarning($"{AxisName} 正在移动中，无法执行回零", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                return false;
            }
            
            try
            {
                OnStateChanged(true, _isHomed, IsConnected);
                var result = await _plcCommunication.HomeAsync(AxisName, CancellationToken.None);
                
                if (result)
                {
                    var newPosition = await _plcCommunication.GetPositionAsync(AxisName, CancellationToken.None);
                    OnPositionChanged(newPosition);
                }
                
                OnStateChanged(false, await _plcCommunication.IsHomedAsync(AxisName, CancellationToken.None), IsConnected);
                return result;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 回零失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                OnStateChanged(false, _isHomed, IsConnected);
                return false;
            }
        }
        
        public override async Task<bool> StopAsync()
        {
            return await StopAsync(CancellationToken.None);
        }
        
        public virtual async Task<bool> StopAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
            {
                LogWarning($"{AxisName} 未连接，无法停止", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var result = await _plcCommunication.StopAsync(AxisName, cancellationToken);
                OnStateChanged(false, _isHomed, IsConnected);
                return result;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 停止操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 停止失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Stop_Error);
                return false;
            }
        }
        
        public override async Task<bool> ResetAsync()
        {
            return await ResetAsync(CancellationToken.None);
        }
        
        public virtual async Task<bool> ResetAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
            {
                LogWarning($"{AxisName} 未连接，无法复位", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var result = await _plcCommunication.ResetAsync(AxisName, cancellationToken);
                HasError = !result;
                return result;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 复位操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 复位失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Reset_Timeout);
                return false;
            }
        }
        
        // 点动功能实现为相对移动
        public override async Task<bool> JogForwardAsync()
        {
            return await JogForwardAsync(CancellationToken.None);
        }
        
        public virtual async Task<bool> JogForwardAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
            {
                LogWarning($"{AxisName} 未连接，无法点动", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                const int jogDistance = 100; // 点动距离
                OnStateChanged(true, _isHomed, IsConnected);
                var result = await _plcCommunication.MoveToPositionAsync(AxisName, jogDistance, true, cancellationToken);
                return result;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 正向点动操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                OnStateChanged(false, _isHomed, IsConnected);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 正向点动失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                OnStateChanged(false, _isHomed, IsConnected);
                return false;
            }
        }
        
        public override async Task<bool> JogBackwardAsync()
        {
            return await JogBackwardAsync(CancellationToken.None);
        }
        
        public virtual async Task<bool> JogBackwardAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
            {
                LogWarning($"{AxisName} 未连接，无法点动", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                const int jogDistance = -100; // 点动距离
                OnStateChanged(true, _isHomed, IsConnected);
                var result = await _plcCommunication.MoveToPositionAsync(AxisName, jogDistance, true, cancellationToken);
                return result;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 反向点动操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                OnStateChanged(false, _isHomed, IsConnected);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 反向点动失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                OnStateChanged(false, _isHomed, IsConnected);
                return false;
            }
        }
        
        public override async Task<bool> JogStopAsync()
        {
            return await JogStopAsync(CancellationToken.None);
        }
        
        public virtual async Task<bool> JogStopAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
            {
                LogWarning($"{AxisName} 未连接，无法停止点动", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                OnStateChanged(false, _isHomed, IsConnected);
                var result = await _plcCommunication.StopAsync(AxisName, cancellationToken);
                return result;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 停止点动操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 停止点动失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Stop_Error);
                return false;
            }
        }
        
        public override async Task<bool> SetJogSpeedAsync(double speed)
        {
            return await SetJogSpeedAsync(speed, CancellationToken.None);
        }
        
        public virtual async Task<bool> SetJogSpeedAsync(double speed, CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
            {
                LogWarning($"{AxisName} 未连接，无法设置点动速度", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var result = await _plcCommunication.SetJogSpeedAsync(AxisName, (int)speed, cancellationToken);
                return result;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 设置点动速度操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 设置点动速度失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Speed_Set_Failed);
                return false;
            }
        }
        
        /// <summary>
        /// 设置轴运行速度（旧方法，为向后兼容保留）
        /// </summary>
        /// <param name="speed">速度值</param>
        /// <returns>操作是否成功</returns>
        [Obsolete("请使用带取消令牌的SetRunSpeedAsync(double, CancellationToken)方法")]
        public override async Task<bool> SetRunSpeedAsync(double speed)
        {
            try
            {
                return await SetRunSpeedAsync(speed, CancellationToken.None);
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 设置运行速度失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Speed_Set_Failed);
                return false;
            }
        }
        
        public virtual async Task<bool> SetRunSpeedAsync(double speed, CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
            {
                LogWarning($"{AxisName} 未连接，无法设置运行速度", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return false;
            }
            
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var result = await _plcCommunication.SetRunSpeedAsync(AxisName, (int)speed, cancellationToken);
                return result;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{AxisName} 设置运行速度操作被取消", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName} 设置运行速度失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Speed_Set_Failed);
                return false;
            }
        }
        
        // PLC轴通常使用系统级变量，此处简单实现变量订阅
        private Action<object> _positionAction;
        private Action<object> _stateAction;
        
        public override void RegisterAction(string variableName, Action<object> action)
        {
            try
            {
                switch (variableName.ToLower())
                {
                    case "position":
                        _positionAction = action;
                        LogInformation($"{AxisName}轴位置变量注册动作", WaferAligner.EventIds.EventIds.Resource_Registered);
                        break;
                    case "state":
                        _stateAction = action;
                        LogInformation($"{AxisName}轴状态变量注册动作", WaferAligner.EventIds.EventIds.Resource_Registered);
                        break;
                    default:
                        LogWarning($"{AxisName}轴尝试注册未知变量: {variableName}", WaferAligner.EventIds.EventIds.Resource_Registered);
                        break;
                }
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName}轴注册变量动作失败: {variableName}", WaferAligner.EventIds.EventIds.Axis_Event_Registration_Failed);
            }
        }
        
        public override void UnregisterAction(string variableName)
        {
            try
            {
                switch (variableName.ToLower())
                {
                    case "position":
                        _positionAction = null;
                        LogInformation($"{AxisName}轴位置变量注销动作", WaferAligner.EventIds.EventIds.Resource_Released);
                        break;
                    case "state":
                        _stateAction = null;
                        LogInformation($"{AxisName}轴状态变量注销动作", WaferAligner.EventIds.EventIds.Resource_Released);
                        break;
                    default:
                        LogWarning($"{AxisName}轴尝试注销未知变量: {variableName}", WaferAligner.EventIds.EventIds.Resource_Released);
                        break;
                }
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName}轴注销变量动作失败: {variableName}", WaferAligner.EventIds.EventIds.Axis_Event_Unregister_Failed);
            }
        }
        
        public override async Task<bool> ConnectAsync(string address, int port)
        {
            // PLC连接通常由PlcConnectionManager统一管理，此处仅更新状态
            IsConnected = _plcCommunication.IsConnected;
            OnStateChanged(_isMoving, _isHomed, IsConnected);
            return IsConnected;
        }
        
        public override async Task DisconnectAsync()
        {
            // PLC断开连接通常由PlcConnectionManager统一管理，此处仅更新状态
            IsConnected = false;
            OnStateChanged(false, false, false);
            await Task.CompletedTask;
        }
        
        public override async Task InitializeAsync()
        {
            await base.InitializeAsync();
            
            IsConnected = _plcCommunication.IsConnected;
            if (IsConnected)
            {
                try
                {
                    var position = await _plcCommunication.GetPositionAsync(AxisName, CancellationToken.None);
                    OnPositionChanged(position);
                    
                    // 尝试获取轴状态
                    var isHomed = await _plcCommunication.IsHomedAsync(AxisName, CancellationToken.None);
                    var isEnabled = await _plcCommunication.IsEnabledAsync(AxisName, CancellationToken.None);
                    var hasError = await _plcCommunication.HasErrorAsync(AxisName, CancellationToken.None);
                    
                    _isHomed = isHomed;
                    IsEnabled = isEnabled;
                    HasError = hasError;
                    IsReady = isEnabled && !hasError;
                    
                    OnStateChanged(false, isHomed, IsConnected);
                }
                catch (Exception ex)
                {
                    LogError(ex, $"{AxisName}轴初始化状态查询失败", WaferAligner.EventIds.EventIds.Axis_Initialize_Failed);
                }
            }
            
            // 设置基本状态
            OnStateChanged(false, _isHomed, IsConnected);
        }
        
        // 事件触发器
        protected virtual void OnPositionChanged(int position)
        {
            _currentPosition = position;
            PositionChanged?.Invoke(this, new AxisPositionChangedEventArgs(AxisName, position));
            _positionAction?.Invoke(position);
        }
        
        protected virtual void OnStateChanged(bool isMoving, bool isHomed, bool isConnected)
        {
            _isMoving = isMoving;
            _isHomed = isHomed;
            IsConnected = isConnected;
            Arrive_position = !isMoving;
            
            StateChanged?.Invoke(this, new AxisStateChangedEventArgs(AxisName, isMoving, isHomed, isConnected));
            
            // 封装状态为对象
            var stateObj = new { 
                IsMoving = isMoving, 
                IsHomed = isHomed, 
                IsConnected = isConnected, 
                IsReady = IsReady,
                IsEnabled = IsEnabled,
                HasError = HasError
            };
            _stateAction?.Invoke(stateObj);
        }
    }
}
