﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static Sunny.UI.UILine;

namespace WaferAligner
{
    public partial class ULine : UserControl
    {
        public ULine()
        {
            InitializeComponent();
        }
        [Category("自定义属性")]
        [Description("文本信息")]
        public string 文本
        {
            get { return lalTextName.Text; }
            set { lalTextName.Text = value; }
        }
        public void SetText(string Text)
        {
            lalTextName.Text = Text;
        }
        public string GetText()
        {
            return lalTextName.Text;
        }


    }
}
