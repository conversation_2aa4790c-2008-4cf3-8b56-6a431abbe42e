﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace WaferAligner
{
    class XmlOperation
    {
        public void CreateXMLTree()
        {
            xmlDoc = new XmlDocument();
            //创建类型声明节点
            XmlNode node = xmlDoc.CreateXmlDeclaration("1.0", "utf-8", "");
            xmlDoc.AppendChild(node);
            //创建根节点
            root = xmlDoc.CreateElement("参数");
            xmlDoc.AppendChild(root);
        }

        XmlDocument xmlDoc = new();
        XmlNode root = null;

        public void CreateXMLTreeAndSave(string strPath)
        {
            xmlDoc = new();
            //创建类型声明节点
            XmlNode node = xmlDoc.CreateXmlDeclaration("1.0", "utf-8", "");
            xmlDoc.AppendChild(node);
            //创建根节点
            root = xmlDoc.CreateElement("参数");
            xmlDoc.AppendChild(root);
            //xmlDoc.Save(strPath);
        }

        public void Create(string xmlPath, string name, string value)
        {
            XmlNode node = xmlDoc.CreateNode(XmlNodeType.Element, name, null);
            node.InnerText = value.Trim();
            root.AppendChild(node);
            //添加为根元素的第一层子结点
            //xmlDoc.Save(xmlPath);
        }

        public void SaveXML(string xmlPath)
        {
            xmlDoc.Save(xmlPath);
        }


        //属性
        public static void CreateAttribute(string xmlPath)
        {
            XmlDocument xmlDoc = new();
            xmlDoc.Load(xmlPath);
            XmlElement node = (XmlElement)xmlDoc.SelectSingleNode("Collection/Book");
            node.SetAttribute("Name", "C#");
            xmlDoc.Save(xmlPath);
        }

        public static void Delete(string xmlPath)
        {
            XmlDocument xmlDoc = new();
            xmlDoc.Load(xmlPath);
            var root = xmlDoc.DocumentElement;//取到根结点

            var element = xmlDoc.SelectSingleNode("Collection/Name");
            root.RemoveChild(element);
            xmlDoc.Save(xmlPath);
        }

        public void DeleteAttribute(string xmlPath)
        {
            XmlDocument xmlDoc = new();
            xmlDoc.Load(xmlPath);
            XmlElement node = (XmlElement)xmlDoc.SelectSingleNode("Collection/Book");
            //移除指定属性
            node.RemoveAttribute("Name");
            //移除当前节点所有属性，不包括默认属性
            node.RemoveAllAttributes();

            xmlDoc.Save(xmlPath);
        }

        public static void Modify(string xmlPath)
        {
            XmlDocument xmlDoc = new();
            xmlDoc.Load(xmlPath);
            var root = xmlDoc.DocumentElement;//取到根结点
            XmlNodeList nodeList = xmlDoc.SelectNodes("/Collection/Book");
            //xml不能直接更改结点名称，只能复制然后替换，再删除原来的结点
            foreach (XmlNode node in nodeList)
            {
                var xmlNode = (XmlElement)node;
                xmlNode.SetAttribute("ISBN", "Zery");
            }
            xmlDoc.Save(xmlPath);
        }

        public void ModifyAttribute(string xmlPath)
        {
            XmlDocument xmlDoc = new();
            xmlDoc.Load(xmlPath);
            XmlElement element = (XmlElement)xmlDoc.SelectSingleNode("Collection/Book");
            element?.SetAttribute("Name", "Zhang");
            xmlDoc.Save(xmlPath);
        }

        public void LoadXML(string xmlPath)
        {
            xmlDoc = new(); //创建一个xml对象
            xmlDoc.Load(xmlPath); //将输入路径的xml文件加载xml对象中
        }

        /// <summary>
        /// 返回输入路径xmlPath的xml文件的对应输入名称name节点中的内容
        /// </summary>
        /// <param name="xmlPath"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        public string Select(string xmlPath, string name)
        {
            //XmlDocument xmlDoc = new XmlDocument(); //创建一个xml对象
            //xmlDoc.Load(xmlPath); //将输入路径的xml文件加载xml对象中
            ////取根结点
            //var root = xmlDoc.DocumentElement;//取到根结点
            //取指定的单个结点
            XmlNode singleNode = xmlDoc.SelectSingleNode("参数/" + name);  //获得输入名称对应的节点

            return singleNode.InnerText; //返回输入路径的xml文件的对应名称节点中的内容
        }

        public static void SelectAttribute(string xmlPath)
        {
            XmlDocument xmlDoc = new();
            xmlDoc.Load(xmlPath);
            XmlElement element = (XmlElement)xmlDoc.SelectSingleNode("Collection/Book");
            string name = element.GetAttribute("Name");
        }
    }
}
