using System;
using System.Threading.Tasks;
using AlignerUI;

namespace WaferAligner.Services
{
    /// <summary>
    /// 配方服务接口
    /// 负责管理对准机参数和配方相关功能
    /// </summary>
    public interface IRecipeService
    {
        // 配方基本信息
        string ProductName { get; set; }
        string ProductSize { get; set; }
        double SpacerThick { get; set; }
        double MarkDistance { get; set; }
        WaferSeries Material { get; set; }
        int VisualNumber { get; set; }

        // 上晶圆拍照相关参数
        double TopWaferPhotoLX { get; set; }
        double TopWaferPhotoLY { get; set; }
        double TopWaferPhotoLZ { get; set; }
        double TopWaferPhotoRX { get; set; }
        double TopWaferPhotoRY { get; set; }
        double TopWaferPhotoRZ { get; set; }
        double TopWaferPhotoZ { get; set; }
        double TopWaferPhotoX { get; set; }
        double TopWaferPhotoY { get; set; }
        double TopWaferPhotoR { get; set; }
        bool IsTopHorizontalAdjust { get; set; }
        bool IsTopHorizontalPhoto { get; set; }
        double TopWaferThick { get; set; }

        // 下晶圆拍照相关参数
        double BottomWaferPhotoZ { get; set; }
        double BottomWaferPhotoLX { get; set; }
        double BottomWaferPhotoLY { get; set; }
        double BottomWaferPhotoLZ { get; set; }
        double BottomWaferPhotoRX { get; set; }
        double BottomWaferPhotoRY { get; set; }
        double BottomWaferPhotoRZ { get; set; }
        double BottomWaferPhotoX { get; set; }
        double BottomWaferPhotoY { get; set; }
        double BottomWaferPhotoR { get; set; }
        double BottomWaferThick { get; set; }
        double BottomWaferPhotoFitZ { get; set; }

        // 配方操作
        Task<bool> SaveRecipeAsync(string fileName = null);
        Task<bool> LoadRecipeAsync(string fileName = null);
        Task<string[]> GetAllRecipeNamesAsync();
        Task<bool> DeleteRecipeAsync(string recipeName);
        
        // 设置参数到PLC
        Task UpdatePlcParametersAsync();
    }
} 