﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WaferAligner
{
    public partial class 确认消息 : Form
    {
        public 确认消息()
        {
            InitializeComponent();
        }
        DialogResult _结果 = DialogResult.No;//实例化一个对话框返回值，暂时赋予NO
        public void 信息(string message)
        {
            label1.Text = message;
            this.ShowDialog();
        }

        public DialogResult 结果()
        {
            return _结果;
        }

        private void button1_Click(object sender, EventArgs e)
        {
              _结果 = DialogResult.Yes;
              this.Close();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            _结果 = DialogResult.No;
            this.Close();
        }
    }
}
