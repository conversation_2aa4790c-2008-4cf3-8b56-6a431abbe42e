﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
//using SemiautoParallelWeld.自定义.类;

namespace WaferAligner
{
    public partial class 电磁阀 : InstrumnetControl
    {
        private string _执行;
        private string _传感器;

        public 电磁阀()
        {
            InitializeComponent();
            _执行 = string.Empty;
            _传感器 = string.Empty;

        }
        public override void UpdateState()
        {
            pictureBox1.BackColor = PLC.ReadData(_传感器).Equals("1") ? Color.Green : Color.Gray;
            button1.BackColor = PLC.ReadData(_传感器).Equals("1") ? Color.Red : Color.Transparent;
        }

        [Browsable(true)]
        public string 名称
        {
            get { return this.label1.Text; }
            set { this.label1.Text = value; }
        }
        [Browsable(true)]
        public string 执行
        {
            get { return _执行; }
            set { _执行 = value; }
        }
        [Browsable(true)]
        public string 传感器
        {
            get { return _传感器; }
            set { _传感器 = value; }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (PLC.ReadData(_执行) == "0")
            {
                PLC.WriteData(_执行, "1");
                PublicFunc.Delay(100);
                if (PLC.ReadData(_执行).Equals("1"))
                {
                    button1.Text = "关闭";
                    button1.BackColor = Color.Red;
                }
                else
                {

                    GlobalVariable.myMessage.Message("写入PLC失败");
                    GlobalVariable.myMessage.ShowDialog();
                }
            }
            else
            {
                PLC.WriteData(_执行, "0");
                PublicFunc.Delay(100);
                if (PLC.ReadData(_执行).Equals("0"))
                {
                    button1.Text = "执行";
                    button1.BackColor = Color.Transparent;
                }
                else
                {
                    GlobalVariable.myMessage.Message("写入PLC失败");
                    GlobalVariable.myMessage.ShowDialog();
                }
            }
        }
    }
}
