using System;
using System.Threading.Tasks;

namespace WaferAligner.Interfaces
{
    /// <summary>
    /// 主窗体ViewModel接口
    /// </summary>
    public interface IMainWindowViewModel
    {
        // 系统状态
        bool IsSystemReady { get; }
        bool IsConnected { get; }
        string SystemStatus { get; }
        
        // 气缸状态属性
        UInt16 TopWaferState { get; set; }
        UInt16 TrayWaferOuterState { get; set; }
        UInt16 TrayWaferInnerState { get; set; }
        UInt16 TrayState { get; set; }
        UInt16 ChuckLockState { get; set; }
        UInt16 HorizontalAdjustState { get; set; }
        
        // 位置参数属性
        double TopWaferPhotoZ { get; set; }
        double BottomWaferPhotoZ { get; set; }
        double TopWaferTakeUpPos { get; set; }
        double BottomWaferTakeDownPos { get; set; }
        double TopWaferZLevel { get; set; }
        
        // PLC相关
        Task<bool> ConnectPLCAsync();
        Task DisconnectPLCAsync();
        Task<bool> WritePLCVariableAsync(string variableName, object value);
        Task<object> ReadPLCVariableAsync(string variableName, Type expectedType);
        
        // 系统参数
        Task<bool> SystemParameterExecuteAsync(double cameraOffset, double bottomPhotoOffset, double topGap, double bottomGap);
        Task<bool> SystemPareExcute(double cameraOffset, double bottomPhoto, double TopGap, double BottomGap);
        Task<bool> SystemPareExcuteAsync(double cameraOffset, double bottomPhoto, double TopGap, double BottomGap);
        Task<bool> RecipePareExcuteAsync();
        
        // 系统参数独立配置方法
        Task<bool> TopGapSystemParaAsync(double value);
        Task<bool> BottomGapSystemParaAsync(double value);
        Task<bool> BottomPhotoSystemParaAsync(double value);
        Task<bool> CameraOffsetAsync(double value);
        
        // 气缸控制执行方法
        Task TopWaferExecute();
        Task TrayWaferOuterExecute();
        Task TrayWaferInnerExecute();
        Task TrayExecute();
        Task ChuckLockExecute();
        Task HorizontalAdjustLockExecute();
        
        // 通信方法
        Task CalibrateXYR();
        Task SendMsg(int P1, int P2, int P3, int P4, int P5);
        Task<string> ReceiveMsg();
        Task SetXYRPosAsync(float x, float y, float r);
        Task<string> GetXYRPos();
        
        // 真空控制
        Task<bool> SetVacuumStateAsync(string vacuumType, bool state);
        Task<bool> GetVacuumStateAsync(string vacuumType);
        
        // 位置控制
        Task<bool> MoveToPositionAsync(string positionName, double value);
        Task<double> GetPositionAsync(string positionName);
        
        // XYR轴控制
        Task<bool> XYRMotion(double XTarget, double YTarget, double RTarget);
        
        // 晶圆操作
        Task<bool> TopWaferUp(bool IsTopHorizontalAdjust, bool IsTopHorizontalPhoto);
        Task<bool> TopWaferTakeUp();
        Task<bool> TakeXYRConfirmed();
        Task<bool> BottomWaferDown(uint WaferSeries);
        Task<bool> BottomWaferTakeDown(uint WaferSeries);
        Task<bool> TakeDownConfirmed();
        Task<bool> VariableConfirm(string FinshVariable);
        Task<bool> WaferInitialZ();
        
        // 相机操作
        Task TargetTakePhoto();
        
        // 事件注册
        void RegisterAction(string variableName, Action<object> action);
        void RegistryAction(string variableName, Action<object> action); // 兼容性方法名
        void UnregisterAction(string variableName);
        void UnregisterAllActions();
        
        // 资源管理
        Task InitializeAsync();
        Task CleanupAsync();
        Task<bool> Init(); // 初始化方法
    }
} 