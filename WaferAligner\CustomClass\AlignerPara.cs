﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Aligner<PERSON>;

namespace WaferAligner
{
    public class AlignerPara
    {
        private string _ProductName;
        private string _ProductSize;
        private double _SpacerThick;
        private double _MarkDistance;
        private WaferSeries _Material;
        private int _VisualNumber;

        private double _TopWaferPhotoLX;
        private double _TopWaferPhotoLY;
        private double _TopWaferPhotoLZ;
        private double _TopWaferPhotoRX;
        private double _TopWaferPhotoRY;
        private double _TopWaferPhotoRZ;
        private double _TopWaferPhotoZ;
        private double _TopWaferPhotoX;
        private double _TopWaferPhotoY;
        private double _TopWaferPhotoR;
        private bool _IsTopHorizontalAdjust;
        private bool _IsTopHorizontalPhoto;
        private double _TopWaferThick;

        private double _BottomWaferPhotoLX;
        private double _BottomWaferPhotoLY;
        private double _BottomWaferPhotoLZ;
        private double _BottomWaferPhotoRX;
        private double _BottomWaferPhotoTY;
        private double _BottomWaferPhotoRZ;
        private double _BottomWaferPhotoZ;
        private double _BottomWaferPhotoX;
        private double _BottomWaferPhotoY;
        private double _BottomWaferPhotoR;
        private double _BottomWaferThick;

        private double _BottomWaferFitZ;


        public string ProductName { get; set; }
        public string ProductSize { get; set; }
        public double SpacerThick { get; set; }
        public double MarkDistance { get; set; }
        public WaferSeries Material { get; set; }
        public int VisualNumber { get; set; }
        public double TopWaferPhotoLX { get; set; }
        public double TopWaferPhotoLY { get; set; }
        public double TopWaferPhotoLZ { get; set; }
        public double TopWaferPhotoRX { get; set; }
        public double TopWaferPhotoRY { get; set; }
        public double TopWaferPhotoRZ { get; set; }
        public double TopWaferPhotoZ { get; set; }
        public double TopWaferPhotoX { get; set; }
        public double TopWaferPhotoY { get; set; }
        public double TopWaferPhotoR { get; set; }
        public bool IsTopHorizontalAdjust { get; set; }
        public bool IsTopHorizontalPhoto { get; set; }
        public double TopWaferThick { get; set; }

        public double BottomWaferPhotoZ { get; set; }
        public double BottomWaferPhotoLX { get; set; }
        public double BottomWaferPhotoLY { get; set; }
        public double BottomWaferPhotoLZ { get; set; }
        public double BottomWaferPhotoRX { get; set; }
        public double BottomWaferPhotoRY { get; set; }
        public double BottomWaferPhotoRZ { get; set; }
        public double BottomWaferPhotoX { get; set; }
        public double BottomWaferPhotoY { get; set; }
        public double BottomWaferPhotoR { get; set; }
        public double BottomWaferThick { get; set; }

        public double BottomWaferPhotoFitZ { get; set; }

        public AlignerPara()
        {
            _ProductName = string.Empty;
            _ProductSize = "6寸";
            _MarkDistance = 0;
            _TopWaferPhotoLX = 0;
            _TopWaferPhotoLY = 0;
            _TopWaferPhotoLZ = 0;
            _TopWaferPhotoRX = 0;
            _TopWaferPhotoRY = 0;
            _TopWaferPhotoRZ = 0;
            _IsTopHorizontalAdjust = false;
            _IsTopHorizontalPhoto = false;
            _TopWaferThick = 0;

            _BottomWaferPhotoZ = 0;
            _BottomWaferPhotoLX = 0;
            _BottomWaferPhotoLY = 0;
            _BottomWaferPhotoLZ = 0;
            _BottomWaferPhotoRX = 0;
            _BottomWaferPhotoTY = 0;
            _BottomWaferPhotoRZ = 0;
            _BottomWaferThick = 0;
            _BottomWaferFitZ = 0;

        }
    }
}
