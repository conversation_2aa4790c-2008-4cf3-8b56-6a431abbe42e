# WaferAligner 系统架构整改计划

## 📋 项目概述

本文档详细描述了 WaferAligner 项目的架构重构计划，旨在解决当前项目中存在的命名混乱、架构不清晰、模块耦合度高等问题，建立清晰的分层架构和可复用的模块化设计。

## 🔍 当前架构问题分析

### 1. 第三方项目命名混乱
- `JYJ001.App.*` 系列项目命名不统一，与 WaferAligner 主题不符
- `Aya.*` 系列项目命名与业务无关
- `PLC.*` 项目在多个位置重复（`Common/PLC.*` 和 `PLC/*`）

### 2. 文件夹结构重复
- PLC相关项目同时存在于 `Common/` 和 `PLC/` 目录
- 服务层项目嵌套过深：`Services/Service.Common/JYJ001.App.Service.Common.*`

### 3. 依赖关系复杂
- 第三方项目之间存在循环依赖
- 主项目对所有第三方项目都有直接依赖

### 4. 架构层次不清晰
- 业务逻辑、基础设施、通信层混杂
- 缺乏清晰的分层设计

### 5. 冗余文件和未使用代码 ✅ **已解决**
- 编译输出目录（bin/obj）占用大量空间
- 重复的PLC项目导致维护困难
- 部分第三方源代码文件未被实际使用

## 🎯 重构目标

1. **统一命名规范**：采用 `WaferAligner.*` 前缀，按功能分层命名
2. **清晰分层架构**：建立 Core、Infrastructure、Services、Communication 分层
3. **模块化设计**：提取可复用模块为独立 DLL
4. **简化依赖关系**：减少项目间的耦合度
5. **提升可维护性**：便于后续开发和扩展

## 📦 新命名规范与项目映射

### 命名规范
```
WaferAligner.Infrastructure.*  // 基础设施层
WaferAligner.Core.*           // 核心业务层  
WaferAligner.Communication.*  // 通信层
WaferAligner.Services.*       // 服务层
```

### 项目重命名映射表
| 当前命名 | 建议新命名 | 功能描述 |
|---------|-----------|----------|
| JYJ001.App.Service.Common.Interface | WaferAligner.Services.Abstractions | 服务层接口定义 |
| JYJ001.App.Service.Common | WaferAligner.Services.Core | 核心服务实现 |
| JYJ001.App.Service.Common.Extension | WaferAligner.Services.Extensions | 服务扩展方法 |
| JYJ001.App.Business | WaferAligner.Core.Business | 核心业务逻辑 |
| JYJ001.App.Service.Usermanagement | WaferAligner.Services.UserManagement | 用户管理服务 |
| Aya.Extension | WaferAligner.Infrastructure.Extensions | 基础扩展方法 |
| Aya.Log | WaferAligner.Infrastructure.Logging | 日志记录功能 |
| PLC.Base | WaferAligner.Communication.Abstractions | 通信抽象接口 |
| PLC.Inovance | WaferAligner.Communication.Inovance | 汇川PLC通信 |
| WaferAligner.EventIds | WaferAligner.Core.Events | 事件定义 |

## 🏗️ 新目录结构设计

```
WaferAligner-Solution/
├── src/
│   ├── WaferAligner.App/                    # 主应用程序
│   ├── Core/                                # 核心业务层
│   │   ├── WaferAligner.Core.Business/      # 业务逻辑
│   │   ├── WaferAligner.Core.Events/        # 事件定义
│   │   └── WaferAligner.Core.Models/        # 数据模型
│   ├── Infrastructure/                      # 基础设施层
│   │   ├── WaferAligner.Infrastructure.Extensions/    # 扩展方法
│   │   ├── WaferAligner.Infrastructure.Logging/       # 日志功能
│   │   └── WaferAligner.Infrastructure.Configuration/ # 配置管理
│   ├── Communication/                       # 通信层
│   │   ├── WaferAligner.Communication.Abstractions/   # 通信抽象
│   │   ├── WaferAligner.Communication.Inovance/       # 汇川PLC
│   │   ├── WaferAligner.Communication.Serial/         # 串口通信
│   │   └── WaferAligner.Communication.BeckHoff/       # 倍福PLC
│   └── Services/                           # 服务层
│       ├── WaferAligner.Services.Abstractions/       # 服务接口
│       ├── WaferAligner.Services.Core/               # 核心服务
│       ├── WaferAligner.Services.Extensions/         # 服务扩展
│       └── WaferAligner.Services.UserManagement/     # 用户管理
├── tools/                                  # 工具和脚本
├── docs/                                   # 文档
└── tests/                                  # 测试项目
```

## 🔧 可独立成DLL的模块识别

### 高复用价值模块（优先DLL化）

#### 1. WaferAligner.Communication.Abstractions
- **功能**：PLC通信基础接口、串口通信接口
- **复用场景**：其他设备控制项目
- **包含内容**：IPlcInstance、通信基础类型定义

#### 2. WaferAligner.Communication.Inovance
- **功能**：汇川PLC通信实现
- **复用场景**：使用汇川PLC的项目
- **包含内容**：汇川PLC驱动、协议实现

#### 3. WaferAligner.Infrastructure.Logging
- **功能**：文件日志记录功能
- **复用场景**：所有需要日志的项目
- **包含内容**：FileLogger、日志配置

#### 4. WaferAligner.Services.UserManagement
- **功能**：用户管理、权限控制
- **复用场景**：需要用户系统的项目
- **包含内容**：用户认证、权限管理、安全功能

#### 5. WaferAligner.Core.Business
- **功能**：轴控制、配方管理等核心业务逻辑
- **复用场景**：类似的设备控制项目
- **包含内容**：轴控制算法、配方管理、设备状态管理

### 中等复用价值模块

#### 6. WaferAligner.Infrastructure.Extensions
- **功能**：通用扩展方法
- **复用场景**：.NET项目通用
- **包含内容**：Observable扩展、通用工具方法

#### 7. WaferAligner.Communication.Serial
- **功能**：串口通信实现
- **复用场景**：需要串口通信的项目
- **包含内容**：串口驱动封装、通信协议

## 📋 代码库清理记录 ✅ **已完成**

### 清理阶段：冗余文件和未使用代码清理
**目标**：清理编译输出、重复项目和未使用的第三方源代码
**完成时间**：2025-07-30
**具体成果**：

#### 1. 编译输出目录清理 ✅
- 删除所有项目的 `bin/` 和 `obj/` 目录
- 清理了约30+个编译输出目录
- 节省磁盘空间数百MB

#### 2. 重复项目清理 ✅
- 删除 `Common/PLC.Base`（保留 `PLC/PLC.Base`）
- 删除 `Common/PLC.Inovance`（保留 `PLC/PLC.Inovance`）
- 消除了PLC项目的重复，简化了依赖关系

#### 3. 安装包文件清理 ✅
- 删除 `WaferAlignerSetup/Debug/` 目录
- 删除 `WaferAlignerSetup/Release/` 目录
- 保留项目文件，删除编译输出

#### 4. 测试项目整理 ✅
- 删除 `WaferAligner/Tests/` 目录（包含被注释的简单测试）
- 保留 `WaferAligner/WaferAligner.Tests/` 目录（正式MSTest项目框架）

#### 5. 第三方源代码分析与清理 ✅
**已删除的未使用文件**：
- `Common/Aya.Log/FileLoggerConfiguration.cs` - 完全未被引用
- `Business/JYJ001.App.Business/RequestContext.cs` - 只定义结构体，未实际使用
- `Services/Service.Common/JYJ001.App.Service.Common/AccountService.cs` - 已在项目中排除
- `Services/Service.Common/JYJ001.App.Service.Common/UserRepository.cs` - 已在项目中排除
- `Services/Service.Common/JYJ001.App.Service.Common.Interface/IAccountService.cs` - 已在项目中排除
- `Services/Service.Common/JYJ001.App.Service.Common.Interface/IBaseRespository.cs` - 已在项目中排除
- `Services/Service.Common/JYJ001.App.Service.Common.Interface/IUserRepository.cs` - 已在项目中排除

**确认保留的第三方源代码**：
- ✅ `Aya.Extension/ObservableExtensions.cs` - 在LoggingService中用于日志流处理
- ✅ `Aya.Log/FileLogger.cs` - 文件日志记录功能的核心实现
- ✅ `JYJ001.App.Business/*` - 用户权限、日志观察者等核心业务模型
- ✅ `JYJ001.App.Service.*` 系列 - 服务层核心实现，被主程序引用
- ✅ `PLC.Base` 和 `PLC.Inovance` - PLC通信的核心组件

**清理成果**：
- 删除了7个未使用的源代码文件
- 清理了约200行无用代码
- 确认所有保留的第三方源代码都有实际用途
- 为重构工作奠定了清洁的代码基础

## 🚀 实施计划

### 第一阶段：基础设施层重构
**目标**：重命名和重组基础设施相关项目
**时间估计**：2-3天
**前置条件**：✅ 代码库清理已完成
**具体任务**：
1. 创建新的目录结构
2. 重命名 Aya.Extension → WaferAligner.Infrastructure.Extensions
3. 重命名 Aya.Log → WaferAligner.Infrastructure.Logging
4. 更新命名空间和引用关系
5. 验证编译和基本功能

**重构依据**：
- Aya.Extension项目中的ObservableExtensions.cs确认在使用，需要保留并重命名
- Aya.Log项目中的FileLogger.cs是日志系统的核心组件，需要保留并重命名

### 第二阶段：通信层整合
**目标**：整合PLC相关项目，消除重复
**时间估计**：2-3天（已简化，重复项目已清理）
**前置条件**：✅ 重复的Common/PLC.*项目已删除
**具体任务**：
1. ~~合并 Common/PLC.* 和 PLC/* 项目~~ ✅ 已完成（重复项目已删除）
2. 重命名 PLC.Base → WaferAligner.Communication.Abstractions
3. 重命名 PLC.Inovance → WaferAligner.Communication.Inovance
4. 统一通信接口抽象
5. 整理串口通信模块
6. 更新依赖关系

**重构依据**：
- PLC.Base和PLC.Inovance项目确认在使用，是PLC通信的核心组件
- IPlcInstance接口在多个地方被引用和实现
- InvoancePlcInstance在服务注册和主程序中被使用

### 第三阶段：服务层重构
**目标**：重组JYJ001.App.Service.*系列项目
**时间估计**：2-3天
**前置条件**：✅ 未使用的服务文件已清理
**具体任务**：
1. 重命名为 WaferAligner.Services.* 系列
2. 简化服务层目录结构
3. 优化服务接口设计
4. 更新依赖注入配置

**重构依据**：
- JYJ001.App.Service.Usermanagement确认在使用，在主程序中被引用
- JYJ001.App.Service.Common系列项目是服务层的核心实现
- 已清理的AccountService、UserRepository等未使用文件不会影响重构

### 第四阶段：核心业务层提取
**目标**：将可复用的业务逻辑提取为独立DLL项目
**时间估计**：4-5天
**前置条件**：✅ JYJ001.App.Business项目分析已完成
**具体任务**：
1. 分析业务逻辑模块
2. 提取轴控制、配方管理等核心功能
3. 创建 WaferAligner.Core.* 系列项目
4. 建立清晰的业务接口
5. 配置DLL输出和打包

**重构依据**：
- JYJ001.App.Business项目中的核心组件已确认在使用：
  - LogObserver.cs - 日志观察者模式实现
  - UserAuth.cs - 用户权限枚举定义
  - UserInfo.cs - 用户信息数据模型
- 这些组件将作为WaferAligner.Core.Business的基础

### 第五阶段：主应用程序重构
**目标**：更新主应用程序的引用关系，验证重构结果
**时间估计**：2-3天
**具体任务**：
1. 更新 WaferAligner 主项目的引用
2. 更新依赖注入配置
3. 全面功能测试
4. 性能测试和优化
5. 文档更新

## 📋 风险评估与应对策略

### 主要风险
1. **编译错误**：命名空间变更可能导致编译失败
2. **功能回归**：重构过程中可能引入bug
3. **依赖冲突**：新的依赖关系可能产生冲突
4. **测试覆盖**：现有测试可能需要大量更新

### 应对策略
1. **渐进式重构**：分阶段进行，每个阶段都保证项目可编译运行
2. **向后兼容**：保留旧的命名空间别名，逐步迁移
3. **充分测试**：每个阶段完成后进行完整的功能测试
4. **版本控制**：每个阶段完成后创建标签，便于回滚
5. **文档同步**：同步更新相关文档和配置

### 风险缓解措施 ✅ **已实施**
1. **代码库清理**：已完成冗余文件清理，降低了重构复杂度
2. **依赖关系分析**：已确认所有保留的第三方源代码都有实际用途
3. **重复项目消除**：已删除重复的PLC项目，简化了依赖关系
4. **未使用代码清理**：已删除7个未使用的源代码文件，减少了潜在的混淆

## 🎯 预期收益

### 短期收益
- 项目结构清晰，新人上手更容易
- 命名规范统一，代码可读性提升
- 消除重复项目，减少维护成本
- 依赖关系简化，编译速度提升

### 长期收益
- 模块化DLL便于其他项目复用
- 分层架构便于单元测试
- 依赖关系清晰，便于后续扩展
- 技术债务减少，开发效率提升

### 已实现收益 ✅
- **磁盘空间节省**：清理编译输出和冗余文件，节省数百MB空间
- **代码质量提升**：删除未使用代码，提高代码库整洁度
- **依赖关系简化**：消除重复PLC项目，简化项目依赖
- **重构风险降低**：通过清理工作，为后续重构奠定了清洁的基础

## 📝 后续维护建议

1. **建立编码规范**：制定详细的命名和架构规范文档
2. **持续重构**：定期评估和优化架构设计
3. **模块版本管理**：为DLL模块建立版本管理机制
4. **文档维护**：保持架构文档与代码同步更新
5. **团队培训**：确保团队成员理解新的架构设计

---

## 📈 项目进度跟踪

### 已完成阶段 ✅
- **清理阶段**（2025-07-30）：代码库清理和第三方源代码分析
  - 编译输出目录清理
  - 重复项目消除
  - 未使用文件清理
  - 第三方源代码使用情况分析

### 进行中阶段
- 无

### 待开始阶段
- **第一阶段**：基础设施层重构（Aya.* → WaferAligner.Infrastructure.*）
- **第二阶段**：通信层整合（PLC.* → WaferAligner.Communication.*）
- **第三阶段**：服务层重构（JYJ001.App.Service.* → WaferAligner.Services.*）
- **第四阶段**：核心业务层提取（JYJ001.App.Business → WaferAligner.Core.*）
- **第五阶段**：主应用程序重构

---

**文档版本**：v1.1
**创建日期**：2025-07-30
**最后更新**：2025-07-30
**负责人**：架构重构小组
**更新内容**：添加代码库清理记录和第三方源代码分析结果
