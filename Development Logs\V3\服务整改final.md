# WaferAligner项目依赖注入与服务调用整改方案

## 一、现状分析

### 1.1 依赖注入框架

WaferAligner项目当前使用了两种依赖注入框架：

- **Microsoft.Extensions.DependencyInjection**：主要用于服务注册和生命周期管理
- **Prism.Ioc**：作为补充DI框架，用于部分服务注册

### 1.2 服务注册方式

当前服务注册主要通过以下方式实现：

- **ServiceConfiguration.cs**：静态类，统一配置服务注册
- **ServiceExtension.cs**：扩展方法，模块化分组不同类型的服务
- **AxisServiceExtensions.cs**：轴服务专用扩展类
- **SerialControlServicesExtensions.cs**：串口控制服务专用扩展类

### 1.3 服务生命周期管理

服务生命周期管理存在以下特点：

- **单例服务(Singleton)**：大多数核心服务，如ILoggingService、ResourceManager、IPlcConnectionManager等
- **瞬态服务(Transient)**：控制器类和视图模型，如ZAxisViewModelNew、CameraAxisViewModelNew
- **未使用作用域(Scoped)**：在桌面应用中，作用域服务未被使用，这是合理的

### 1.4 日志服务实现

日志服务存在以下特点：

- **接口定义**：ILoggingService接口定义在JYJ001.App.Services.Common.Interfaces命名空间
- **实现类**：LoggingService支持多个日志记录器（如FileLogger）
- **扩展方法**：FunctionExtensions.cs提供丰富的扩展方法（LogTrace、LogInformation等）
- **使用模式**：页面通过BasePage继承获取统一的Logger属性

### 1.5 服务扩展类架构一致性问题

当前服务扩展类存在一些架构一致性问题：

- **扩展类命名不一致**：有SerialControlServicesExtensions但没有对应的PLCControlServicesExtensions
- **服务注册位置混乱**：
  - 串口控制有专门的扩展类和命名空间
  - PLC相关服务直接在ServiceConfiguration的ConfigurePLCServices方法中注册
  - 这破坏了架构的对称性和一致性
- **扩展类职责边界模糊**：
  - ConfigurePLCServices方法中同时注册PLC服务和调用串口服务注册
  - 部分服务可能在多个扩展类中重复注册（如ResourceManager）

## 二、存在问题

### 2.1 日志服务混合使用

- 代码中同时存在ILoggingService和Microsoft.Extensions.Logging.ILogger的混合使用
- BasePage中存在已标记过时的ILogger属性，可能导致混淆
- 不同代码区域使用不同的日志记录方式，缺乏一致性

### 2.2 服务获取方式不统一

- 部分代码直接使用CommonFun.host.Services.GetService获取服务
- 部分代码使用继承自BasePage的GetService<T>方法
- 部分代码使用this.GetRequiredService获取服务
- 在不同页面和组件中服务注入时机不一致（构造函数vs初始化方法）

### 2.3 Timer资源管理改进空间

- 已使用TimerWrapper替代System.Timers.Timer，但资源注册和释放仍有优化空间
- 多个Timer实例的生命周期管理不够统一

### 2.4 异常处理机制冗余

- 很多地方存在重复的try-catch结构
- 异常处理和日志记录模式不统一

### 2.5 服务注入时机不一致

- 有些服务在构造函数中注入，有些在OnInitializeAsync中注入
- 可能导致初始化顺序不确定和不必要的null检查

### 2.6 用户上下文静态依赖

- 用户信息和权限通过CurrentUser静态类管理，缺乏依赖注入
- 无法在单元测试中Mock和替换用户上下文

### 2.7 框架混用和服务注册结构不一致

- 同时使用Prism.IoC和Microsoft.Extensions.DependencyInjection两种容器
- ServiceExtension.cs使用Prism的RegisterSingleton，其他用Microsoft的AddSingleton
- 缺少PLCControlServicesExtensions，与SerialControlServicesExtensions不对称
- 服务注册位置分散，容易导致重复注册和生命周期管理混乱

## 三、整改目标

- 全面统一依赖注入与服务注册方式，消除静态类和服务定位器依赖
- 日志、用户管理、轴/PLC/串口等所有服务注册与获取方式标准化
- 明确所有服务生命周期（单例/瞬态）
- 用户上下文、权限控制、资源管理全部通过依赖注入实现
- 保证原有功能的正确性和完整性，便于后续维护和扩展
- 优化服务扩展类架构，保持一致性和对称性

## 四、优化方案

### 4.1 服务注册标准化

#### 执行步骤

1. **服务注册风格统一**
   - 将所有RegisterSingleton调用替换为AddSingleton
   - 将所有Register调用替换为AddTransient

2. **生命周期审查**
   - 检查所有服务注册，确保正确的生命周期类型

3. **服务注册模块化**
   - 按功能域进一步整理服务注册代码
   - 分离基础设施、业务和UI服务

#### 代码示例

```csharp
public static IServiceCollection ConfigureAllServices(this IServiceCollection services, IConfiguration configuration)
{
    // 日志服务
    services.AddSingleton<ILoggingService, LoggingService>();

    // 用户管理与上下文
    services.AddSingleton<IUserStorageService, JsonUserStorageService>();
    services.AddSingleton<IUserManagement, UserManagementService>();
    services.AddSingleton<IUserContext, UserContextService>();

    // 轴/PLC/串口相关
    services.AddSingleton<IPlcConnectionManager, PlcConnectionManager>();
    services.AddSingleton<IPlcCommunication, PlcCommunication>();
    services.AddSingleton<IPlcVariableService, PlcVariableService>();
    services.AddSingleton<IAxisViewModelFactory, AxisViewModelFactory>();
    services.AddSingleton<IMainWindowViewModel, MainWindowViewModelService>();
    services.AddSingleton<ICylinderService, CylinderService>();

    // 串口轴相关
    services.AddSingleton<ISerialAxisControllerFactory, SerialAxisControllerFactory>();
    services.AddTransient<ISerialAxisViewModel, SerialAxisViewModel>();

    // 其他服务...
    services.AddSingleton<ResourceManager>();
    services.AddSingleton<PerformanceMonitor>();

    return services;
}
```

#### 服务生命周期验证

```csharp
private static void ValidateServiceLifetimes(IServiceProvider serviceProvider)
{
    // 检查核心服务 - 必须是单例
    ValidateSingletonService<ILoggingService>(serviceProvider);
    ValidateSingletonService<ResourceManager>(serviceProvider);
    ValidateSingletonService<IAxisViewModelFactory>(serviceProvider);
    ValidateSingletonService<IAxisEventService>(serviceProvider);
    ValidateSingletonService<IUserContext>(serviceProvider);
    // ... 其他应为单例的服务
    
    // 日志记录验证结果
    serviceProvider.GetService<ILoggingService>()?.LogInformation("服务生命周期验证完成", EventIds.ConfigurationValidated);
}

private static void ValidateSingletonService<T>(IServiceProvider serviceProvider) where T : class
{
    var logger = serviceProvider.GetService<ILoggingService>();
    var serviceType = typeof(T);
    
    // 获取两次服务实例，检查是否为同一实例
    var instance1 = serviceProvider.GetService<T>();
    var instance2 = serviceProvider.GetService<T>();
    
    if (instance1 == null)
    {
        logger?.LogWarning($"服务 {serviceType.Name} 未注册", EventIds.ServiceValidationWarning);
        return;
    }
    
    if (!ReferenceEquals(instance1, instance2))
    {
        logger?.LogWarning($"服务 {serviceType.Name} 注册为非单例，但应该是单例", EventIds.ServiceValidationWarning);
    }
}
```

### 4.2 服务扩展类架构优化

#### 执行步骤

1. **创建PLCControlServicesExtensions类**
   - 实现与SerialControlServicesExtensions对应的PLC服务扩展类
   - 将PLC相关服务注册从ServiceConfiguration移到专用扩展类

2. **明确服务扩展类职责**
   - PLCControlServicesExtensions负责PLC通信相关服务
   - SerialControlServicesExtensions负责串口通信相关服务
   - AxisServiceExtensions负责轴控制模型和服务

3. **统一命名空间结构**
   - 为不同通信方式创建一致的命名空间层次

#### 代码示例

```csharp
// 新增PLCControlServicesExtensions.cs
namespace WaferAligner.PLCControl
{
    public static class PLCControlServicesExtensions
    {
        public static IServiceCollection AddPLCControlServices(this IServiceCollection services)
        {
            if (services == null)
                throw new ArgumentNullException(nameof(services));
                
            // PLC连接管理器
            services.AddSingleton<IPlcConnectionManager, PlcConnectionManager>();
            
            // 注册PLC实例
            services.AddSingleton<Aya.PLC.Base.IPlcInstance, InvoancePlcInstance>();
            
            // PLC变量服务
            services.AddSingleton<IPlcVariableService, PlcVariableService>();
            
            // PLC通信服务
            services.AddSingleton<IPlcCommunication, PlcCommunication>();
            
            return services;
        }
        
        // 可以添加其他PLC相关服务配置方法
        public static IServiceCollection AddPLCMonitoring(this IServiceCollection services)
        {
            // 添加PLC监控相关服务
            return services;
        }
    }
}
```

```csharp
// 修改ServiceConfiguration.cs中的配置方法
private static IServiceCollection ConfigurePLCServices(this IServiceCollection services)
{
    // 使用专门的PLC服务扩展
    services.AddPLCControlServices();
    
    return services;
}

// 增加一个独立的串口配置方法，与PLC分开
private static IServiceCollection ConfigureSerialServices(this IServiceCollection services)
{
    // 注册串口控制服务
    services.AddSerialControlServices();
    
    return services;
}

// 在ConfigureAllServices中调用两个独立的方法
public static IServiceCollection ConfigureAllServices(this IServiceCollection services)
{
    // 配置核心服务
    services.ConfigureCoreServices();
    
    // 配置基础设施服务
    services.ConfigureInfrastructureServices();
    
    // 配置业务服务
    services.ConfigureBusinessServices();
    
    // 配置PLC服务
    services.ConfigurePLCServices();
    
    // 配置串口服务 - 新增，与PLC分开
    services.ConfigureSerialServices();
    
    // 配置轴控制服务
    services.ConfigureAxisServices();
    
    // 配置UI服务
    services.ConfigureUIServices();
    
    // ...其余代码
}
```

#### 命名空间和组织结构的一致性

建立清晰的层次结构：

```
WaferAligner
├── Common
│   └── ServiceConfiguration.cs (总入口)
├── PLCControl
│   ├── PLCControlServicesExtensions.cs (PLC服务扩展)
│   ├── Implementation
│   └── Interfaces
├── SerialControl
│   ├── SerialControlServicesExtensions.cs (串口服务扩展)
│   ├── Implementation
│   └── Interfaces
└── Services
    ├── AxisServiceExtensions.cs (轴服务扩展)
    └── 其他服务...
```

### 4.3 用户管理与上下文整改

#### 用户上下文服务（彻底替代CurrentUser静态类）

```csharp
public interface IUserContext
{
    UserInfo? CurrentUser { get; }
    void SetCurrentUser(UserInfo? user);
    bool IsAuthenticated { get; }
    bool HasPermission(string permission);
    bool HasRole(string role);
    bool IsAdmin();
    bool IsOperator();
    bool CanConfigParameters();
    bool CanAccessMotionPage();
    string GetRoleDisplayName();
    void Logout();
}
```

实现类UserContextService通过依赖注入获取IUserManagement，所有页面和服务通过依赖注入获取用户信息和权限。

```csharp
public class UserContextService : IUserContext
{
    private readonly IUserManagement _userManagement;
    private readonly ILoggingService _loggingService;
    private UserInfo? _currentUser;
    
    public UserContextService(IUserManagement userManagement, ILoggingService loggingService)
    {
        _userManagement = userManagement;
        _loggingService = loggingService;
    }
    
    public UserInfo? CurrentUser => _currentUser;
    
    public void SetCurrentUser(UserInfo? user)
    {
        _currentUser = user;
        _loggingService.LogInformation($"用户上下文更新: {user?.Username ?? "未登录"}", EventIds.UserContextChanged);
    }
    
    public bool IsAuthenticated => _currentUser != null;
    
    public bool HasPermission(string permission)
    {
        if (_currentUser == null || _userManagement == null) return false;
        return _userManagement.CheckPermission(_currentUser.Username, permission);
    }
    
    // 其他方法实现...
}
```

#### 用户管理服务与存储服务

- `IUserManagement`/`UserManagementService`：负责认证、权限、角色、用户增删改查
- `IUserStorageService`/`JsonUserStorageService`：负责用户、角色、权限的本地文件存储，支持异步和配置化路径

```csharp
public interface IUserManagement
{
    bool Authenticate(string username, string password);
    UserInfo? GetUserInfo(string username);
    bool CheckPermission(string username, string permission);
    // ... 角色、权限、用户管理等
}
```

### 4.4 日志服务统一

#### 执行步骤

1. **移除ILogger引用**
   - 移除所有Microsoft.Extensions.Logging.ILogger的直接依赖
   - 移除BasePage中过时的ILogger属性

2. **统一日志扩展方法**
   - 确保所有日志调用使用ILoggingService扩展方法

3. **日志服务注册优化**
   - 确保LoggingService被正确注册为单例

#### 代码示例

```csharp
// 修改前 - BasePage.cs
[Obsolete("请使用 Logger 属性替代，此属性将在未来版本中移除")]
protected JYJ001.App.Services.Common.Interfaces.ILoggingService ILogger => null;

// 修改后 - 完全移除此属性
```

```csharp
// 优化LoggingService注册
private static IServiceCollection ConfigureCoreServices(this IServiceCollection services)
{
    // 移除微软日志框架直接注册
    // services.AddLogging(builder => {...});
    
    // 仅保留自定义日志服务
    services.AddSingleton<ILoggingService, LoggingService>();
    
    // 其他核心服务...
    return services;
}
```

```csharp
// 修改前 - 混合使用
_logger.LogInformation("消息"); // Microsoft.Extensions.Logging
_loggingService.Log(LogLevel.Information, eventId, "消息");

// 修改后 - 统一使用
_loggingService.LogInformation("消息", eventId);
```

在轴视图模型中，优化日志辅助方法：

```csharp
// 在AxisViewModelBase.cs中，修改日志辅助方法，完全去除对Microsoft.Extensions.Logging的依赖
protected virtual void LogInformation(string message, EventId? eventId = null)
{
    _loggingService?.LogInformation(message, eventId);
}

protected virtual void LogWarning(string message, EventId? eventId = null)
{
    _loggingService?.LogWarning(message, eventId);
}

protected virtual void LogError(Exception ex, string message, EventId? eventId = null)
{
    _loggingService?.LogError(ex, message, eventId);
}
```

### 4.5 服务获取规范化

#### 执行步骤

1. **统一服务获取方法**
   - 在所有页面中使用BasePage提供的GetService/GetRequiredService

2. **避免直接使用Host服务定位器**
   - 移除对CommonFun.host.Services的直接调用

3. **规范化构造函数注入**
   - 确保所有页面在构造函数中一致获取服务

#### 代码示例

```csharp
// 修改前
_axisEventService = CommonFun.host.Services.GetService<IAxisEventService>();

// 修改后
_axisEventService = GetService<IAxisEventService>();
```

```csharp
// 修改前 - 服务获取散落在不同方法
public FTitlePage3()
{
    _loggingService = GetService<ILoggingService>();
    // ...
}

protected override async Task OnInitializeAsync()
{
    _axisEventService = GetService<IAxisEventService>();
    // ...
}

// 修改后 - 统一在构造函数获取所有需要的服务
public FTitlePage3()
{
    try
    {
        InitializeComponent();

        // 集中获取所有需要的服务
        _loggingService = GetService<ILoggingService>();
        _axisEventService = GetService<IAxisEventService>();
        _plcVariableService = GetService<IPlcVariableService>();
        _uiUpdateService = GetService<IUIUpdateService>();
        _mainWindowViewModel = GetService<IMainWindowViewModel>();
        _axisFactory = GetService<IAxisViewModelFactory>();
        _cylinderService = GetService<ICylinderService>();
        _plcConnectionManager = GetService<IPlcConnectionManager>();
        _userContext = GetService<IUserContext>();
        
        // 记录服务获取
        _loggingService?.LogInformation("页面服务初始化完成", EventIds.ServiceInitialized);
    }
    catch (Exception ex)
    {
        MessageBox.Show($"页面初始化失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}
```

扩展BasePage增加批量服务获取支持：

```csharp
// 添加到BasePage.cs
/// <summary>
/// 批量获取多个服务，返回获取失败的服务类型列表
/// </summary>
protected List<Type> GetServices<T1, T2, T3, T4, T5, T6>(
    out T1 service1, out T2 service2, out T3 service3, 
    out T4 service4, out T5 service5, out T6 service6)
    where T1 : class
    where T2 : class
    where T3 : class
    where T4 : class
    where T5 : class
    where T6 : class
{
    List<Type> failedServices = new List<Type>();
    
    try { service1 = GetService<T1>(); } 
    catch (Exception) { service1 = null; failedServices.Add(typeof(T1)); }
    
    try { service2 = GetService<T2>(); } 
    catch (Exception) { service2 = null; failedServices.Add(typeof(T2)); }
    
    // ... 类似获取其他服务
    
    return failedServices;
}
```

### 4.6 资源管理优化

#### 执行步骤

1. **增强ResourceManager功能**
   - 扩展ResourceManager支持更多资源类型
   - 优化资源释放机制

2. **统一Timer管理**
   - 使用统一的TimerWrapper机制

3. **优化取消令牌管理**
   - 规范化CancellationTokenSource的使用和释放

#### 代码示例

```csharp
// 增强ResourceManager.cs
public void RegisterResources<T>(string baseKey, IEnumerable<T> resources) where T : IDisposable
{
    if (resources == null) return;
    
    int index = 0;
    foreach (var resource in resources)
    {
        RegisterResource($"{baseKey}_{index++}", resource);
    }
}

public void LogResourceStatus(ILoggingService loggingService)
{
    if (loggingService == null) return;
    
    loggingService.LogInformation($"当前资源数: {_resources.Count}", EventIds.ResourceStatus);
    
    // 分类统计
    int timerCount = 0, ctsCount = 0, otherCount = 0;
    
    foreach (var resource in _resources.Values)
    {
        if (resource is TimerWrapper) timerCount++;
        else if (resource is CancellationTokenSourceWrapper) ctsCount++;
        else otherCount++;
    }
    
    loggingService.LogInformation($"资源统计: 定时器({timerCount}), 取消令牌({ctsCount}), 其他({otherCount})", 
        EventIds.ResourceStatus);
}
```

```csharp
// 添加到BasePage.cs的工厂方法
protected TimerWrapper CreateTimer(string name, double interval, ElapsedEventHandler handler)
{
    var timer = new TimerWrapper($"{this.GetType().Name}_{name}", interval, Logger);
    timer.AddElapsedHandler((s, e) => {
        SafeInvoke(() => handler(s, e));
    });
    RegisterResource(name, timer);
    return timer;
}

protected CancellationTokenSource CreateCancellationTokenSource(string name)
{
    var cts = new CancellationTokenSource();
    RegisterCancellationTokenSource(name, cts);
    return cts;
}
```

### 4.7 轴控制和PLC通信优化

#### 执行步骤

1. **轴视图模型资源清理增强**
   - 添加统一的资源释放机制

2. **统一轴视图模型的构造函数注入**
   - 确保所有轴视图模型使用一致的服务注入方式

3. **优化PlcCommunication的连接管理**
   - 增强连接管理的可靠性和重试机制

#### 代码示例

```csharp
// 添加到AxisViewModelBase.cs
public virtual async Task DisposeAsync()
{
    // 取消所有进行中的轴操作
    try
    {
        // 停止所有可能的运动
        await StopAsync();
        
        // 解注册所有事件处理器
        if (_registeredActions != null)
        {
            foreach (var action in _registeredActions.ToList())
            {
                UnregisterAction(action.Key);
            }
        }
    }
    catch (Exception ex)
    {
        _loggingService?.LogError(ex, $"释放轴视图模型资源时发生错误: {AxisName}", EventIds.ResourceReleased);
    }
}
```

```csharp
// 在PlcCommunication中增加连接生命周期管理方法
public async Task EnsureConnectionAsync(string axisName, CancellationToken cancellationToken = default)
{
    if (string.IsNullOrEmpty(axisName) || !_axisVarMap.ContainsKey(axisName))
    {
        throw new ArgumentException($"未知轴名称或未配置轴变量映射: {axisName}");
    }

    var axisVars = _axisVarMap[axisName];
    if (!_plcConnectionManager.IsConnected(axisVars.ConnectionName))
    {
        await _plcConnectionManager.ConnectAsync(axisVars.ConnectionName);
        _loggingService.LogInformation($"已重新连接到PLC: {axisVars.ConnectionName}", 
            EventIds.PlcConnected);
    }
}
```

```csharp
// 增强PLC连接配置选项
public class PlcConnectionOptions
{
    public int ConnectTimeout { get; set; } = 5000; // 默认5秒
    public int RetryCount { get; set; } = 3; // 默认重试3次
    public bool EnableDevelopmentMode { get; set; } = false; // 开发模式
}

// 在服务注册中添加选项
services.Configure<PlcConnectionOptions>(configuration.GetSection("PlcConnection"));
```

## 五、批量替换清单

### 5.1 替换CurrentUser静态调用

| 替换前 | 替换后 |
|--------|--------|
| `CurrentUser.User` | `_userContext.CurrentUser` |
| `CurrentUser.IsAdmin()` | `_userContext.IsAdmin()` |
| `CurrentUser.HasPermission(xxx)` | `_userContext.HasPermission(xxx)` |
| `CurrentUser.CanAccessMotionPage()` | `_userContext.CanAccessMotionPage()` |
| `CurrentUser.GetRoleDisplayName()` | `_userContext.GetRoleDisplayName()` |
| `CurrentUser.Logout()` | `_userContext.Logout()` |

### 5.2 替换服务获取方式

| 替换前 | 替换后 |
|--------|--------|
| `CommonFun.host.Services.GetService<T>()` | `GetService<T>()` 或 构造函数注入 |
| `ServiceProvider.GetService<T>()` | `GetService<T>()` |
| `FHeaderMainFooter.frmM.Logger` | 通过注入方式获取 `_loggingService` |

### 5.3 替换日志调用

| 替换前 | 替换后 |
|--------|--------|
| `_logger.LogInformation("msg")` | `_loggingService.LogInformation("msg")` |
| `_loggingService.Log(LogLevel.Information, eventId, "msg")` | `_loggingService.LogInformation("msg", eventId)` |
| `ILogger`相关代码 | 全部移除，仅用`ILoggingService` |

### 5.4 替换轴控制相关代码

| 替换前 | 替换后 |
|--------|--------|
| 直接创建轴视图模型 | 通过工厂获取 `_axisFactory.CreateXAxisAsync()` |
| 旧版PLC接口直接调用 | 通过 `_plcCommunication` 或 `_plcVariableService` 访问 |

### 5.5 替换资源管理

| 替换前 | 替换后 |
|--------|--------|
| `new System.Timers.Timer()` | `CreateTimer("name", interval, handlerMethod)` |
| `new CancellationTokenSource()` | `CreateCancellationTokenSource("name")` |
| 手动管理资源释放 | 统一用ResourceManager注册和释放 |

### 5.6 替换服务注册

| 替换前 | 替换后 |
|--------|--------|
| `containerRegistry.RegisterSingleton<T, TImpl>()` | `services.AddSingleton<T, TImpl>()` |
| `containerRegistry.Register<T, TImpl>()` | `services.AddTransient<T, TImpl>()` |

### 5.7 架构一致性优化

| 当前结构 | 优化后结构 |
|--------|--------|
| PLC服务直接在ServiceConfiguration中注册 | 创建PLCControlServicesExtensions类 |
| ConfigurePLCServices中调用SerialControlServices | 分离为独立的ConfigureSerialServices方法 |
| 框架混用(Prism.IoC和Microsoft DI) | 统一使用Microsoft.Extensions.DependencyInjection |
| 服务注册分散，可能重复 | 明确各扩展类职责，避免重复注册 |

## 六、实施计划

### 6.1 实施顺序与依赖关系

1. **第一阶段：架构优化与服务注册统一（4天）**
   - 创建PLCControlServicesExtensions类
   - 统一依赖注入框架为Microsoft.Extensions.DependencyInjection
   - 服务注册标准化
   - 服务生命周期验证机制

2. **第二阶段：日志服务统一（3-4天）**
   - 移除ILogger相关代码
   - 统一ILoggingService扩展方法
   - 轴视图模型中的日志调用优化

3. **第三阶段：用户上下文替换（2天）**
   - 创建IUserContext接口和实现
   - 替换所有CurrentUser静态调用
   - 在页面中注入IUserContext

4. **第四阶段：服务获取规范化（2-3天）**
   - 统一服务获取方法
   - 规范化构造函数注入
   - BasePage扩展方法优化

5. **第五阶段：资源管理优化（3-4天）**
   - ResourceManager增强
   - Timer和CancellationTokenSource管理优化
   - 轴视图模型资源清理

6. **第六阶段：测试与验证（2-3天）**
   - 功能测试
   - 性能测试
   - 内存泄漏检查

总计：16-20个工作日可完成全部优化，每个阶段都可独立交付并产生价值。

### 6.2 验证与测试计划

每个阶段实施后需进行以下测试：

1. **服务初始化测试**
   - 验证所有服务是否能正确创建
   - 检查服务生命周期是否符合预期

2. **轴控制功能测试**
   - 创建所有类型的轴视图模型实例
   - 执行基本操作（移动、回零、停止等）
   - 验证日志记录是否正确

3. **用户权限测试**
   - 模拟不同用户角色登录
   - 验证权限检查功能
   - 确认页面访问控制

4. **PLC通信测试**
   - 验证读写PLC变量功能
   - 测试连接管理和异常恢复

5. **资源管理测试**
   - 验证资源释放机制
   - 确认无内存泄漏
   - 长时间运行稳定性测试

### 6.3 风险管理

1. **原有功能保障**
   - 每个阶段后进行全面测试，尤其是高频操作和关键功能
   - 保留原始代码备份，以便回滚
   - 采用功能切换机制，允许在运行时切换回旧实现

2. **兼容性保障**
   - 所有替换都保持原有方法签名和行为不变
   - 确保返回类型和异常处理保持一致
   - 优先实现核心功��，避免过度设计

3. **分阶段验证**
   - 每个替换后立即编译验证，确保无语法错误
   - 进行单元测试和功能测试，确认行为一致
   - 逐步推进，确保每个阶段都稳定可用

## 七、详细实施指南

### 7.1 服务扩展类架构优化实施

1. 创建PLCControlServicesExtensions类，整合PLC相关服务注册
2. 修改ServiceConfiguration.cs，分离PLC和串口服务配置
3. 调整命名空间和目录结构，保持一致性
4. 消除服务注册重复，确保每个服务只注册一次
5. 统一使用Microsoft.Extensions.DependencyInjection框架

### 7.2 用户上下文服务实现

1. 创建IUserContext接口和UserContextService实现类
2. 在ServiceConfiguration中注册为单例
3. 修改CurrentUser静态类，使其在内部委托给UserContextService
4. 在BasePage中添加UserContext属性
5. 替换所有CurrentUser静态调用为_userContext实例调用

### 7.3 日志服务统一实现

1. 移除BasePage中过时的ILogger属性
2. 检查FunctionExtensions.cs中的所有扩展方法，确保签名一致
3. 统一所有日志调用为_loggingService.LogXxx(message, eventId)格式
4. 确保所有事件ID使用WaferAligner.EventIds类中的常量

### 7.4 资源管理实现

1. 增强ResourceManager，添加批量资源管理和监控功能
2. 在BasePage中添加CreateTimer和CreateCancellationTokenSource工厂方法
3. 为轴视图模型添加DisposeAsync方法，确保资源正确释放
4. 在页面OnDispose方法中确保所有资源正确释放

### 7.5 轴控制和PLC通信优化

1. 统一所有轴视图模型的构造函数注入模式
2. 确保所有轴视图模型通过工厂方法创建
3. 增强PlcCommunication的连接管理，添加重试和容错机制
4. 确保所有PLC变量读写通过服务接口进行，避免直接访问

## 八、结论

本整改方案已全面涵盖WaferAligner项目依赖注入与服务调用的各个方面，包括：

1. **统一服务注册**：标准化所有服务的注册方式和生命周期
2. **优化服务扩展类架构**：增加PLCControlServicesExtensions，保持架构一致性
3. **用户上下文服务化**：将CurrentUser静态类替换为可注入的IUserContext服务
4. **日志服务统一**：消除ILogger与ILoggingService的混用，统一日志接口
5. **服务获取规范化**：统一所有代码中的服务获取方式
6. **资源管理优化**：强化ResourceManager功能，统一资源生命周期管理
7. **轴控制与PLC通信增强**：改进通信可靠性和资源管理

本方案与Phase 3重构计划中的"统一日志记录机制"和"清理剩余静态耦合"任务紧密对应，实施后将显著提升WaferAligner项目的代码质量和系统稳定性。

通过标准化服务注册、统一日志服务、规范化服务获取和优化资源管理，项目将获得以下收益：

1. **提高系统稳定性**：通过统一的资源管理和服务生命周期控制，减少潜在的资源泄漏和异常
2. **提升代码可维护性**：统一的服务调用模式和架构一致性使代码更加清晰易懂
3. **增强系统弹性**：更好的异常处理和日志记录提供了更强的问题诊断能力
4. **降低后续开发成本**：标准化的服务注册和获取模式使新功能开发更加高效
5. **提高可测试性**：依赖注入模式使单元测试和集成测试更加容易实现

方案已全面覆盖WaferAligner项目的所有核心代码，包括轴控制、PLC通信、UI交互等各个方面，确保原有功能的正确性和完整性。

按照分阶段实施计划，可以在保证系统稳定运行的同时，逐步实现对代码库的优化和改进。 

## 九、工作进度更新（2025年7月25日）

### 9.1 已完成的整改工作

#### 第一阶段：架构优化与服务注册统一

1. ✅ **创建PLCControlServicesExtensions类**
   - 实现了专用的PLC服务扩展类，与SerialControlServicesExtensions对应
   - 集中管理所有PLC相关服务的注册
   - 位置：`WaferAligner/PLCControl/PLCControlServicesExtensions.cs`

2. ✅ **修改ServiceConfiguration.cs**
   - 将PLC和串口服务配置分离，保持架构一致性
   - 添加独立的ConfigurePLCServices和ConfigureSerialServices方法
   - 调用专用扩展类注册服务，避免重复注册

3. ✅ **统一依赖注入框架**
   - 将ServiceExtension.cs中的RegisterSingleton调用替换为AddSingleton
   - 将Register调用替换为AddTransient
   - 统一使用Microsoft.Extensions.DependencyInjection框架

4. ✅ **添加服务生命周期验证机制**
   - 创建ServiceLifetimeValidator类，验证关键服务是否正确注册为单例
   - 在ServiceConfiguration.ValidateServiceConfiguration方法中调用验证机制
   - 通过日志记录验证结果，方便问题诊断

5. ✅ **优化目录结构**
   - 创建PLCControl目录，保持与SerialControl目录结构一致
   - 添加Implementation和Interfaces子目录，保持架构对称性
   
#### 第二阶段：日志服务统一

1. ✅ **移除过时的ILogger属性**
   - 删除BasePage中标记为过时的ILogger属性
   - 统一使用ILoggingService接口

2. ✅ **优化日志服务配置**
   - 修改ServiceConfiguration.cs，移除对Microsoft.Extensions.Logging的直接依赖
   - 仅保留自定义日志服务ILoggingService的注册
   - 通过自定义日志服务配置设置日志级别，替代原有的builder.SetMinimumLevel

3. ✅ **扩展日志服务功能**
   - 为LoggingService添加SetMinimumLogLevel方法，支持运行时调整日志级别
   - 为FileLogger添加SetMinimumLogLevel方法，确保日志级别设置在所有记录器中生效
   - 修改LogEntry类，添加Exception属性以支持异常日志记录
   - 优化日志格式，在日志输出中添加异常信息

4. ✅ **日志调用模式统一**
   - 确认项目中使用统一的`Logger?.LogXXX(message, eventId)`格式
   - 移除对Microsoft.Extensions.Logging.ILogger接口的直接使用
   - 修复FTitlePage4和FTitlePage2中混合使用Logger和_loggingService的问题
   - 统一UI页面使用BasePage提供的Logger属性而非直接注入_loggingService

#### 第三阶段：用户上下文替换

1. ✅ **创建IUserContext接口和实现**
   - 创建了IUserContext接口，定义用户上下文管理相关方法
   - 实现了UserContextService类，作为IUserContext的实现
   - 在ServiceConfiguration中将IUserContext注册为单例服务

2. ✅ **在BasePage中注入IUserContext**
   - 在BasePage中添加了UserContext属性，使所有页面都能方便访问用户上下文
   - 所有从BasePage继承的页面都可以直接访问UserContext属性

3. ✅ **替换CurrentUser静态调用**
   - FTitlePage1中替换CurrentUser为UserContext
   - FTitlePage2中替换CurrentUser为UserContext
   - FTitlePage3中替换CurrentUser为UserContext
   - FTitlePage4中替换CurrentUser为UserContext
   - FHeaderMainFooter中替换CurrentUser为IUserContext注入服务
   - FLoginPage中添加_userContext注入并规范化对它的使用
   - FChangePasswordDialog中添加_userContext支持

4. ✅ **完全移除CurrentUser静态类**
   - 将所有页面和服务中的CurrentUser静态调用替换为UserContext或_userContext
   - 移除FLoginPage中对CurrentUser.User的直接设置，完全依赖IUserContext
   - 确认所有代码都不再依赖CurrentUser静态类
   - 删除CurrentUser.cs文件，彻底消除静态依赖
   
5. ✅ **修复UI页面管理问题**
   - 修复了FHeaderMainFooter中的HasNode方法，正确检查节点是否存在
   - 创建了IsPageAdded辅助方法，通过页面Text属性比较检查页面是否已添加
   - 增强了UpdatePagePermissions和FHeaderMainFooter_Load方法，避免重复添加页面
   - 解决了登录后出现"This UIPage is already exists"错误的问题
   - 修复了TabPage和UIPage类型不兼容的问题
   - 确保所有UI页面保持在原有的Sunny.UI.Demo命名空间中

#### 第四阶段：服务获取规范化

1. ✅ **统一服务获取方法**
   - 移除所有对CommonFun.host.Services的直接调用
   - 在页面中统一使用BasePage提供的GetService/GetRequiredService方法
   - 在非页面类中通过构造函数参数注入服务

2. ✅ **规范化构造函数注入**
   - 修改FLoginPage和FChangePasswordDialog，改为构造函数参数注入
   - 修改MainWindowViewModel、ZAxisViewModel、CameralHoldAxisViewModel等，改为构造函数参数注入
   - 修改XyrAxisViewModel、GenericAxis、CommonAxis等，改为构造函数参数注入
   - 修改JsonOperator，改为构造函数参数注入

3. ✅ **调整调用方代码**
   - 修改FHeaderMainFooter中对FChangePasswordDialog的调用，传入所需服务
   - 修改FTitlePage4中对FChangePasswordDialog的调用，传入所需服务
   - 修改Program.cs中对FLoginPage的调用，传入所需服务
   - 确保所有服务获取方式符合规范，消除对CommonFun.host.Services的直接依赖

4. ✅ **修复EventIds缺失问题**
   - 在EventIds.cs中添加ResourceStatus事件ID
   - 更新ResourceManager.LogResourceStatus方法，使用新添加的ResourceStatus事件ID
   - 确保所有日志记录使用正确的事件ID

#### 第五阶段：资源管理优化

1. ✅ **ResourceManager增强**
   - 添加了批量资源注册方法`RegisterResources<T>`，支持一次注册多个资源
   - 添加了资源统计功能`GetStatistics`和`LogResourceStatus`，用于监控资源使用情况
   - 添加了`RemoveResourcesByPrefix`方法，支持按前缀批量移除资源
   - 增强了`BeginShutdownAsync`方法，支持超时参数和取消令牌
   - 添加了CancellationTokenSourceWrapper类，用于统一管理取消令牌
   - 优化了资源清理逻辑，使用并行任务提高效率

2. ✅ **统一Timer管理**
   - 在BasePage中添加了工厂方法`CreateTimer`，统一创建TimerWrapper实例
   - 添加了`CreateTimerWithAsyncHandler`方法，支持处理异步事件处理器
   - 优化了TimerWrapper类，增加状态跟踪和性能监控功能
   - 在ResourceManager中添加了`RegisterTimers`方法，支持批量注册定时器
   - 修改所有页面，用BasePage提供的方法创建和管理定时器

3. ✅ **优化取消令牌管理**
   - 在BasePage中添加了`CreateCancellationTokenSource`工厂方法
   - 添加了`CreateCancellationTokenSource(int timeoutMs)`重载，支持超时
   - 添加了`CreateLinkedCancellationTokenSource`方法，支持连接现有令牌
   - 在ResourceManager中添加了`RegisterCancellationTokenSources`方法，支持批量注册
   - 修改所有页面和轴控制代码，使用BasePage提供的方法创建和管理取消令牌

4. ✅ **轴视图模型资源清理**
   - 为IAxisViewModel接口添加了IDisposable和IAsyncDisposable接口
   - 实现了AxisViewModelBase抽象类中的Dispose()和DisposeAsync()方法
   - 添加了DisposeAsyncCore()方法处理异步资源释放
   - 添加了DisposeManagedResources()方法处理同步资源释放
   - 添加了CancelAllTokenSources()方法取消所有进行中的操作
   - 实现了SerialAxisViewModel的资源清理方法
   - 实现了CameralHoldAxisViewModel和GenericAxis的资源清理

### 9.2 后续计划

接下来将继续实施整改方案中的以下阶段：

1. **第六阶段：测试与验证**
   - 功能测试
   - 性能测试
   - 内存泄漏检查

### 9.3 整改效果初步评估

目前已完成的整改工作初步显示出以下效果：

1. **架构一致性提升**：PLC和串口服务的组织结构更加一致，遵循相同的模式
2. **依赖注入标准化**：统一使用Microsoft.Extensions.DependencyInjection框架，减少混合使用不同框架的问题
3. **日志机制优化**：统一的日志接口和调用模式，提高了系统稳定性和日志记录的可靠性
4. **服务生命周期管理**：通过验证机制确保关键服务的生命周期正确，减少潜在的资源泄漏
5. **用户上下文服务化**：移除对静态CurrentUser类的直接依赖，改为使用可注入的IUserContext服务，大幅提高了代码的可测试性和解耦程度
6. **服务获取规范化**：统一了服务获取方式，消除了对CommonFun.host.Services的直接依赖，提高了代码的可维护性和可测试性
7. **构造函数注入规范化**：对话框、业务类和ViewModel/Axis类均改为构造函数参数注入，明确了依赖关系，便于后续单元测试
8. **资源管理优化**：统一的资源创建和释放机制，减少资源泄漏风险，提高系统稳定性
9. **轴控制资源清理**：实现了完整的资源清理机制，避免长时间运行时的内存泄漏问题

通过以上整改，已经实现了提高系统稳定性、提升代码可维护性和减少资源泄漏的目标，为后续阶段的整改工作奠定了基础。 

### 9.4 第六阶段：测试与验证详细计划

为确保整改后的代码稳定可靠，第六阶段将进行全面的测试与验证，包括功能测试、性能测试和内存泄漏检查。

#### 9.4.1 功能测试计划

##### 基础服务测试
1. **依赖注入框架测试**
   - **测试用例 F001**: 验证关键服务的生命周期
     - 验证方法: 使用ServiceLifetimeValidator验证单例服务是否保持单例
     - 预期结果: 所有标记为单例的服务应当保持单例状态，返回相同的实例引用
   - **测试用例 F002**: 验证服务解析性能
     - 验证方法: 批量请求1000次关键服务，记录平均解析时间
     - 预期结果: 单例服务解析时间应当在1ms以内

##### 用户服务测试
1. **用户上下文服务测试**
   - **测试用例 F101**: 用户登录与权限验证
     - 验证方法: 使用不同角色用户登录，验证权限控制是否正确
     - 预期结果: 管理员应能访问所有页面，操作员应只能访问指定页面
   - **测试用例 F102**: 用户会话持久性
     - 验证方法: 登录后关闭再重新打开特定页面，验证用户会话是否保持
     - 预期结果: 用户登录状态应当保持不变，无需重新登录

##### 轴控制服务测试
1. **轴视图模型测试**
   - **测试用例 F201**: XYR轴功能测试
     - 验证方法: 执行移动、回零、点动等操作，验证轴响应是否正确
     - 预期结果: 所有操作应当成功执行，位置更新准确
   - **测试用例 F202**: 相机轴功能测试
     - 验证方法: 执行相机轴特有操作，如移动到工作位置、安全位置等
     - 预期结果: 所有操作应当成功执行，位置更新准确
   - **测试用例 F203**: 串口轴功能测试
     - 验证方法: 执行串口轴操作，验证通信是否正确
     - 预期结果: 命令应正确发送并接收响应

##### 资源管理测试
1. **资源生命周期测试**
   - **测试用例 F301**: 页面资源释放
     - 验证方法: 打开并关闭多个页面，验证资源是否正确释放
     - 预期结果: 所有注册的资源应当在页面关闭后被释放
   - **测试用例 F302**: 取消令牌测试
     - 验证方法: 执行可取消操作并中途取消，验证操作是否正确终止
     - 预期结果: 所有操作应当在取消后立即终止，不再执行后续步骤

##### 日志服务测试
1. **日志记录测试**
   - **测试用例 F401**: 日志级别过滤
     - 验证方法: 设置不同日志级别，验证日志过滤是否正确
     - 预期结果: 只有高于或等于设置级别的日志应被记录
   - **测试用例 F402**: 异常日志记录
     - 验证方法: 触发异常，验证异常信息是否正确记录
     - 预期结果: 异常日志应包含异常类型、消息和堆栈跟踪

##### 集成测试
1. **系统操作流程测试**
   - **测试用例 F501**: 完整工艺流程
     - 验证方法: 执行完整的标准工艺流程，包括加载参数、轴移动、执行工艺等
     - 预期结果: 所有步骤应顺利执行，无错误或异常

#### 9.4.2 性能测试计划

##### UI响应性能
1. **页面加载性能**
   - **测试用例 P001**: 主页面加载时间
     - 验证方法: 使用PerformanceMonitor记录主页面加载时间
     - 预期结果: 页面加载时间应小于500ms
   - **测试用例 P002**: 标签页切换性能
     - 验证方法: 使用PerformanceMonitor记录标签页切换时间
     - 预期结果: 标签页切换时间应小于300ms

##### 轴操作性能
1. **轴命令响应时间**
   - **测试用例 P101**: 轴移动命令响应
     - 验证方法: 记录发送命令到轴开始移动的时间
     - 预期结果: 响应时间应小于100ms
   - **测试用例 P102**: 轴状态查询性能
     - 验证方法: 批量执行100次轴状态查询，记录平均响应时间
     - 预期结果: 平均响应时间应小于50ms

##### 资源管理性能
1. **资源创建与释放性能**
   - **测试用例 P201**: 定时器创建性能
     - 验证方法: 批量创建100个定时器，记录总时间
     - 预期结果: 创建时间应小于500ms
   - **测试用例 P202**: 资源释放性能
     - 验证方法: 释放包含100个资源的页面，记录释放时间
     - 预期结果: 释放时间应小于1秒

##### 日志记录性能
1. **高负载日志记录**
   - **测试用例 P301**: 批量日志记录
     - 验证方法: 在1秒内记录1000条日志，监测系统响应性
     - 预期结果: 系统应保持响应，UI不应冻结

#### 9.4.3 内存泄漏检查计划

##### 静态分析
1. **代码静态分析**
   - **测试用例 M001**: IDisposable实现检查
     - 验证方法: 使用代码分析工具检查所有实现IDisposable的类是否正确释放资源
     - 预期结果: 所有类应正确实现Dispose模式

##### 动态分析
1. **长时间运行测试**
   - **测试用例 M101**: 24小时稳定性测试
     - 验证方法: 系统持续运行24小时，定期执行标准操作，监控内存使用
     - 预期结果: 内存使用应保持稳定，不应持续增长
   - **测试用例 M102**: 资源使用监控
     - 验证方法: 使用ResourceManager.GetStatistics监控资源数量
     - 预期结果: 资源数量应随页面打开关闭变化，但长期趋势应保持稳定

2. **压力测试**
   - **测试用例 M201**: UI压力测试
     - 验证方法: 快速频繁切换页面1000次，监控内存使用
     - 预期结果: 内存使用应在测试后恢复到接近初始水平
   - **测试用例 M202**: 轴操作压力测试
     - 验证方法: 连续执行1000次轴移动操作，监控内存使用
     - 预期结果: 内存使用应在测试后恢复到接近初始水平

#### 9.4.4 实施方法与工具

1. **测试环境**
   - 开发环境测试: 使用Visual Studio 2022内置诊断工具
   - 生产环境模拟测试: 使用与生产环境相同配置的测试机

2. **测试工具**
   - **功能测试**: 自定义测试页面，记录执行结果
   - **性能测试**: 
     - Visual Studio性能分析器
     - 自定义PerformanceMonitor类记录关键指标
   - **内存泄漏检查**:
     - Visual Studio内存分析器
     - DotTrace Memory Profiler
     - ResourceManager统计功能

3. **测试报告模板**
   ```
   测试用例ID: [ID]
   测试类型: [功能/性能/内存]
   测试项目: [项目名称]
   测试方法: [详细步骤]
   预期结果: [期望输出]
   实际结果: [实际输出]
   通过/失败: [通过/失败]
   备注: [额外说明]
   ```

#### 9.4.5 验收标准

1. **功能验收标准**
   - 所有功能测试用例通过率达到100%
   - 无功能性回归问题

2. **性能验收标准**
   - UI响应时间符合预期目标
   - 轴操作响应时间符合预期目标
   - 资源管理性能符合预期目标

3. **内存管理验收标准**
   - 24小时稳定性测试内存增长不超过初始值的20%
   - 压力测试后内存恢复到初始值的120%以内

#### 9.4.6 测试排期

1. **第1周: 测试准备与功能测试**
   - 周一至周二: 准备测试环境与工具
   - 周三至周五: 执行功能测试用例

2. **第2周: 性能与内存测试**
   - 周一至周二: 执行性能测试用例
   - 周三至周五: 执行内存泄漏测试用例

3. **第3周: 问题修复与回归测试**
   - 周一至周三: 修复测试中发现的问题
   - 周四至周五: 执行回归测试

#### 9.4.7 风险管理

1. **已识别风险**
   - **风险R1**: 测试覆盖不全面导致线上问题
     - 缓解措施: 建立完善的回归测试机制，确保关键功能全覆盖
   - **风险R2**: 性能测试结果受测试环境影响
     - 缓解措施: 在多种环境下进行测试，建立基线数据
   - **风险R3**: 内存泄漏难以在短期测试中发现
     - 缓解措施: 结合静态分析和长时间运行测试，增加监控频率

2. **应急计划**
   - 建立回滚机制，确保发现严重问题时可迅速恢复
   - 准备热修复方案，针对关键问题可快速部署修复 